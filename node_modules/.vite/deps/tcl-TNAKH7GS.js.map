{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/tcl/tcl.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/tcl/tcl.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  tokenPostfix: \".tcl\",\n  specialFunctions: [\n    \"set\",\n    \"unset\",\n    \"rename\",\n    \"variable\",\n    \"proc\",\n    \"coroutine\",\n    \"foreach\",\n    \"incr\",\n    \"append\",\n    \"lappend\",\n    \"linsert\",\n    \"lreplace\"\n  ],\n  mainFunctions: [\n    \"if\",\n    \"then\",\n    \"elseif\",\n    \"else\",\n    \"case\",\n    \"switch\",\n    \"while\",\n    \"for\",\n    \"break\",\n    \"continue\",\n    \"return\",\n    \"package\",\n    \"namespace\",\n    \"catch\",\n    \"exit\",\n    \"eval\",\n    \"expr\",\n    \"uplevel\",\n    \"upvar\"\n  ],\n  builtinFunctions: [\n    \"file\",\n    \"info\",\n    \"concat\",\n    \"join\",\n    \"lindex\",\n    \"list\",\n    \"llength\",\n    \"lrange\",\n    \"lsearch\",\n    \"lsort\",\n    \"split\",\n    \"array\",\n    \"parray\",\n    \"binary\",\n    \"format\",\n    \"regexp\",\n    \"regsub\",\n    \"scan\",\n    \"string\",\n    \"subst\",\n    \"dict\",\n    \"cd\",\n    \"clock\",\n    \"exec\",\n    \"glob\",\n    \"pid\",\n    \"pwd\",\n    \"close\",\n    \"eof\",\n    \"fblocked\",\n    \"fconfigure\",\n    \"fcopy\",\n    \"fileevent\",\n    \"flush\",\n    \"gets\",\n    \"open\",\n    \"puts\",\n    \"read\",\n    \"seek\",\n    \"socket\",\n    \"tell\",\n    \"interp\",\n    \"after\",\n    \"auto_execok\",\n    \"auto_load\",\n    \"auto_mkindex\",\n    \"auto_reset\",\n    \"bgerror\",\n    \"error\",\n    \"global\",\n    \"history\",\n    \"load\",\n    \"source\",\n    \"time\",\n    \"trace\",\n    \"unknown\",\n    \"unset\",\n    \"update\",\n    \"vwait\",\n    \"winfo\",\n    \"wm\",\n    \"bind\",\n    \"event\",\n    \"pack\",\n    \"place\",\n    \"grid\",\n    \"font\",\n    \"bell\",\n    \"clipboard\",\n    \"destroy\",\n    \"focus\",\n    \"grab\",\n    \"lower\",\n    \"option\",\n    \"raise\",\n    \"selection\",\n    \"send\",\n    \"tk\",\n    \"tkwait\",\n    \"tk_bisque\",\n    \"tk_focusNext\",\n    \"tk_focusPrev\",\n    \"tk_focusFollowsMouse\",\n    \"tk_popup\",\n    \"tk_setPalette\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"'\\[\\]\\{\\};\\$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  variables: /(?:\\$+(?:(?:\\:\\:?)?[a-zA-Z_]\\w*)+)/,\n  tokenizer: {\n    root: [\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@specialFunctions\": {\n              token: \"keyword.flow\",\n              next: \"@specialFunc\"\n            },\n            \"@mainFunctions\": \"keyword\",\n            \"@builtinFunctions\": \"variable\",\n            \"@default\": \"operator.scss\"\n          }\n        }\n      ],\n      [/\\s+\\-+(?!\\d|\\.)\\w*|{\\*}/, \"metatag\"],\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"operator\"],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/\\.(?!\\d|\\.)[\\w\\-]*/, \"operator.sql\"],\n      [/\\d+(\\.\\d+)?/, \"number\"],\n      [/\\d+/, \"number\"],\n      [/;/, \"delimiter\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@dstring\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }]\n    ],\n    dstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    sstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/#.*\\\\$/, { token: \"comment\", next: \"@newlineComment\" }],\n      [/#.*(?!\\\\)$/, \"comment\"]\n    ],\n    newlineComment: [\n      [/.*\\\\$/, \"comment\"],\n      [/.*(?!\\\\)$/, { token: \"comment\", next: \"@pop\" }]\n    ],\n    nestedVariable: [\n      [/[^\\{\\}\\$]+/, \"type.identifier\"],\n      [/\\}/, { token: \"identifier\", next: \"@pop\" }]\n    ],\n    nestedCall: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\]/, { token: \"@brackets\", next: \"@pop\" }],\n      { include: \"root\" }\n    ],\n    specialFunc: [\n      [/\"/, { token: \"string\", next: \"@dstring\" }],\n      [/'/, { token: \"string\", next: \"@sstring\" }],\n      [/\\S+/, { token: \"type\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,eAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,EACrD;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,IACT,MAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,qBAAqB;AAAA,cACnB,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,kBAAkB;AAAA,YAClB,qBAAqB;AAAA,YACrB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,2BAA2B,SAAS;AAAA,MACrC,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,UAAU;AAAA,MACvB,CAAC,kBAAkB,EAAE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAAA,MACnE,CAAC,cAAc,iBAAiB;AAAA,MAChC,CAAC,sBAAsB,cAAc;AAAA,MACrC,CAAC,eAAe,QAAQ;AAAA,MACxB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,MACnE,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,IACrE;AAAA,IACA,SAAS;AAAA,MACP,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,cAAc,CAAC;AAAA,MAClD,CAAC,kBAAkB,EAAE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAAA,MACnE,CAAC,cAAc,iBAAiB;AAAA,MAChC,CAAC,gBAAgB,QAAQ;AAAA,MACzB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,SAAS;AAAA,MACP,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,cAAc,CAAC;AAAA,MAClD,CAAC,kBAAkB,EAAE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAAA,MACnE,CAAC,cAAc,iBAAiB;AAAA,MAChC,CAAC,gBAAgB,QAAQ;AAAA,MACzB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,UAAU,EAAE,OAAO,WAAW,MAAM,kBAAkB,CAAC;AAAA,MACxD,CAAC,cAAc,SAAS;AAAA,IAC1B;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,aAAa,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,IAClD;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,cAAc,iBAAiB;AAAA,MAChC,CAAC,MAAM,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,IAC9C;AAAA,IACA,YAAY;AAAA,MACV,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,cAAc,CAAC;AAAA,MAClD,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC;AAAA,MAC3C,EAAE,SAAS,OAAO;AAAA,IACpB;AAAA,IACA,aAAa;AAAA,MACX,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,WAAW,CAAC;AAAA,MAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,WAAW,CAAC;AAAA,MAC3C,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,EACF;AACF;", "names": []}