{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/razor/razor.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/razor/razor.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"<\", close: \">\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(`<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(`<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  tokenizer: {\n    root: [\n      [/@@@@/],\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.root\" }],\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)([\\w\\-]+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)([:\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)([\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      [/[^<@]+/]\n    ],\n    doctype: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.comment\" }],\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.comment\" }],\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.otherTag\" }],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n    ],\n    script: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.script\" }],\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    scriptAfterType: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.scriptAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptAfterTypeEquals: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.scriptAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptWithCustomType: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.scriptWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInEmbeddedState.scriptEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    style: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.style\" }],\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    styleAfterType: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.styleAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleAfterTypeEquals: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.styleAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleWithCustomType: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.styleWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInEmbeddedState.styleEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    razorInSimpleState: [\n      [/@\\*/, \"comment.cs\", \"@razorBlockCommentTopLevel\"],\n      [/@[{(]/, \"metatag.cs\", \"@razorRootTopLevel\"],\n      [/(@)(\\s*[\\w]+)/, [\"metatag.cs\", { token: \"identifier.cs\", switchTo: \"@$S2.$S3\" }]],\n      [/[})]/, { token: \"metatag.cs\", switchTo: \"@$S2.$S3\" }],\n      [/\\*@/, { token: \"comment.cs\", switchTo: \"@$S2.$S3\" }]\n    ],\n    razorInEmbeddedState: [\n      [/@\\*/, \"comment.cs\", \"@razorBlockCommentTopLevel\"],\n      [/@[{(]/, \"metatag.cs\", \"@razorRootTopLevel\"],\n      [\n        /(@)(\\s*[\\w]+)/,\n        [\n          \"metatag.cs\",\n          {\n            token: \"identifier.cs\",\n            switchTo: \"@$S2.$S3\",\n            nextEmbedded: \"$S3\"\n          }\n        ]\n      ],\n      [\n        /[})]/,\n        {\n          token: \"metatag.cs\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ],\n      [\n        /\\*@/,\n        {\n          token: \"comment.cs\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ]\n    ],\n    razorBlockCommentTopLevel: [\n      [/\\*@/, \"@rematch\", \"@pop\"],\n      [/[^*]+/, \"comment.cs\"],\n      [/./, \"comment.cs\"]\n    ],\n    razorBlockComment: [\n      [/\\*@/, \"comment.cs\", \"@pop\"],\n      [/[^*]+/, \"comment.cs\"],\n      [/./, \"comment.cs\"]\n    ],\n    razorRootTopLevel: [\n      [/\\{/, \"delimiter.bracket.cs\", \"@razorRoot\"],\n      [/\\(/, \"delimiter.parenthesis.cs\", \"@razorRoot\"],\n      [/[})]/, \"@rematch\", \"@pop\"],\n      { include: \"razorCommon\" }\n    ],\n    razorRoot: [\n      [/\\{/, \"delimiter.bracket.cs\", \"@razorRoot\"],\n      [/\\(/, \"delimiter.parenthesis.cs\", \"@razorRoot\"],\n      [/\\}/, \"delimiter.bracket.cs\", \"@pop\"],\n      [/\\)/, \"delimiter.parenthesis.cs\", \"@pop\"],\n      { include: \"razorCommon\" }\n    ],\n    razorCommon: [\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@razorKeywords\": { token: \"keyword.cs\" },\n            \"@default\": \"identifier.cs\"\n          }\n        }\n      ],\n      [/[\\[\\]]/, \"delimiter.array.cs\"],\n      [/[ \\t\\r\\n]+/],\n      [/\\/\\/.*$/, \"comment.cs\"],\n      [/@\\*/, \"comment.cs\", \"@razorBlockComment\"],\n      [/\"([^\"]*)\"/, \"string.cs\"],\n      [/'([^']*)'/, \"string.cs\"],\n      [/(<)([\\w\\-]+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)([\\w\\-]+)(>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<\\/)([\\w\\-]+)(>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/[\\+\\-\\*\\%\\&\\|\\^\\~\\!\\=\\<\\>\\/\\?\\;\\:\\.\\,]/, \"delimiter.cs\"],\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float.cs\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float.cs\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F]/, \"number.hex.cs\"],\n      [/0[0-7']*[0-7]/, \"number.octal.cs\"],\n      [/0[bB][0-1']*[0-1]/, \"number.binary.cs\"],\n      [/\\d[\\d']*/, \"number.cs\"],\n      [/\\d/, \"number.cs\"]\n    ]\n  },\n  razorKeywords: [\n    \"abstract\",\n    \"as\",\n    \"async\",\n    \"await\",\n    \"base\",\n    \"bool\",\n    \"break\",\n    \"by\",\n    \"byte\",\n    \"case\",\n    \"catch\",\n    \"char\",\n    \"checked\",\n    \"class\",\n    \"const\",\n    \"continue\",\n    \"decimal\",\n    \"default\",\n    \"delegate\",\n    \"do\",\n    \"double\",\n    \"descending\",\n    \"explicit\",\n    \"event\",\n    \"extern\",\n    \"else\",\n    \"enum\",\n    \"false\",\n    \"finally\",\n    \"fixed\",\n    \"float\",\n    \"for\",\n    \"foreach\",\n    \"from\",\n    \"goto\",\n    \"group\",\n    \"if\",\n    \"implicit\",\n    \"in\",\n    \"int\",\n    \"interface\",\n    \"internal\",\n    \"into\",\n    \"is\",\n    \"lock\",\n    \"long\",\n    \"nameof\",\n    \"new\",\n    \"null\",\n    \"namespace\",\n    \"object\",\n    \"operator\",\n    \"out\",\n    \"override\",\n    \"orderby\",\n    \"params\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"readonly\",\n    \"ref\",\n    \"return\",\n    \"switch\",\n    \"struct\",\n    \"sbyte\",\n    \"sealed\",\n    \"short\",\n    \"sizeof\",\n    \"stackalloc\",\n    \"static\",\n    \"string\",\n    \"select\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"typeof\",\n    \"uint\",\n    \"ulong\",\n    \"unchecked\",\n    \"unsafe\",\n    \"ushort\",\n    \"using\",\n    \"var\",\n    \"virtual\",\n    \"volatile\",\n    \"void\",\n    \"when\",\n    \"while\",\n    \"where\",\n    \"yield\",\n    \"model\",\n    \"inject\"\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,cAAc,CAAC,QAAQ,KAAK;AAAA,EAC9B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,QAAQ,KAAK;AAAA,IACd,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,MACE,YAAY,IAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,wCAAwC,GAAG;AAAA,MACpG,WAAW;AAAA,MACX,QAAQ;AAAA,QACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,MAClE;AAAA,IACF;AAAA,IACA;AAAA,MACE,YAAY,IAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,wCAAwC,GAAG;AAAA,MACpG,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,IACnF;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,MAAM;AAAA,MACP,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,2BAA2B,CAAC;AAAA,MACrE,CAAC,aAAa,gBAAgB,UAAU;AAAA,MACxC,CAAC,QAAQ,gBAAgB,UAAU;AAAA,MACnC,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MACtE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC;AAAA,MAC1E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,SAAS,CAAC,CAAC;AAAA,MACxE,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC9E,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC/E,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,YAAY;AAAA,MACb,CAAC,QAAQ;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,8BAA8B,CAAC;AAAA,MACxE,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,gBAAgB,MAAM;AAAA,IAC9B;AAAA,IACA,SAAS;AAAA,MACP,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,8BAA8B,CAAC;AAAA,MACxE,CAAC,OAAO,gBAAgB,MAAM;AAAA,MAC9B,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,sBAAsB;AAAA,IAC9B;AAAA,IACA,UAAU;AAAA,MACR,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,+BAA+B,CAAC;AAAA,MACzE,CAAC,QAAQ,kBAAkB,MAAM;AAAA,MACjC,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,6BAA6B,CAAC;AAAA,MACvE,CAAC,QAAQ,kBAAkB,kBAAkB;AAAA,MAC7C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACE;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,KAAK,aAAa,wBAAwB;AAAA,MAC3C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACvD;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACvD;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACvD;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IACzE;AAAA,IACA,OAAO;AAAA,MACL,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,4BAA4B,CAAC;AAAA,MACtE,CAAC,QAAQ,kBAAkB,iBAAiB;AAAA,MAC5C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACE;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,KAAK,aAAa,uBAAuB;AAAA,MAC1C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACtD;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACtD;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACtD;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IACxE;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,OAAO,cAAc,4BAA4B;AAAA,MAClD,CAAC,SAAS,cAAc,oBAAoB;AAAA,MAC5C,CAAC,iBAAiB,CAAC,cAAc,EAAE,OAAO,iBAAiB,UAAU,WAAW,CAAC,CAAC;AAAA,MAClF,CAAC,QAAQ,EAAE,OAAO,cAAc,UAAU,WAAW,CAAC;AAAA,MACtD,CAAC,OAAO,EAAE,OAAO,cAAc,UAAU,WAAW,CAAC;AAAA,IACvD;AAAA,IACA,sBAAsB;AAAA,MACpB,CAAC,OAAO,cAAc,4BAA4B;AAAA,MAClD,CAAC,SAAS,cAAc,oBAAoB;AAAA,MAC5C;AAAA,QACE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,YACV,cAAc;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB,CAAC,OAAO,YAAY,MAAM;AAAA,MAC1B,CAAC,SAAS,YAAY;AAAA,MACtB,CAAC,KAAK,YAAY;AAAA,IACpB;AAAA,IACA,mBAAmB;AAAA,MACjB,CAAC,OAAO,cAAc,MAAM;AAAA,MAC5B,CAAC,SAAS,YAAY;AAAA,MACtB,CAAC,KAAK,YAAY;AAAA,IACpB;AAAA,IACA,mBAAmB;AAAA,MACjB,CAAC,MAAM,wBAAwB,YAAY;AAAA,MAC3C,CAAC,MAAM,4BAA4B,YAAY;AAAA,MAC/C,CAAC,QAAQ,YAAY,MAAM;AAAA,MAC3B,EAAE,SAAS,cAAc;AAAA,IAC3B;AAAA,IACA,WAAW;AAAA,MACT,CAAC,MAAM,wBAAwB,YAAY;AAAA,MAC3C,CAAC,MAAM,4BAA4B,YAAY;AAAA,MAC/C,CAAC,MAAM,wBAAwB,MAAM;AAAA,MACrC,CAAC,MAAM,4BAA4B,MAAM;AAAA,MACzC,EAAE,SAAS,cAAc;AAAA,IAC3B;AAAA,IACA,aAAa;AAAA,MACX;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,kBAAkB,EAAE,OAAO,aAAa;AAAA,YACxC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,UAAU,oBAAoB;AAAA,MAC/B,CAAC,YAAY;AAAA,MACb,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,OAAO,cAAc,oBAAoB;AAAA,MAC1C,CAAC,aAAa,WAAW;AAAA,MACzB,CAAC,aAAa,WAAW;AAAA,MACzB,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MACtE,CAAC,mBAAmB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MACpE,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MACtE,CAAC,0CAA0C,cAAc;AAAA,MACzD,CAAC,0BAA0B,iBAAiB;AAAA,MAC5C,CAAC,4BAA4B,iBAAiB;AAAA,MAC9C,CAAC,iCAAiC,eAAe;AAAA,MACjD,CAAC,iBAAiB,iBAAiB;AAAA,MACnC,CAAC,qBAAqB,kBAAkB;AAAA,MACxC,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,MAAM,WAAW;AAAA,IACpB;AAAA,EACF;AAAA,EACA,eAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AACX;", "names": []}