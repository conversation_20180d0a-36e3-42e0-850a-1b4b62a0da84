{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/mdx/mdx.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/mdx/mdx.ts\nvar conf = {\n  comments: {\n    blockComment: [\"{/*\", \"*/}\"]\n  },\n  brackets: [[\"{\", \"}\"]],\n  autoClosingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"\\u201C\", close: \"\\u201D\" },\n    { open: \"\\u2018\", close: \"\\u2019\" },\n    { open: \"`\", close: \"`\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"_\", close: \"_\" },\n    { open: \"**\", close: \"**\" },\n    { open: \"<\", close: \">\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: /^\\s*- .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"- \" }\n    },\n    {\n      beforeText: /^\\s*\\+ .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"+ \" }\n    },\n    {\n      beforeText: /^\\s*\\* .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"* \" }\n    },\n    {\n      beforeText: /^> /,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"> \" }\n    },\n    {\n      beforeText: /<\\w+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /\\s+>\\s*$/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /<\\/\\w+>/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Outdent }\n    },\n    ...Array.from({ length: 100 }, (_, index) => ({\n      beforeText: new RegExp(`^${index}\\\\. .+`),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: `${index + 1}. ` }\n    }))\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".mdx\",\n  control: /[!#()*+.[\\\\\\]_`{}\\-]/,\n  escapes: /\\\\@control/,\n  tokenizer: {\n    root: [\n      [/^---$/, { token: \"meta.content\", next: \"@frontmatter\", nextEmbedded: \"yaml\" }],\n      [/^\\s*import/, { token: \"keyword\", next: \"@import\", nextEmbedded: \"js\" }],\n      [/^\\s*export/, { token: \"keyword\", next: \"@export\", nextEmbedded: \"js\" }],\n      [/<\\w+/, { token: \"type.identifier\", next: \"@jsx\" }],\n      [/<\\/?\\w+>/, \"type.identifier\"],\n      [\n        /^(\\s*)(>*\\s*)(#{1,6}\\s)/,\n        [{ token: \"white\" }, { token: \"comment\" }, { token: \"keyword\", next: \"@header\" }]\n      ],\n      [/^(\\s*)(>*\\s*)([*+-])(\\s+)/, [\"white\", \"comment\", \"keyword\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(-{3,}|\\*{3,}|_{3,})$/, [\"white\", \"comment\", \"keyword\"]],\n      [/`{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_backtick\" }],\n      [/~{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_tilde\" }],\n      [\n        /`{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_backtick\", nextEmbedded: \"$1\" }\n      ],\n      [\n        /~{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_tilde\", nextEmbedded: \"$1\" }\n      ],\n      [/^(\\s*)(-{4,})$/, [\"white\", \"comment\"]],\n      [/^(\\s*)(>+)/, [\"white\", \"comment\"]],\n      { include: \"content\" }\n    ],\n    content: [\n      [\n        /(\\[)(.+)(]\\()(.+)(\\s+\".*\")(\\))/,\n        [\"\", \"string.link\", \"\", \"type.identifier\", \"string.link\", \"\"]\n      ],\n      [/(\\[)(.+)(]\\()(.+)(\\))/, [\"\", \"type.identifier\", \"\", \"string.link\", \"\"]],\n      [/(\\[)(.+)(]\\[)(.+)(])/, [\"\", \"type.identifier\", \"\", \"type.identifier\", \"\"]],\n      [/(\\[)(.+)(]:\\s+)(\\S*)/, [\"\", \"type.identifier\", \"\", \"string.link\"]],\n      [/(\\[)(.+)(])/, [\"\", \"type.identifier\", \"\"]],\n      [/`.*`/, \"variable.source\"],\n      [/_/, { token: \"emphasis\", next: \"@emphasis_underscore\" }],\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@emphasis_asterisk\" }],\n      [/\\*\\*/, { token: \"strong\", next: \"@strong\" }],\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }]\n    ],\n    import: [[/'\\s*(;|$)/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    expression: [\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\" }],\n      [/}/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    export: [[/^\\s*$/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    jsx: [\n      [/\\s+/, \"\"],\n      [/(\\w+)(=)(\"(?:[^\"\\\\]|\\\\.)*\")/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+)(=)('(?:[^'\\\\]|\\\\.)*')/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+(?=\\s|>|={|$))/, [\"attribute.name\"]],\n      [/={/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }],\n      [/>/, { token: \"type.identifier\", next: \"@pop\" }]\n    ],\n    header: [\n      [/.$/, { token: \"keyword\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"keyword\" }]\n    ],\n    strong: [\n      [/\\*\\*/, { token: \"strong\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"strong\" }]\n    ],\n    emphasis_underscore: [\n      [/_/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    emphasis_asterisk: [\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    frontmatter: [[/^---$/, { token: \"meta.content\", nextEmbedded: \"@pop\", next: \"@pop\" }]],\n    codeblock_highlight_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_highlight_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,cAAc,CAAC,OAAO,KAAK;AAAA,EAC7B;AAAA,EACA,UAAU,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA,EACrB,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAU,OAAO,IAAS;AAAA,IAClC,EAAE,MAAM,KAAU,OAAO,IAAS;AAAA,IAClC,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,IAC1B,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,MACE,YAAY;AAAA,MACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,KAAK;AAAA,IACnG;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,KAAK;AAAA,IACnG;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,KAAK;AAAA,IACnG;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,KAAK;AAAA,IACnG;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,IACnF;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,IACnF;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,QAAQ;AAAA,IACpF;AAAA,IACA,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,GAAG,CAAC,GAAG,WAAW;AAAA,MAC5C,YAAY,IAAI,OAAO,IAAI,KAAK,QAAQ;AAAA,MACxC,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK;AAAA,IAC/G,EAAE;AAAA,EACJ;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,SAAS,EAAE,OAAO,gBAAgB,MAAM,gBAAgB,cAAc,OAAO,CAAC;AAAA,MAC/E,CAAC,cAAc,EAAE,OAAO,WAAW,MAAM,WAAW,cAAc,KAAK,CAAC;AAAA,MACxE,CAAC,cAAc,EAAE,OAAO,WAAW,MAAM,WAAW,cAAc,KAAK,CAAC;AAAA,MACxE,CAAC,QAAQ,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,MACnD,CAAC,YAAY,iBAAiB;AAAA,MAC9B;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,MAClF;AAAA,MACA,CAAC,6BAA6B,CAAC,SAAS,WAAW,WAAW,OAAO,CAAC;AAAA,MACtE,CAAC,iCAAiC,CAAC,SAAS,WAAW,UAAU,OAAO,CAAC;AAAA,MACzE,CAAC,iCAAiC,CAAC,SAAS,WAAW,UAAU,OAAO,CAAC;AAAA,MACzE,CAAC,sCAAsC,CAAC,SAAS,WAAW,SAAS,CAAC;AAAA,MACtE,CAAC,iBAAiB,EAAE,OAAO,UAAU,MAAM,sBAAsB,CAAC;AAAA,MAClE,CAAC,iBAAiB,EAAE,OAAO,UAAU,MAAM,mBAAmB,CAAC;AAAA,MAC/D;AAAA,QACE;AAAA,QACA,EAAE,OAAO,UAAU,MAAM,iCAAiC,cAAc,KAAK;AAAA,MAC/E;AAAA,MACA;AAAA,QACE;AAAA,QACA,EAAE,OAAO,UAAU,MAAM,8BAA8B,cAAc,KAAK;AAAA,MAC5E;AAAA,MACA,CAAC,kBAAkB,CAAC,SAAS,SAAS,CAAC;AAAA,MACvC,CAAC,cAAc,CAAC,SAAS,SAAS,CAAC;AAAA,MACnC,EAAE,SAAS,UAAU;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE;AAAA,QACA,CAAC,IAAI,eAAe,IAAI,mBAAmB,eAAe,EAAE;AAAA,MAC9D;AAAA,MACA,CAAC,yBAAyB,CAAC,IAAI,mBAAmB,IAAI,eAAe,EAAE,CAAC;AAAA,MACxE,CAAC,wBAAwB,CAAC,IAAI,mBAAmB,IAAI,mBAAmB,EAAE,CAAC;AAAA,MAC3E,CAAC,wBAAwB,CAAC,IAAI,mBAAmB,IAAI,aAAa,CAAC;AAAA,MACnE,CAAC,eAAe,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MAC3C,CAAC,QAAQ,iBAAiB;AAAA,MAC1B,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,uBAAuB,CAAC;AAAA,MACzD,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,qBAAqB,CAAC;AAAA,MAC9D,CAAC,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,MAC7C,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,eAAe,cAAc,KAAK,CAAC;AAAA,IAC/E;AAAA,IACA,QAAQ,CAAC,CAAC,aAAa,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC,CAAC;AAAA,IAC/E,YAAY;AAAA,MACV,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,cAAc,CAAC;AAAA,MACzD,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IAC1E;AAAA,IACA,QAAQ,CAAC,CAAC,SAAS,EAAE,OAAO,qBAAqB,MAAM,QAAQ,cAAc,OAAO,CAAC,CAAC;AAAA,IACtF,KAAK;AAAA,MACH,CAAC,OAAO,EAAE;AAAA,MACV,CAAC,+BAA+B,CAAC,kBAAkB,YAAY,QAAQ,CAAC;AAAA,MACxE,CAAC,+BAA+B,CAAC,kBAAkB,YAAY,QAAQ,CAAC;AAAA,MACxE,CAAC,sBAAsB,CAAC,gBAAgB,CAAC;AAAA,MACzC,CAAC,MAAM,EAAE,OAAO,qBAAqB,MAAM,eAAe,cAAc,KAAK,CAAC;AAAA,MAC9E,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IAClD;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,MAAM,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,MACzC,EAAE,SAAS,UAAU;AAAA,MACrB,CAAC,KAAK,EAAE,OAAO,UAAU,CAAC;AAAA,IAC5B;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MAC1C,EAAE,SAAS,UAAU;AAAA,MACrB,CAAC,KAAK,EAAE,OAAO,SAAS,CAAC;AAAA,IAC3B;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,MACzC,EAAE,SAAS,UAAU;AAAA,MACrB,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC;AAAA,IAC7B;AAAA,IACA,mBAAmB;AAAA,MACjB,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,MAChD,EAAE,SAAS,UAAU;AAAA,MACrB,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC;AAAA,IAC7B;AAAA,IACA,aAAa,CAAC,CAAC,SAAS,EAAE,OAAO,gBAAgB,cAAc,QAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,IACtF,8BAA8B;AAAA,MAC5B,CAAC,gBAAgB,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MACxE,CAAC,OAAO,iBAAiB;AAAA,IAC3B;AAAA,IACA,2BAA2B;AAAA,MACzB,CAAC,gBAAgB,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MACxE,CAAC,OAAO,iBAAiB;AAAA,IAC3B;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,gBAAgB,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MAClD,CAAC,OAAO,iBAAiB;AAAA,IAC3B;AAAA,IACA,iBAAiB;AAAA,MACf,CAAC,gBAAgB,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MAClD,CAAC,OAAO,iBAAiB;AAAA,IAC3B;AAAA,EACF;AACF;", "names": []}