{"version": 3, "sources": ["../../monaco-editor/esm/vs/language/json/jsonMode.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/json/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  _defaults;\n  _idleCheckInterval;\n  _lastUsedTime;\n  _configChangeListener;\n  _worker;\n  _client;\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        moduleId: \"vs/language/json/jsonWorker\",\n        label: this._defaults.languageId,\n        createData: {\n          languageSettings: this._defaults.diagnosticsOptions,\n          languageId: this._defaults.languageId,\n          enableSchemaRequest: this._defaults.diagnosticsOptions.enableSchemaRequest\n        }\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(\"Range#create called with invalid arguments[\" + one + \", \" + two + \", \" + three + \", \" + four + \"]\");\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && (Range.is(candidate.targetSelectionRange) || Is.undefined(candidate.targetSelectionRange)) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2[\"Comment\"] = \"comment\";\n  FoldingRangeKind2[\"Imports\"] = \"imports\";\n  FoldingRangeKind2[\"Region\"] = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind) {\n    var result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    var candidate = value;\n    return candidate !== void 0 && candidate !== null && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    var result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      args[_i - 2] = arguments[_i];\n    }\n    var result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    var candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    var result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate !== void 0 && Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    var candidate = value;\n    return typeof candidate === \"string\";\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    var candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    var result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    var result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    var result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    var candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every(function(change) {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextEditChangeImpl = function() {\n  function TextEditChangeImpl2(edits, changeAnnotations) {\n    this.edits = edits;\n    this.changeAnnotations = changeAnnotations;\n  }\n  TextEditChangeImpl2.prototype.insert = function(position, newText, annotation) {\n    var edit;\n    var id;\n    if (annotation === void 0) {\n      edit = TextEdit.insert(position, newText);\n    } else if (ChangeAnnotationIdentifier.is(annotation)) {\n      id = annotation;\n      edit = AnnotatedTextEdit.insert(position, newText, annotation);\n    } else {\n      this.assertChangeAnnotations(this.changeAnnotations);\n      id = this.changeAnnotations.manage(annotation);\n      edit = AnnotatedTextEdit.insert(position, newText, id);\n    }\n    this.edits.push(edit);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  TextEditChangeImpl2.prototype.replace = function(range, newText, annotation) {\n    var edit;\n    var id;\n    if (annotation === void 0) {\n      edit = TextEdit.replace(range, newText);\n    } else if (ChangeAnnotationIdentifier.is(annotation)) {\n      id = annotation;\n      edit = AnnotatedTextEdit.replace(range, newText, annotation);\n    } else {\n      this.assertChangeAnnotations(this.changeAnnotations);\n      id = this.changeAnnotations.manage(annotation);\n      edit = AnnotatedTextEdit.replace(range, newText, id);\n    }\n    this.edits.push(edit);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  TextEditChangeImpl2.prototype.delete = function(range, annotation) {\n    var edit;\n    var id;\n    if (annotation === void 0) {\n      edit = TextEdit.del(range);\n    } else if (ChangeAnnotationIdentifier.is(annotation)) {\n      id = annotation;\n      edit = AnnotatedTextEdit.del(range, annotation);\n    } else {\n      this.assertChangeAnnotations(this.changeAnnotations);\n      id = this.changeAnnotations.manage(annotation);\n      edit = AnnotatedTextEdit.del(range, id);\n    }\n    this.edits.push(edit);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  TextEditChangeImpl2.prototype.add = function(edit) {\n    this.edits.push(edit);\n  };\n  TextEditChangeImpl2.prototype.all = function() {\n    return this.edits;\n  };\n  TextEditChangeImpl2.prototype.clear = function() {\n    this.edits.splice(0, this.edits.length);\n  };\n  TextEditChangeImpl2.prototype.assertChangeAnnotations = function(value) {\n    if (value === void 0) {\n      throw new Error(\"Text edit change is not configured to manage change annotations.\");\n    }\n  };\n  return TextEditChangeImpl2;\n}();\nvar ChangeAnnotations = function() {\n  function ChangeAnnotations2(annotations) {\n    this._annotations = annotations === void 0 ? /* @__PURE__ */ Object.create(null) : annotations;\n    this._counter = 0;\n    this._size = 0;\n  }\n  ChangeAnnotations2.prototype.all = function() {\n    return this._annotations;\n  };\n  Object.defineProperty(ChangeAnnotations2.prototype, \"size\", {\n    get: function() {\n      return this._size;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  ChangeAnnotations2.prototype.manage = function(idOrAnnotation, annotation) {\n    var id;\n    if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {\n      id = idOrAnnotation;\n    } else {\n      id = this.nextId();\n      annotation = idOrAnnotation;\n    }\n    if (this._annotations[id] !== void 0) {\n      throw new Error(\"Id \" + id + \" is already in use.\");\n    }\n    if (annotation === void 0) {\n      throw new Error(\"No annotation provided for id \" + id);\n    }\n    this._annotations[id] = annotation;\n    this._size++;\n    return id;\n  };\n  ChangeAnnotations2.prototype.nextId = function() {\n    this._counter++;\n    return this._counter.toString();\n  };\n  return ChangeAnnotations2;\n}();\nvar WorkspaceChange = function() {\n  function WorkspaceChange2(workspaceEdit) {\n    var _this = this;\n    this._textEditChanges = /* @__PURE__ */ Object.create(null);\n    if (workspaceEdit !== void 0) {\n      this._workspaceEdit = workspaceEdit;\n      if (workspaceEdit.documentChanges) {\n        this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);\n        workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        workspaceEdit.documentChanges.forEach(function(change) {\n          if (TextDocumentEdit.is(change)) {\n            var textEditChange = new TextEditChangeImpl(change.edits, _this._changeAnnotations);\n            _this._textEditChanges[change.textDocument.uri] = textEditChange;\n          }\n        });\n      } else if (workspaceEdit.changes) {\n        Object.keys(workspaceEdit.changes).forEach(function(key) {\n          var textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);\n          _this._textEditChanges[key] = textEditChange;\n        });\n      }\n    } else {\n      this._workspaceEdit = {};\n    }\n  }\n  Object.defineProperty(WorkspaceChange2.prototype, \"edit\", {\n    get: function() {\n      this.initDocumentChanges();\n      if (this._changeAnnotations !== void 0) {\n        if (this._changeAnnotations.size === 0) {\n          this._workspaceEdit.changeAnnotations = void 0;\n        } else {\n          this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        }\n      }\n      return this._workspaceEdit;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  WorkspaceChange2.prototype.getTextEditChange = function(key) {\n    if (OptionalVersionedTextDocumentIdentifier.is(key)) {\n      this.initDocumentChanges();\n      if (this._workspaceEdit.documentChanges === void 0) {\n        throw new Error(\"Workspace edit is not configured for document changes.\");\n      }\n      var textDocument = { uri: key.uri, version: key.version };\n      var result = this._textEditChanges[textDocument.uri];\n      if (!result) {\n        var edits = [];\n        var textDocumentEdit = {\n          textDocument,\n          edits\n        };\n        this._workspaceEdit.documentChanges.push(textDocumentEdit);\n        result = new TextEditChangeImpl(edits, this._changeAnnotations);\n        this._textEditChanges[textDocument.uri] = result;\n      }\n      return result;\n    } else {\n      this.initChanges();\n      if (this._workspaceEdit.changes === void 0) {\n        throw new Error(\"Workspace edit is not configured for normal text edit changes.\");\n      }\n      var result = this._textEditChanges[key];\n      if (!result) {\n        var edits = [];\n        this._workspaceEdit.changes[key] = edits;\n        result = new TextEditChangeImpl(edits);\n        this._textEditChanges[key] = result;\n      }\n      return result;\n    }\n  };\n  WorkspaceChange2.prototype.initDocumentChanges = function() {\n    if (this._workspaceEdit.documentChanges === void 0 && this._workspaceEdit.changes === void 0) {\n      this._changeAnnotations = new ChangeAnnotations();\n      this._workspaceEdit.documentChanges = [];\n      this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n    }\n  };\n  WorkspaceChange2.prototype.initChanges = function() {\n    if (this._workspaceEdit.documentChanges === void 0 && this._workspaceEdit.changes === void 0) {\n      this._workspaceEdit.changes = /* @__PURE__ */ Object.create(null);\n    }\n  };\n  WorkspaceChange2.prototype.createFile = function(uri, optionsOrAnnotation, options) {\n    this.initDocumentChanges();\n    if (this._workspaceEdit.documentChanges === void 0) {\n      throw new Error(\"Workspace edit is not configured for document changes.\");\n    }\n    var annotation;\n    if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n      annotation = optionsOrAnnotation;\n    } else {\n      options = optionsOrAnnotation;\n    }\n    var operation;\n    var id;\n    if (annotation === void 0) {\n      operation = CreateFile.create(uri, options);\n    } else {\n      id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n      operation = CreateFile.create(uri, options, id);\n    }\n    this._workspaceEdit.documentChanges.push(operation);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  WorkspaceChange2.prototype.renameFile = function(oldUri, newUri, optionsOrAnnotation, options) {\n    this.initDocumentChanges();\n    if (this._workspaceEdit.documentChanges === void 0) {\n      throw new Error(\"Workspace edit is not configured for document changes.\");\n    }\n    var annotation;\n    if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n      annotation = optionsOrAnnotation;\n    } else {\n      options = optionsOrAnnotation;\n    }\n    var operation;\n    var id;\n    if (annotation === void 0) {\n      operation = RenameFile.create(oldUri, newUri, options);\n    } else {\n      id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n      operation = RenameFile.create(oldUri, newUri, options, id);\n    }\n    this._workspaceEdit.documentChanges.push(operation);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  WorkspaceChange2.prototype.deleteFile = function(uri, optionsOrAnnotation, options) {\n    this.initDocumentChanges();\n    if (this._workspaceEdit.documentChanges === void 0) {\n      throw new Error(\"Workspace edit is not configured for document changes.\");\n    }\n    var annotation;\n    if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n      annotation = optionsOrAnnotation;\n    } else {\n      options = optionsOrAnnotation;\n    }\n    var operation;\n    var id;\n    if (annotation === void 0) {\n      operation = DeleteFile.create(uri, options);\n    } else {\n      id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n      operation = DeleteFile.create(uri, options, id);\n    }\n    this._workspaceEdit.documentChanges.push(operation);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  return WorkspaceChange2;\n}();\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n})(MarkupKind || (MarkupKind = {}));\n(function(MarkupKind2) {\n  function is(value) {\n    var candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    var candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    var candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    var candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation) {\n    var parameters = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      parameters[_i - 2] = arguments[_i];\n    }\n    var result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    var result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    var result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    var result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only) {\n    var result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string));\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    var result = { title };\n    var checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    var result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate !== void 0 && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    var text = document.getText();\n    var sortedEdits = mergeSort(edits, function(a, b) {\n      var diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    var lastModifiedOffset = text.length;\n    for (var i = sortedEdits.length - 1; i >= 0; i--) {\n      var e = sortedEdits[i];\n      var startOffset = document.offsetAt(e.range.start);\n      var endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    var p = data.length / 2 | 0;\n    var left = data.slice(0, p);\n    var right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    var leftIdx = 0;\n    var rightIdx = 0;\n    var i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      var ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = function() {\n  function FullTextDocument2(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  Object.defineProperty(FullTextDocument2.prototype, \"uri\", {\n    get: function() {\n      return this._uri;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(FullTextDocument2.prototype, \"languageId\", {\n    get: function() {\n      return this._languageId;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(FullTextDocument2.prototype, \"version\", {\n    get: function() {\n      return this._version;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  FullTextDocument2.prototype.getText = function(range) {\n    if (range) {\n      var start = this.offsetAt(range.start);\n      var end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  };\n  FullTextDocument2.prototype.update = function(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  };\n  FullTextDocument2.prototype.getLineOffsets = function() {\n    if (this._lineOffsets === void 0) {\n      var lineOffsets = [];\n      var text = this._content;\n      var isLineStart = true;\n      for (var i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        var ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  };\n  FullTextDocument2.prototype.positionAt = function(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    var lineOffsets = this.getLineOffsets();\n    var low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      var mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    var line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  };\n  FullTextDocument2.prototype.offsetAt = function(position) {\n    var lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    var lineOffset = lineOffsets[position.line];\n    var nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  };\n  Object.defineProperty(FullTextDocument2.prototype, \"lineCount\", {\n    get: function() {\n      return this.getLineOffsets().length;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  return FullTextDocument2;\n}();\nvar Is;\n(function(Is2) {\n  var toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n      onModelRemoved(event.model);\n      onModelAdd(event.model);\n    }));\n    this._disposables.push(configChangeEvent((_) => {\n      monaco_editor_core_exports.editor.getModels().forEach((model) => {\n        if (model.getLanguageId() === this._languageId) {\n          onModelRemoved(model);\n          onModelAdd(model);\n        }\n      });\n    }));\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  _disposables = [];\n  _listener = /* @__PURE__ */ Object.create(null);\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker) => {\n      return worker.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(position.lineNumber, wordInfo.startColumn, position.lineNumber, wordInfo.endColumn);\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(range.start.line + 1, range.start.character + 1, range.end.line + 1, range.end.character + 1);\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => ({\n        name: item.name,\n        detail: \"\",\n        containerName: item.containerName,\n        kind: toSymbolKind(item.kind),\n        range: toRange(item.location.range),\n        selectionRange: toRange(item.location.range),\n        tags: []\n      }));\n    });\n  }\n};\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.Array;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  canFormatMultipleRanges = false;\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.getColorPresentations(resource.toString(), info.color, fromRange(info.range))).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.getSelectionRanges(resource.toString(), positions.map(fromPosition))).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/scanner.js\nfunction createScanner(text, ignoreTrivia) {\n  if (ignoreTrivia === void 0) {\n    ignoreTrivia = false;\n  }\n  var len = text.length;\n  var pos = 0, value = \"\", tokenOffset = 0, token = 16, lineNumber = 0, lineStartOffset = 0, tokenLineStartOffset = 0, prevTokenLineStartOffset = 0, scanError = 0;\n  function scanHexDigits(count, exact) {\n    var digits = 0;\n    var value2 = 0;\n    while (digits < count || !exact) {\n      var ch = text.charCodeAt(pos);\n      if (ch >= 48 && ch <= 57) {\n        value2 = value2 * 16 + ch - 48;\n      } else if (ch >= 65 && ch <= 70) {\n        value2 = value2 * 16 + ch - 65 + 10;\n      } else if (ch >= 97 && ch <= 102) {\n        value2 = value2 * 16 + ch - 97 + 10;\n      } else {\n        break;\n      }\n      pos++;\n      digits++;\n    }\n    if (digits < count) {\n      value2 = -1;\n    }\n    return value2;\n  }\n  function setPosition(newPosition) {\n    pos = newPosition;\n    value = \"\";\n    tokenOffset = 0;\n    token = 16;\n    scanError = 0;\n  }\n  function scanNumber() {\n    var start = pos;\n    if (text.charCodeAt(pos) === 48) {\n      pos++;\n    } else {\n      pos++;\n      while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n      }\n    }\n    if (pos < text.length && text.charCodeAt(pos) === 46) {\n      pos++;\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n      } else {\n        scanError = 3;\n        return text.substring(start, pos);\n      }\n    }\n    var end = pos;\n    if (pos < text.length && (text.charCodeAt(pos) === 69 || text.charCodeAt(pos) === 101)) {\n      pos++;\n      if (pos < text.length && text.charCodeAt(pos) === 43 || text.charCodeAt(pos) === 45) {\n        pos++;\n      }\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n        end = pos;\n      } else {\n        scanError = 3;\n      }\n    }\n    return text.substring(start, end);\n  }\n  function scanString() {\n    var result = \"\", start = pos;\n    while (true) {\n      if (pos >= len) {\n        result += text.substring(start, pos);\n        scanError = 2;\n        break;\n      }\n      var ch = text.charCodeAt(pos);\n      if (ch === 34) {\n        result += text.substring(start, pos);\n        pos++;\n        break;\n      }\n      if (ch === 92) {\n        result += text.substring(start, pos);\n        pos++;\n        if (pos >= len) {\n          scanError = 2;\n          break;\n        }\n        var ch2 = text.charCodeAt(pos++);\n        switch (ch2) {\n          case 34:\n            result += '\"';\n            break;\n          case 92:\n            result += \"\\\\\";\n            break;\n          case 47:\n            result += \"/\";\n            break;\n          case 98:\n            result += \"\\b\";\n            break;\n          case 102:\n            result += \"\\f\";\n            break;\n          case 110:\n            result += \"\\n\";\n            break;\n          case 114:\n            result += \"\\r\";\n            break;\n          case 116:\n            result += \"\t\";\n            break;\n          case 117:\n            var ch3 = scanHexDigits(4, true);\n            if (ch3 >= 0) {\n              result += String.fromCharCode(ch3);\n            } else {\n              scanError = 4;\n            }\n            break;\n          default:\n            scanError = 5;\n        }\n        start = pos;\n        continue;\n      }\n      if (ch >= 0 && ch <= 31) {\n        if (isLineBreak(ch)) {\n          result += text.substring(start, pos);\n          scanError = 2;\n          break;\n        } else {\n          scanError = 6;\n        }\n      }\n      pos++;\n    }\n    return result;\n  }\n  function scanNext() {\n    value = \"\";\n    scanError = 0;\n    tokenOffset = pos;\n    lineStartOffset = lineNumber;\n    prevTokenLineStartOffset = tokenLineStartOffset;\n    if (pos >= len) {\n      tokenOffset = len;\n      return token = 17;\n    }\n    var code = text.charCodeAt(pos);\n    if (isWhiteSpace(code)) {\n      do {\n        pos++;\n        value += String.fromCharCode(code);\n        code = text.charCodeAt(pos);\n      } while (isWhiteSpace(code));\n      return token = 15;\n    }\n    if (isLineBreak(code)) {\n      pos++;\n      value += String.fromCharCode(code);\n      if (code === 13 && text.charCodeAt(pos) === 10) {\n        pos++;\n        value += \"\\n\";\n      }\n      lineNumber++;\n      tokenLineStartOffset = pos;\n      return token = 14;\n    }\n    switch (code) {\n      case 123:\n        pos++;\n        return token = 1;\n      case 125:\n        pos++;\n        return token = 2;\n      case 91:\n        pos++;\n        return token = 3;\n      case 93:\n        pos++;\n        return token = 4;\n      case 58:\n        pos++;\n        return token = 6;\n      case 44:\n        pos++;\n        return token = 5;\n      case 34:\n        pos++;\n        value = scanString();\n        return token = 10;\n      case 47:\n        var start = pos - 1;\n        if (text.charCodeAt(pos + 1) === 47) {\n          pos += 2;\n          while (pos < len) {\n            if (isLineBreak(text.charCodeAt(pos))) {\n              break;\n            }\n            pos++;\n          }\n          value = text.substring(start, pos);\n          return token = 12;\n        }\n        if (text.charCodeAt(pos + 1) === 42) {\n          pos += 2;\n          var safeLength = len - 1;\n          var commentClosed = false;\n          while (pos < safeLength) {\n            var ch = text.charCodeAt(pos);\n            if (ch === 42 && text.charCodeAt(pos + 1) === 47) {\n              pos += 2;\n              commentClosed = true;\n              break;\n            }\n            pos++;\n            if (isLineBreak(ch)) {\n              if (ch === 13 && text.charCodeAt(pos) === 10) {\n                pos++;\n              }\n              lineNumber++;\n              tokenLineStartOffset = pos;\n            }\n          }\n          if (!commentClosed) {\n            pos++;\n            scanError = 1;\n          }\n          value = text.substring(start, pos);\n          return token = 13;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n      case 45:\n        value += String.fromCharCode(code);\n        pos++;\n        if (pos === len || !isDigit(text.charCodeAt(pos))) {\n          return token = 16;\n        }\n      case 48:\n      case 49:\n      case 50:\n      case 51:\n      case 52:\n      case 53:\n      case 54:\n      case 55:\n      case 56:\n      case 57:\n        value += scanNumber();\n        return token = 11;\n      default:\n        while (pos < len && isUnknownContentCharacter(code)) {\n          pos++;\n          code = text.charCodeAt(pos);\n        }\n        if (tokenOffset !== pos) {\n          value = text.substring(tokenOffset, pos);\n          switch (value) {\n            case \"true\":\n              return token = 8;\n            case \"false\":\n              return token = 9;\n            case \"null\":\n              return token = 7;\n          }\n          return token = 16;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n    }\n  }\n  function isUnknownContentCharacter(code) {\n    if (isWhiteSpace(code) || isLineBreak(code)) {\n      return false;\n    }\n    switch (code) {\n      case 125:\n      case 93:\n      case 123:\n      case 91:\n      case 34:\n      case 58:\n      case 44:\n      case 47:\n        return false;\n    }\n    return true;\n  }\n  function scanNextNonTrivia() {\n    var result;\n    do {\n      result = scanNext();\n    } while (result >= 12 && result <= 15);\n    return result;\n  }\n  return {\n    setPosition,\n    getPosition: function() {\n      return pos;\n    },\n    scan: ignoreTrivia ? scanNextNonTrivia : scanNext,\n    getToken: function() {\n      return token;\n    },\n    getTokenValue: function() {\n      return value;\n    },\n    getTokenOffset: function() {\n      return tokenOffset;\n    },\n    getTokenLength: function() {\n      return pos - tokenOffset;\n    },\n    getTokenStartLine: function() {\n      return lineStartOffset;\n    },\n    getTokenStartCharacter: function() {\n      return tokenOffset - prevTokenLineStartOffset;\n    },\n    getTokenError: function() {\n      return scanError;\n    }\n  };\n}\nfunction isWhiteSpace(ch) {\n  return ch === 32 || ch === 9 || ch === 11 || ch === 12 || ch === 160 || ch === 5760 || ch >= 8192 && ch <= 8203 || ch === 8239 || ch === 8287 || ch === 12288 || ch === 65279;\n}\nfunction isLineBreak(ch) {\n  return ch === 10 || ch === 13 || ch === 8232 || ch === 8233;\n}\nfunction isDigit(ch) {\n  return ch >= 48 && ch <= 57;\n}\n\n// node_modules/jsonc-parser/lib/esm/impl/parser.js\nvar ParseOptions;\n(function(ParseOptions2) {\n  ParseOptions2.DEFAULT = {\n    allowTrailingComma: false\n  };\n})(ParseOptions || (ParseOptions = {}));\n\n// node_modules/jsonc-parser/lib/esm/main.js\nvar createScanner2 = createScanner;\n\n// src/language/json/tokenization.ts\nfunction createTokenizationSupport(supportComments) {\n  return {\n    getInitialState: () => new JSONState(null, null, false, null),\n    tokenize: (line, state) => tokenize(supportComments, line, state)\n  };\n}\nvar TOKEN_DELIM_OBJECT = \"delimiter.bracket.json\";\nvar TOKEN_DELIM_ARRAY = \"delimiter.array.json\";\nvar TOKEN_DELIM_COLON = \"delimiter.colon.json\";\nvar TOKEN_DELIM_COMMA = \"delimiter.comma.json\";\nvar TOKEN_VALUE_BOOLEAN = \"keyword.json\";\nvar TOKEN_VALUE_NULL = \"keyword.json\";\nvar TOKEN_VALUE_STRING = \"string.value.json\";\nvar TOKEN_VALUE_NUMBER = \"number.json\";\nvar TOKEN_PROPERTY_NAME = \"string.key.json\";\nvar TOKEN_COMMENT_BLOCK = \"comment.block.json\";\nvar TOKEN_COMMENT_LINE = \"comment.line.json\";\nvar ParentsStack = class {\n  constructor(parent, type) {\n    this.parent = parent;\n    this.type = type;\n  }\n  static pop(parents) {\n    if (parents) {\n      return parents.parent;\n    }\n    return null;\n  }\n  static push(parents, type) {\n    return new ParentsStack(parents, type);\n  }\n  static equals(a, b) {\n    if (!a && !b) {\n      return true;\n    }\n    if (!a || !b) {\n      return false;\n    }\n    while (a && b) {\n      if (a === b) {\n        return true;\n      }\n      if (a.type !== b.type) {\n        return false;\n      }\n      a = a.parent;\n      b = b.parent;\n    }\n    return true;\n  }\n};\nvar JSONState = class {\n  _state;\n  scanError;\n  lastWasColon;\n  parents;\n  constructor(state, scanError, lastWasColon, parents) {\n    this._state = state;\n    this.scanError = scanError;\n    this.lastWasColon = lastWasColon;\n    this.parents = parents;\n  }\n  clone() {\n    return new JSONState(this._state, this.scanError, this.lastWasColon, this.parents);\n  }\n  equals(other) {\n    if (other === this) {\n      return true;\n    }\n    if (!other || !(other instanceof JSONState)) {\n      return false;\n    }\n    return this.scanError === other.scanError && this.lastWasColon === other.lastWasColon && ParentsStack.equals(this.parents, other.parents);\n  }\n  getStateData() {\n    return this._state;\n  }\n  setStateData(state) {\n    this._state = state;\n  }\n};\nfunction tokenize(comments, line, state, offsetDelta = 0) {\n  let numberOfInsertedCharacters = 0;\n  let adjustOffset = false;\n  switch (state.scanError) {\n    case 2 /* UnexpectedEndOfString */:\n      line = '\"' + line;\n      numberOfInsertedCharacters = 1;\n      break;\n    case 1 /* UnexpectedEndOfComment */:\n      line = \"/*\" + line;\n      numberOfInsertedCharacters = 2;\n      break;\n  }\n  const scanner = createScanner2(line);\n  let lastWasColon = state.lastWasColon;\n  let parents = state.parents;\n  const ret = {\n    tokens: [],\n    endState: state.clone()\n  };\n  while (true) {\n    let offset = offsetDelta + scanner.getPosition();\n    let type = \"\";\n    const kind = scanner.scan();\n    if (kind === 17 /* EOF */) {\n      break;\n    }\n    if (offset === offsetDelta + scanner.getPosition()) {\n      throw new Error(\"Scanner did not advance, next 3 characters are: \" + line.substr(scanner.getPosition(), 3));\n    }\n    if (adjustOffset) {\n      offset -= numberOfInsertedCharacters;\n    }\n    adjustOffset = numberOfInsertedCharacters > 0;\n    switch (kind) {\n      case 1 /* OpenBraceToken */:\n        parents = ParentsStack.push(parents, 0 /* Object */);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 2 /* CloseBraceToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 3 /* OpenBracketToken */:\n        parents = ParentsStack.push(parents, 1 /* Array */);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 4 /* CloseBracketToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 6 /* ColonToken */:\n        type = TOKEN_DELIM_COLON;\n        lastWasColon = true;\n        break;\n      case 5 /* CommaToken */:\n        type = TOKEN_DELIM_COMMA;\n        lastWasColon = false;\n        break;\n      case 8 /* TrueKeyword */:\n      case 9 /* FalseKeyword */:\n        type = TOKEN_VALUE_BOOLEAN;\n        lastWasColon = false;\n        break;\n      case 7 /* NullKeyword */:\n        type = TOKEN_VALUE_NULL;\n        lastWasColon = false;\n        break;\n      case 10 /* StringLiteral */:\n        const currentParent = parents ? parents.type : 0 /* Object */;\n        const inArray = currentParent === 1 /* Array */;\n        type = lastWasColon || inArray ? TOKEN_VALUE_STRING : TOKEN_PROPERTY_NAME;\n        lastWasColon = false;\n        break;\n      case 11 /* NumericLiteral */:\n        type = TOKEN_VALUE_NUMBER;\n        lastWasColon = false;\n        break;\n    }\n    if (comments) {\n      switch (kind) {\n        case 12 /* LineCommentTrivia */:\n          type = TOKEN_COMMENT_LINE;\n          break;\n        case 13 /* BlockCommentTrivia */:\n          type = TOKEN_COMMENT_BLOCK;\n          break;\n      }\n    }\n    ret.endState = new JSONState(state.getStateData(), scanner.getTokenError(), lastWasColon, parents);\n    ret.tokens.push({\n      startIndex: offset,\n      scopes: type\n    });\n  }\n  return ret;\n}\n\n// src/language/json/jsonMode.ts\nvar JSONDiagnosticsAdapter = class extends DiagnosticsAdapter {\n  constructor(languageId, worker, defaults) {\n    super(languageId, worker, defaults.onDidChange);\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel((model) => {\n      this._resetSchema(model.uri);\n    }));\n    this._disposables.push(monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n      this._resetSchema(event.model.uri);\n    }));\n  }\n  _resetSchema(resource) {\n    this._worker().then((worker) => {\n      worker.resetSchema(resource.toString());\n    });\n  }\n};\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration: modeConfiguration2 } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration2.documentFormattingEdits) {\n      providers.push(monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(languageId, new DocumentFormattingEditProvider(worker)));\n    }\n    if (modeConfiguration2.documentRangeFormattingEdits) {\n      providers.push(monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(languageId, new DocumentRangeFormattingEditProvider(worker)));\n    }\n    if (modeConfiguration2.completionItems) {\n      providers.push(monaco_editor_core_exports.languages.registerCompletionItemProvider(languageId, new CompletionAdapter(worker, [\" \", \":\", '\"'])));\n    }\n    if (modeConfiguration2.hovers) {\n      providers.push(monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker)));\n    }\n    if (modeConfiguration2.documentSymbols) {\n      providers.push(monaco_editor_core_exports.languages.registerDocumentSymbolProvider(languageId, new DocumentSymbolAdapter(worker)));\n    }\n    if (modeConfiguration2.tokens) {\n      providers.push(monaco_editor_core_exports.languages.setTokensProvider(languageId, createTokenizationSupport(true)));\n    }\n    if (modeConfiguration2.colors) {\n      providers.push(monaco_editor_core_exports.languages.registerColorProvider(languageId, new DocumentColorAdapter(worker)));\n    }\n    if (modeConfiguration2.foldingRanges) {\n      providers.push(monaco_editor_core_exports.languages.registerFoldingRangeProvider(languageId, new FoldingRangeAdapter(worker)));\n    }\n    if (modeConfiguration2.diagnostics) {\n      providers.push(new JSONDiagnosticsAdapter(languageId, worker, defaults));\n    }\n    if (modeConfiguration2.selectionRanges) {\n      providers.push(monaco_editor_core_exports.languages.registerSelectionRangeProvider(languageId, new SelectionRangeAdapter(worker)));\n    }\n  }\n  registerProviders();\n  disposables.push(monaco_editor_core_exports.languages.setLanguageConfiguration(defaults.languageId, richEditConfiguration));\n  let modeConfiguration = defaults.modeConfiguration;\n  defaults.onDidChange((newDefaults) => {\n    if (newDefaults.modeConfiguration !== modeConfiguration) {\n      modeConfiguration = newDefaults.modeConfiguration;\n      registerProviders();\n    }\n  });\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\nvar richEditConfiguration = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\[\\{\\]\\}\\:\\\"\\,\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ]\n};\nexport {\n  CompletionAdapter,\n  DefinitionAdapter,\n  DiagnosticsAdapter,\n  DocumentColorAdapter,\n  DocumentFormattingEditProvider,\n  DocumentHighlightAdapter,\n  DocumentLinkAdapter,\n  DocumentRangeFormattingEditProvider,\n  DocumentSymbolAdapter,\n  FoldingRangeAdapter,\n  HoverAdapter,\n  ReferenceAdapter,\n  RenameAdapter,\n  SelectionRangeAdapter,\n  WorkerManager,\n  fromPosition,\n  fromRange,\n  setupMode,\n  toRange,\n  toTextEdit\n};\n"], "mappings": ";;;;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,qBAAqB,IAAI,KAAK;AAClC,IAAI,gBAAgB,MAAM;AAAA,EAOxB,YAAY,UAAU;AANtB;AACA;AACA;AACA;AACA;AACA;AAEE,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,qBAAqB,OAAO,YAAY,MAAM,KAAK,aAAa,GAAG,KAAK,GAAG;AAChF,SAAK,gBAAgB;AACrB,SAAK,wBAAwB,KAAK,UAAU,YAAY,MAAM,KAAK,YAAY,CAAC;AAAA,EAClF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACjB;AACA,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,UAAU;AACR,kBAAc,KAAK,kBAAkB;AACrC,SAAK,sBAAsB,QAAQ;AACnC,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,eAAe;AACb,QAAI,CAAC,KAAK,SAAS;AACjB;AAAA,IACF;AACA,QAAI,0BAA0B,KAAK,IAAI,IAAI,KAAK;AAChD,QAAI,0BAA0B,oBAAoB;AAChD,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,gBAAgB,KAAK,IAAI;AAC9B,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU,2BAA2B,OAAO,gBAAgB;AAAA,QAC/D,UAAU;AAAA,QACV,OAAO,KAAK,UAAU;AAAA,QACtB,YAAY;AAAA,UACV,kBAAkB,KAAK,UAAU;AAAA,UACjC,YAAY,KAAK,UAAU;AAAA,UAC3B,qBAAqB,KAAK,UAAU,mBAAmB;AAAA,QACzD;AAAA,MACF,CAAC;AACD,WAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,IACvC;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,4BAA4B,WAAW;AACrC,QAAI;AACJ,WAAO,KAAK,WAAW,EAAE,KAAK,CAAC,WAAW;AACxC,gBAAU;AAAA,IACZ,CAAC,EAAE,KAAK,CAAC,MAAM;AACb,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,oBAAoB,SAAS;AAAA,MACnD;AAAA,IACF,CAAC,EAAE,KAAK,CAAC,MAAM,OAAO;AAAA,EACxB;AACF;AAGA,IAAI;AAAA,CACH,SAAS,UAAU;AAClB,WAAS,YAAY;AACrB,WAAS,YAAY;AACvB,GAAG,YAAY,UAAU,CAAC,EAAE;AAC5B,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,YAAU,YAAY;AACtB,YAAU,YAAY;AACxB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,WAAS,OAAO,MAAM,WAAW;AAC/B,QAAI,SAAS,OAAO,WAAW;AAC7B,aAAO,SAAS;AAAA,IAClB;AACA,QAAI,cAAc,OAAO,WAAW;AAClC,kBAAY,SAAS;AAAA,IACvB;AACA,WAAO,EAAE,MAAM,UAAU;AAAA,EAC3B;AACA,YAAU,SAAS;AACnB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,SAAS,UAAU,IAAI,KAAK,GAAG,SAAS,UAAU,SAAS;AAAA,EACtG;AACA,YAAU,KAAK;AACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,QAAQ;AAChB,WAAS,OAAO,KAAK,KAAK,OAAO,MAAM;AACrC,QAAI,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,KAAK,KAAK,GAAG,SAAS,IAAI,GAAG;AACnF,aAAO,EAAE,OAAO,SAAS,OAAO,KAAK,GAAG,GAAG,KAAK,SAAS,OAAO,OAAO,IAAI,EAAE;AAAA,IAC/E,WAAW,SAAS,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG;AAC/C,aAAO,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,IAChC,OAAO;AACL,YAAM,IAAI,MAAM,gDAAgD,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,GAAG;AAAA,IACrH;AAAA,EACF;AACA,SAAO,SAAS;AAChB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,SAAS,GAAG,UAAU,KAAK,KAAK,SAAS,GAAG,UAAU,GAAG;AAAA,EACjG;AACA,SAAO,KAAK;AACd,GAAG,UAAU,QAAQ,CAAC,EAAE;AACxB,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,WAAS,OAAO,KAAK,OAAO;AAC1B,WAAO,EAAE,KAAK,MAAM;AAAA,EACtB;AACA,YAAU,SAAS;AACnB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,UAAU,UAAU,GAAG;AAAA,EACtH;AACA,YAAU,KAAK;AACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,OAAO,WAAW,aAAa,sBAAsB,sBAAsB;AAClF,WAAO,EAAE,WAAW,aAAa,sBAAsB,qBAAqB;AAAA,EAC9E;AACA,gBAAc,SAAS;AACvB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,WAAW,KAAK,GAAG,OAAO,UAAU,SAAS,MAAM,MAAM,GAAG,UAAU,oBAAoB,KAAK,GAAG,UAAU,UAAU,oBAAoB,OAAO,MAAM,GAAG,UAAU,oBAAoB,KAAK,GAAG,UAAU,UAAU,oBAAoB;AAAA,EAC7R;AACA,gBAAc,KAAK;AACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI;AAAA,CACH,SAAS,QAAQ;AAChB,WAAS,OAAO,KAAK,OAAO,MAAM,OAAO;AACvC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAS;AAChB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,YAAY,UAAU,KAAK,GAAG,CAAC,KAAK,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC,KAAK,GAAG,YAAY,UAAU,MAAM,GAAG,CAAC,KAAK,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC;AAAA,EACrK;AACA,SAAO,KAAK;AACd,GAAG,UAAU,QAAQ,CAAC,EAAE;AACxB,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,WAAS,OAAO,OAAO,OAAO;AAC5B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,oBAAkB,SAAS;AAC3B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,MAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,KAAK;AAAA,EAC9D;AACA,oBAAkB,KAAK;AACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,OAAO,UAAU,qBAAqB;AACpD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,qBAAmB,SAAS;AAC5B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,OAAO,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,QAAQ,KAAK,SAAS,GAAG,SAAS,OAAO,GAAG,UAAU,UAAU,mBAAmB,KAAK,GAAG,WAAW,UAAU,qBAAqB,SAAS,EAAE;AAAA,EAC/M;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,oBAAkB,SAAS,IAAI;AAC/B,oBAAkB,SAAS,IAAI;AAC/B,oBAAkB,QAAQ,IAAI;AAChC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,OAAO,WAAW,SAAS,gBAAgB,cAAc,MAAM;AACtE,QAAI,SAAS;AAAA,MACX;AAAA,MACA;AAAA,IACF;AACA,QAAI,GAAG,QAAQ,cAAc,GAAG;AAC9B,aAAO,iBAAiB;AAAA,IAC1B;AACA,QAAI,GAAG,QAAQ,YAAY,GAAG;AAC5B,aAAO,eAAe;AAAA,IACxB;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AACpB,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,gBAAc,SAAS;AACvB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,SAAS,UAAU,SAAS,KAAK,GAAG,SAAS,UAAU,SAAS,MAAM,GAAG,UAAU,UAAU,cAAc,KAAK,GAAG,SAAS,UAAU,cAAc,OAAO,GAAG,UAAU,UAAU,YAAY,KAAK,GAAG,SAAS,UAAU,YAAY,OAAO,GAAG,UAAU,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAChT;AACA,gBAAc,KAAK;AACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI;AAAA,CACH,SAAS,+BAA+B;AACvC,WAAS,OAAO,UAAU,SAAS;AACjC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,gCAA8B,SAAS;AACvC,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,SAAS,GAAG,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAChG;AACA,gCAA8B,KAAK;AACrC,GAAG,iCAAiC,+BAA+B,CAAC,EAAE;AACtE,IAAI;AAAA,CACH,SAAS,qBAAqB;AAC7B,sBAAoB,QAAQ;AAC5B,sBAAoB,UAAU;AAC9B,sBAAoB,cAAc;AAClC,sBAAoB,OAAO;AAC7B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA,CACH,SAAS,gBAAgB;AACxB,iBAAe,cAAc;AAC7B,iBAAe,aAAa;AAC9B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA,CACH,SAAS,kBAAkB;AAC1B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,cAAc,UAAU,cAAc,QAAQ,GAAG,OAAO,UAAU,IAAI;AAAA,EAC/E;AACA,mBAAiB,KAAK;AACxB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,OAAO,SAAS,UAAU,MAAM,QAAQ,oBAAoB;AAC1E,QAAI,SAAS,EAAE,OAAO,QAAQ;AAC9B,QAAI,GAAG,QAAQ,QAAQ,GAAG;AACxB,aAAO,WAAW;AAAA,IACpB;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AACpB,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,GAAG,QAAQ,MAAM,GAAG;AACtB,aAAO,SAAS;AAAA,IAClB;AACA,QAAI,GAAG,QAAQ,kBAAkB,GAAG;AAClC,aAAO,qBAAqB;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI;AACJ,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,OAAO,MAAM,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,UAAU,UAAU,QAAQ,OAAO,GAAG,QAAQ,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,UAAU,UAAU,IAAI,OAAO,GAAG,UAAU,UAAU,eAAe,KAAK,GAAG,QAAQ,KAAK,UAAU,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,OAAO,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,UAAU,UAAU,MAAM,OAAO,GAAG,UAAU,UAAU,kBAAkB,KAAK,GAAG,WAAW,UAAU,oBAAoB,6BAA6B,EAAE;AAAA,EACzkB;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,UAAU;AAClB,WAAS,OAAO,OAAO,SAAS;AAC9B,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IAC7B;AACA,QAAI,SAAS,EAAE,OAAO,QAAQ;AAC9B,QAAI,GAAG,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AACvC,aAAO,YAAY;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,WAAS,SAAS;AAClB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAC3F;AACA,WAAS,KAAK;AAChB,GAAG,YAAY,UAAU,CAAC,EAAE;AAC5B,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,WAAS,QAAQ,OAAO,SAAS;AAC/B,WAAO,EAAE,OAAO,QAAQ;AAAA,EAC1B;AACA,YAAU,UAAU;AACpB,WAAS,OAAO,UAAU,SAAS;AACjC,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,QAAQ;AAAA,EAC9D;AACA,YAAU,SAAS;AACnB,WAAS,IAAI,OAAO;AAClB,WAAO,EAAE,OAAO,SAAS,GAAG;AAAA,EAC9B;AACA,YAAU,MAAM;AAChB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,OAAO,KAAK,MAAM,GAAG,UAAU,KAAK;AAAA,EAChG;AACA,YAAU,KAAK;AACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,WAAS,OAAO,OAAO,mBAAmB,aAAa;AACrD,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,sBAAsB,QAAQ;AAChC,aAAO,oBAAoB;AAAA,IAC7B;AACA,QAAI,gBAAgB,QAAQ;AAC1B,aAAO,cAAc;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,oBAAkB,SAAS;AAC3B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,cAAc,UAAU,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,MAAM,GAAG,QAAQ,UAAU,iBAAiB,KAAK,UAAU,sBAAsB,YAAY,GAAG,OAAO,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAAA,EACpP;AACA,oBAAkB,KAAK;AACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,6BAA6B;AACrC,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,OAAO,cAAc;AAAA,EAC9B;AACA,8BAA4B,KAAK;AACnC,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAClE,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,QAAQ,OAAO,SAAS,YAAY;AAC3C,WAAO,EAAE,OAAO,SAAS,cAAc,WAAW;AAAA,EACpD;AACA,qBAAmB,UAAU;AAC7B,WAAS,OAAO,UAAU,SAAS,YAAY;AAC7C,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,SAAS,cAAc,WAAW;AAAA,EACxF;AACA,qBAAmB,SAAS;AAC5B,WAAS,IAAI,OAAO,YAAY;AAC9B,WAAO,EAAE,OAAO,SAAS,IAAI,cAAc,WAAW;AAAA,EACxD;AACA,qBAAmB,MAAM;AACzB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,SAAS,GAAG,SAAS,MAAM,iBAAiB,GAAG,UAAU,YAAY,KAAK,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACvI;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,WAAS,OAAO,cAAc,OAAO;AACnC,WAAO,EAAE,cAAc,MAAM;AAAA,EAC/B;AACA,oBAAkB,SAAS;AAC3B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,wCAAwC,GAAG,UAAU,YAAY,KAAK,MAAM,QAAQ,UAAU,KAAK;AAAA,EACrI;AACA,oBAAkB,KAAK;AACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,KAAK,SAAS,YAAY;AACxC,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AACA,QAAI,YAAY,WAAW,QAAQ,cAAc,UAAU,QAAQ,mBAAmB,SAAS;AAC7F,aAAO,UAAU;AAAA,IACnB;AACA,QAAI,eAAe,QAAQ;AACzB,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAAW,UAAU,QAAQ,cAAc,UAAU,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAU,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,iBAAiB,UAAU,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACrY;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,QAAQ,QAAQ,SAAS,YAAY;AACnD,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AACA,QAAI,YAAY,WAAW,QAAQ,cAAc,UAAU,QAAQ,mBAAmB,SAAS;AAC7F,aAAO,UAAU;AAAA,IACnB;AACA,QAAI,eAAe,QAAQ;AACzB,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM,MAAM,UAAU,YAAY,WAAW,UAAU,QAAQ,cAAc,UAAU,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAU,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,iBAAiB,UAAU,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACva;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,KAAK,SAAS,YAAY;AACxC,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AACA,QAAI,YAAY,WAAW,QAAQ,cAAc,UAAU,QAAQ,sBAAsB,SAAS;AAChG,aAAO,UAAU;AAAA,IACnB;AACA,QAAI,eAAe,QAAQ;AACzB,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAAW,UAAU,QAAQ,cAAc,UAAU,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,sBAAsB,UAAU,GAAG,QAAQ,UAAU,QAAQ,iBAAiB,QAAQ,UAAU,iBAAiB,UAAU,2BAA2B,GAAG,UAAU,YAAY;AAAA,EAC3Y;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,gBAAgB;AACxB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,cAAc,UAAU,YAAY,UAAU,UAAU,oBAAoB,YAAY,UAAU,oBAAoB,UAAU,UAAU,gBAAgB,MAAM,SAAS,QAAQ;AACtL,UAAI,GAAG,OAAO,OAAO,IAAI,GAAG;AAC1B,eAAO,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM;AAAA,MAC/E,OAAO;AACL,eAAO,iBAAiB,GAAG,MAAM;AAAA,MACnC;AAAA,IACF,CAAC;AAAA,EACH;AACA,iBAAe,KAAK;AACtB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI,qBAAqB,WAAW;AAClC,WAAS,oBAAoB,OAAO,mBAAmB;AACrD,SAAK,QAAQ;AACb,SAAK,oBAAoB;AAAA,EAC3B;AACA,sBAAoB,UAAU,SAAS,SAAS,UAAU,SAAS,YAAY;AAC7E,QAAI;AACJ,QAAI;AACJ,QAAI,eAAe,QAAQ;AACzB,aAAO,SAAS,OAAO,UAAU,OAAO;AAAA,IAC1C,WAAW,2BAA2B,GAAG,UAAU,GAAG;AACpD,WAAK;AACL,aAAO,kBAAkB,OAAO,UAAU,SAAS,UAAU;AAAA,IAC/D,OAAO;AACL,WAAK,wBAAwB,KAAK,iBAAiB;AACnD,WAAK,KAAK,kBAAkB,OAAO,UAAU;AAC7C,aAAO,kBAAkB,OAAO,UAAU,SAAS,EAAE;AAAA,IACvD;AACA,SAAK,MAAM,KAAK,IAAI;AACpB,QAAI,OAAO,QAAQ;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,sBAAoB,UAAU,UAAU,SAAS,OAAO,SAAS,YAAY;AAC3E,QAAI;AACJ,QAAI;AACJ,QAAI,eAAe,QAAQ;AACzB,aAAO,SAAS,QAAQ,OAAO,OAAO;AAAA,IACxC,WAAW,2BAA2B,GAAG,UAAU,GAAG;AACpD,WAAK;AACL,aAAO,kBAAkB,QAAQ,OAAO,SAAS,UAAU;AAAA,IAC7D,OAAO;AACL,WAAK,wBAAwB,KAAK,iBAAiB;AACnD,WAAK,KAAK,kBAAkB,OAAO,UAAU;AAC7C,aAAO,kBAAkB,QAAQ,OAAO,SAAS,EAAE;AAAA,IACrD;AACA,SAAK,MAAM,KAAK,IAAI;AACpB,QAAI,OAAO,QAAQ;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,sBAAoB,UAAU,SAAS,SAAS,OAAO,YAAY;AACjE,QAAI;AACJ,QAAI;AACJ,QAAI,eAAe,QAAQ;AACzB,aAAO,SAAS,IAAI,KAAK;AAAA,IAC3B,WAAW,2BAA2B,GAAG,UAAU,GAAG;AACpD,WAAK;AACL,aAAO,kBAAkB,IAAI,OAAO,UAAU;AAAA,IAChD,OAAO;AACL,WAAK,wBAAwB,KAAK,iBAAiB;AACnD,WAAK,KAAK,kBAAkB,OAAO,UAAU;AAC7C,aAAO,kBAAkB,IAAI,OAAO,EAAE;AAAA,IACxC;AACA,SAAK,MAAM,KAAK,IAAI;AACpB,QAAI,OAAO,QAAQ;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,sBAAoB,UAAU,MAAM,SAAS,MAAM;AACjD,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AACA,sBAAoB,UAAU,MAAM,WAAW;AAC7C,WAAO,KAAK;AAAA,EACd;AACA,sBAAoB,UAAU,QAAQ,WAAW;AAC/C,SAAK,MAAM,OAAO,GAAG,KAAK,MAAM,MAAM;AAAA,EACxC;AACA,sBAAoB,UAAU,0BAA0B,SAAS,OAAO;AACtE,QAAI,UAAU,QAAQ;AACpB,YAAM,IAAI,MAAM,kEAAkE;AAAA,IACpF;AAAA,EACF;AACA,SAAO;AACT,EAAE;AACF,IAAI,oBAAoB,WAAW;AACjC,WAAS,mBAAmB,aAAa;AACvC,SAAK,eAAe,gBAAgB,SAAyB,uBAAO,OAAO,IAAI,IAAI;AACnF,SAAK,WAAW;AAChB,SAAK,QAAQ;AAAA,EACf;AACA,qBAAmB,UAAU,MAAM,WAAW;AAC5C,WAAO,KAAK;AAAA,EACd;AACA,SAAO,eAAe,mBAAmB,WAAW,QAAQ;AAAA,IAC1D,KAAK,WAAW;AACd,aAAO,KAAK;AAAA,IACd;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACD,qBAAmB,UAAU,SAAS,SAAS,gBAAgB,YAAY;AACzE,QAAI;AACJ,QAAI,2BAA2B,GAAG,cAAc,GAAG;AACjD,WAAK;AAAA,IACP,OAAO;AACL,WAAK,KAAK,OAAO;AACjB,mBAAa;AAAA,IACf;AACA,QAAI,KAAK,aAAa,EAAE,MAAM,QAAQ;AACpC,YAAM,IAAI,MAAM,QAAQ,KAAK,qBAAqB;AAAA,IACpD;AACA,QAAI,eAAe,QAAQ;AACzB,YAAM,IAAI,MAAM,mCAAmC,EAAE;AAAA,IACvD;AACA,SAAK,aAAa,EAAE,IAAI;AACxB,SAAK;AACL,WAAO;AAAA,EACT;AACA,qBAAmB,UAAU,SAAS,WAAW;AAC/C,SAAK;AACL,WAAO,KAAK,SAAS,SAAS;AAAA,EAChC;AACA,SAAO;AACT,EAAE;AACF,IAAI,kBAAkB,WAAW;AAC/B,WAAS,iBAAiB,eAAe;AACvC,QAAI,QAAQ;AACZ,SAAK,mBAAmC,uBAAO,OAAO,IAAI;AAC1D,QAAI,kBAAkB,QAAQ;AAC5B,WAAK,iBAAiB;AACtB,UAAI,cAAc,iBAAiB;AACjC,aAAK,qBAAqB,IAAI,kBAAkB,cAAc,iBAAiB;AAC/E,sBAAc,oBAAoB,KAAK,mBAAmB,IAAI;AAC9D,sBAAc,gBAAgB,QAAQ,SAAS,QAAQ;AACrD,cAAI,iBAAiB,GAAG,MAAM,GAAG;AAC/B,gBAAI,iBAAiB,IAAI,mBAAmB,OAAO,OAAO,MAAM,kBAAkB;AAClF,kBAAM,iBAAiB,OAAO,aAAa,GAAG,IAAI;AAAA,UACpD;AAAA,QACF,CAAC;AAAA,MACH,WAAW,cAAc,SAAS;AAChC,eAAO,KAAK,cAAc,OAAO,EAAE,QAAQ,SAAS,KAAK;AACvD,cAAI,iBAAiB,IAAI,mBAAmB,cAAc,QAAQ,GAAG,CAAC;AACtE,gBAAM,iBAAiB,GAAG,IAAI;AAAA,QAChC,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,WAAK,iBAAiB,CAAC;AAAA,IACzB;AAAA,EACF;AACA,SAAO,eAAe,iBAAiB,WAAW,QAAQ;AAAA,IACxD,KAAK,WAAW;AACd,WAAK,oBAAoB;AACzB,UAAI,KAAK,uBAAuB,QAAQ;AACtC,YAAI,KAAK,mBAAmB,SAAS,GAAG;AACtC,eAAK,eAAe,oBAAoB;AAAA,QAC1C,OAAO;AACL,eAAK,eAAe,oBAAoB,KAAK,mBAAmB,IAAI;AAAA,QACtE;AAAA,MACF;AACA,aAAO,KAAK;AAAA,IACd;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACD,mBAAiB,UAAU,oBAAoB,SAAS,KAAK;AAC3D,QAAI,wCAAwC,GAAG,GAAG,GAAG;AACnD,WAAK,oBAAoB;AACzB,UAAI,KAAK,eAAe,oBAAoB,QAAQ;AAClD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AACA,UAAI,eAAe,EAAE,KAAK,IAAI,KAAK,SAAS,IAAI,QAAQ;AACxD,UAAI,SAAS,KAAK,iBAAiB,aAAa,GAAG;AACnD,UAAI,CAAC,QAAQ;AACX,YAAI,QAAQ,CAAC;AACb,YAAI,mBAAmB;AAAA,UACrB;AAAA,UACA;AAAA,QACF;AACA,aAAK,eAAe,gBAAgB,KAAK,gBAAgB;AACzD,iBAAS,IAAI,mBAAmB,OAAO,KAAK,kBAAkB;AAC9D,aAAK,iBAAiB,aAAa,GAAG,IAAI;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,OAAO;AACL,WAAK,YAAY;AACjB,UAAI,KAAK,eAAe,YAAY,QAAQ;AAC1C,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AACA,UAAI,SAAS,KAAK,iBAAiB,GAAG;AACtC,UAAI,CAAC,QAAQ;AACX,YAAI,QAAQ,CAAC;AACb,aAAK,eAAe,QAAQ,GAAG,IAAI;AACnC,iBAAS,IAAI,mBAAmB,KAAK;AACrC,aAAK,iBAAiB,GAAG,IAAI;AAAA,MAC/B;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,mBAAiB,UAAU,sBAAsB,WAAW;AAC1D,QAAI,KAAK,eAAe,oBAAoB,UAAU,KAAK,eAAe,YAAY,QAAQ;AAC5F,WAAK,qBAAqB,IAAI,kBAAkB;AAChD,WAAK,eAAe,kBAAkB,CAAC;AACvC,WAAK,eAAe,oBAAoB,KAAK,mBAAmB,IAAI;AAAA,IACtE;AAAA,EACF;AACA,mBAAiB,UAAU,cAAc,WAAW;AAClD,QAAI,KAAK,eAAe,oBAAoB,UAAU,KAAK,eAAe,YAAY,QAAQ;AAC5F,WAAK,eAAe,UAA0B,uBAAO,OAAO,IAAI;AAAA,IAClE;AAAA,EACF;AACA,mBAAiB,UAAU,aAAa,SAAS,KAAK,qBAAqB,SAAS;AAClF,SAAK,oBAAoB;AACzB,QAAI,KAAK,eAAe,oBAAoB,QAAQ;AAClD,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC1E;AACA,QAAI;AACJ,QAAI,iBAAiB,GAAG,mBAAmB,KAAK,2BAA2B,GAAG,mBAAmB,GAAG;AAClG,mBAAa;AAAA,IACf,OAAO;AACL,gBAAU;AAAA,IACZ;AACA,QAAI;AACJ,QAAI;AACJ,QAAI,eAAe,QAAQ;AACzB,kBAAY,WAAW,OAAO,KAAK,OAAO;AAAA,IAC5C,OAAO;AACL,WAAK,2BAA2B,GAAG,UAAU,IAAI,aAAa,KAAK,mBAAmB,OAAO,UAAU;AACvG,kBAAY,WAAW,OAAO,KAAK,SAAS,EAAE;AAAA,IAChD;AACA,SAAK,eAAe,gBAAgB,KAAK,SAAS;AAClD,QAAI,OAAO,QAAQ;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,mBAAiB,UAAU,aAAa,SAAS,QAAQ,QAAQ,qBAAqB,SAAS;AAC7F,SAAK,oBAAoB;AACzB,QAAI,KAAK,eAAe,oBAAoB,QAAQ;AAClD,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC1E;AACA,QAAI;AACJ,QAAI,iBAAiB,GAAG,mBAAmB,KAAK,2BAA2B,GAAG,mBAAmB,GAAG;AAClG,mBAAa;AAAA,IACf,OAAO;AACL,gBAAU;AAAA,IACZ;AACA,QAAI;AACJ,QAAI;AACJ,QAAI,eAAe,QAAQ;AACzB,kBAAY,WAAW,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACvD,OAAO;AACL,WAAK,2BAA2B,GAAG,UAAU,IAAI,aAAa,KAAK,mBAAmB,OAAO,UAAU;AACvG,kBAAY,WAAW,OAAO,QAAQ,QAAQ,SAAS,EAAE;AAAA,IAC3D;AACA,SAAK,eAAe,gBAAgB,KAAK,SAAS;AAClD,QAAI,OAAO,QAAQ;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,mBAAiB,UAAU,aAAa,SAAS,KAAK,qBAAqB,SAAS;AAClF,SAAK,oBAAoB;AACzB,QAAI,KAAK,eAAe,oBAAoB,QAAQ;AAClD,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC1E;AACA,QAAI;AACJ,QAAI,iBAAiB,GAAG,mBAAmB,KAAK,2BAA2B,GAAG,mBAAmB,GAAG;AAClG,mBAAa;AAAA,IACf,OAAO;AACL,gBAAU;AAAA,IACZ;AACA,QAAI;AACJ,QAAI;AACJ,QAAI,eAAe,QAAQ;AACzB,kBAAY,WAAW,OAAO,KAAK,OAAO;AAAA,IAC5C,OAAO;AACL,WAAK,2BAA2B,GAAG,UAAU,IAAI,aAAa,KAAK,mBAAmB,OAAO,UAAU;AACvG,kBAAY,WAAW,OAAO,KAAK,SAAS,EAAE;AAAA,IAChD;AACA,SAAK,eAAe,gBAAgB,KAAK,SAAS;AAClD,QAAI,OAAO,QAAQ;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT,EAAE;AACF,IAAI;AAAA,CACH,SAAS,yBAAyB;AACjC,WAAS,OAAO,KAAK;AACnB,WAAO,EAAE,IAAI;AAAA,EACf;AACA,0BAAwB,SAAS;AACjC,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG;AAAA,EACzD;AACA,0BAAwB,KAAK;AAC/B,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAC1D,IAAI;AAAA,CACH,SAAS,kCAAkC;AAC1C,WAAS,OAAO,KAAK,SAAS;AAC5B,WAAO,EAAE,KAAK,QAAQ;AAAA,EACxB;AACA,mCAAiC,SAAS;AAC1C,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ,UAAU,OAAO;AAAA,EAC1F;AACA,mCAAiC,KAAK;AACxC,GAAG,oCAAoC,kCAAkC,CAAC,EAAE;AAC5E,IAAI;AAAA,CACH,SAAS,0CAA0C;AAClD,WAAS,OAAO,KAAK,SAAS;AAC5B,WAAO,EAAE,KAAK,QAAQ;AAAA,EACxB;AACA,2CAAyC,SAAS;AAClD,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,QAAQ,GAAG,QAAQ,UAAU,OAAO;AAAA,EACzH;AACA,2CAAyC,KAAK;AAChD,GAAG,4CAA4C,0CAA0C,CAAC,EAAE;AAC5F,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,WAAS,OAAO,KAAK,YAAY,SAAS,MAAM;AAC9C,WAAO,EAAE,KAAK,YAAY,SAAS,KAAK;AAAA,EAC1C;AACA,oBAAkB,SAAS;AAC3B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,OAAO,UAAU,UAAU,KAAK,GAAG,QAAQ,UAAU,OAAO,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAC1J;AACA,oBAAkB,KAAK;AACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,cAAY,YAAY;AACxB,cAAY,WAAW;AACzB,GAAG,eAAe,aAAa,CAAC,EAAE;AAAA,CACjC,SAAS,aAAa;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,cAAc,YAAY,aAAa,cAAc,YAAY;AAAA,EAC1E;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,gBAAgB;AACxB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,KAAK,KAAK,WAAW,GAAG,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAC9F;AACA,iBAAe,KAAK;AACtB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA,CACH,SAAS,qBAAqB;AAC7B,sBAAoB,OAAO;AAC3B,sBAAoB,SAAS;AAC7B,sBAAoB,WAAW;AAC/B,sBAAoB,cAAc;AAClC,sBAAoB,QAAQ;AAC5B,sBAAoB,WAAW;AAC/B,sBAAoB,QAAQ;AAC5B,sBAAoB,YAAY;AAChC,sBAAoB,SAAS;AAC7B,sBAAoB,WAAW;AAC/B,sBAAoB,OAAO;AAC3B,sBAAoB,QAAQ;AAC5B,sBAAoB,OAAO;AAC3B,sBAAoB,UAAU;AAC9B,sBAAoB,UAAU;AAC9B,sBAAoB,QAAQ;AAC5B,sBAAoB,OAAO;AAC3B,sBAAoB,YAAY;AAChC,sBAAoB,SAAS;AAC7B,sBAAoB,aAAa;AACjC,sBAAoB,WAAW;AAC/B,sBAAoB,SAAS;AAC7B,sBAAoB,QAAQ;AAC5B,sBAAoB,WAAW;AAC/B,sBAAoB,gBAAgB;AACtC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,oBAAkB,YAAY;AAC9B,oBAAkB,UAAU;AAC9B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,qBAAmB,aAAa;AAClC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,SAAS,QAAQ,SAAS;AACxC,WAAO,EAAE,SAAS,QAAQ,QAAQ;AAAA,EACpC;AACA,qBAAmB,SAAS;AAC5B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,OAAO,KAAK,MAAM,GAAG,UAAU,MAAM,KAAK,MAAM,GAAG,UAAU,OAAO;AAAA,EAC9G;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,kBAAgB,OAAO;AACvB,kBAAgB,oBAAoB;AACtC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,WAAS,OAAO,OAAO;AACrB,WAAO,EAAE,MAAM;AAAA,EACjB;AACA,kBAAgB,SAAS;AAC3B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,WAAS,OAAO,OAAO,cAAc;AACnC,WAAO,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,cAAc,CAAC,CAAC,aAAa;AAAA,EACnE;AACA,kBAAgB,SAAS;AAC3B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,cAAc,WAAW;AAChC,WAAO,UAAU,QAAQ,yBAAyB,MAAM;AAAA,EAC1D;AACA,gBAAc,gBAAgB;AAC9B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,OAAO,SAAS,KAAK,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAC1H;AACA,gBAAc,KAAK;AACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI;AAAA,CACH,SAAS,QAAQ;AAChB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,CAAC,CAAC,aAAa,GAAG,cAAc,SAAS,MAAM,cAAc,GAAG,UAAU,QAAQ,KAAK,aAAa,GAAG,UAAU,QAAQ,KAAK,GAAG,WAAW,UAAU,UAAU,aAAa,EAAE,OAAO,MAAM,UAAU,UAAU,MAAM,GAAG,MAAM,KAAK;AAAA,EAC7O;AACA,SAAO,KAAK;AACd,GAAG,UAAU,QAAQ,CAAC,EAAE;AACxB,IAAI;AAAA,CACH,SAAS,uBAAuB;AAC/B,WAAS,OAAO,OAAO,eAAe;AACpC,WAAO,gBAAgB,EAAE,OAAO,cAAc,IAAI,EAAE,MAAM;AAAA,EAC5D;AACA,wBAAsB,SAAS;AACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AACtD,IAAI;AAAA,CACH,SAAS,uBAAuB;AAC/B,WAAS,OAAO,OAAO,eAAe;AACpC,QAAI,aAAa,CAAC;AAClB,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,iBAAW,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IACnC;AACA,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,GAAG,QAAQ,aAAa,GAAG;AAC7B,aAAO,gBAAgB;AAAA,IACzB;AACA,QAAI,GAAG,QAAQ,UAAU,GAAG;AAC1B,aAAO,aAAa;AAAA,IACtB,OAAO;AACL,aAAO,aAAa,CAAC;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,wBAAsB,SAAS;AACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AACtD,IAAI;AAAA,CACH,SAAS,wBAAwB;AAChC,yBAAuB,OAAO;AAC9B,yBAAuB,OAAO;AAC9B,yBAAuB,QAAQ;AACjC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AACxD,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,OAAO,MAAM;AAC3B,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,GAAG,OAAO,IAAI,GAAG;AACnB,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,qBAAmB,SAAS;AAC9B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,cAAY,OAAO;AACnB,cAAY,SAAS;AACrB,cAAY,YAAY;AACxB,cAAY,UAAU;AACtB,cAAY,QAAQ;AACpB,cAAY,SAAS;AACrB,cAAY,WAAW;AACvB,cAAY,QAAQ;AACpB,cAAY,cAAc;AAC1B,cAAY,OAAO;AACnB,cAAY,YAAY;AACxB,cAAY,WAAW;AACvB,cAAY,WAAW;AACvB,cAAY,WAAW;AACvB,cAAY,SAAS;AACrB,cAAY,SAAS;AACrB,cAAY,UAAU;AACtB,cAAY,QAAQ;AACpB,cAAY,SAAS;AACrB,cAAY,MAAM;AAClB,cAAY,OAAO;AACnB,cAAY,aAAa;AACzB,cAAY,SAAS;AACrB,cAAY,QAAQ;AACpB,cAAY,WAAW;AACvB,cAAY,gBAAgB;AAC9B,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,YAAY;AACpB,aAAW,aAAa;AAC1B,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,MAAM,MAAM,OAAO,KAAK,eAAe;AACrD,QAAI,SAAS;AAAA,MACX;AAAA,MACA;AAAA,MACA,UAAU,EAAE,KAAK,MAAM;AAAA,IACzB;AACA,QAAI,eAAe;AACjB,aAAO,gBAAgB;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AACA,qBAAmB,SAAS;AAC9B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,WAAS,OAAO,MAAM,QAAQ,MAAM,OAAO,gBAAgB,UAAU;AACnE,QAAI,SAAS;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,aAAa,QAAQ;AACvB,aAAO,WAAW;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AACA,kBAAgB,SAAS;AACzB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KAAK,MAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,cAAc,MAAM,UAAU,WAAW,UAAU,GAAG,OAAO,UAAU,MAAM,OAAO,UAAU,eAAe,UAAU,GAAG,QAAQ,UAAU,UAAU,OAAO,UAAU,aAAa,UAAU,MAAM,QAAQ,UAAU,QAAQ,OAAO,UAAU,SAAS,UAAU,MAAM,QAAQ,UAAU,IAAI;AAAA,EAC9Z;AACA,kBAAgB,KAAK;AACvB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,kBAAgB,QAAQ;AACxB,kBAAgB,WAAW;AAC3B,kBAAgB,WAAW;AAC3B,kBAAgB,kBAAkB;AAClC,kBAAgB,iBAAiB;AACjC,kBAAgB,kBAAkB;AAClC,kBAAgB,SAAS;AACzB,kBAAgB,wBAAwB;AACxC,kBAAgB,eAAe;AACjC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,aAAa,MAAM;AACjC,QAAI,SAAS,EAAE,YAAY;AAC3B,QAAI,SAAS,UAAU,SAAS,MAAM;AACpC,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,qBAAmB,SAAS;AAC5B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,MAAM,UAAU,SAAS,UAAU,GAAG,WAAW,UAAU,MAAM,GAAG,MAAM;AAAA,EAC9J;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,OAAO,qBAAqB,MAAM;AAChD,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,YAAY;AAChB,QAAI,OAAO,wBAAwB,UAAU;AAC3C,kBAAY;AACZ,aAAO,OAAO;AAAA,IAChB,WAAW,QAAQ,GAAG,mBAAmB,GAAG;AAC1C,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,aAAa,SAAS,QAAQ;AAChC,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,KAAK,MAAM,UAAU,gBAAgB,UAAU,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,OAAO,UAAU,SAAS,UAAU,GAAG,OAAO,UAAU,IAAI,OAAO,UAAU,SAAS,UAAU,UAAU,YAAY,YAAY,UAAU,YAAY,UAAU,QAAQ,GAAG,UAAU,OAAO,OAAO,UAAU,gBAAgB,UAAU,GAAG,QAAQ,UAAU,WAAW,OAAO,UAAU,SAAS,UAAU,cAAc,GAAG,UAAU,IAAI;AAAA,EACvd;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,WAAS,OAAO,OAAO,MAAM;AAC3B,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,GAAG,QAAQ,IAAI,GAAG;AACpB,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,YAAU,SAAS;AACnB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,OAAO,KAAK,QAAQ,GAAG,UAAU,OAAO;AAAA,EAC/H;AACA,YAAU,KAAK;AACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,SAAS,cAAc;AACrC,WAAO,EAAE,SAAS,aAAa;AAAA,EACjC;AACA,qBAAmB,SAAS;AAC5B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,SAAS,UAAU,OAAO,KAAK,GAAG,QAAQ,UAAU,YAAY;AAAA,EACrG;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,OAAO,OAAO,QAAQ,MAAM;AACnC,WAAO,EAAE,OAAO,QAAQ,KAAK;AAAA,EAC/B;AACA,gBAAc,SAAS;AACvB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM;AAAA,EAC5H;AACA,gBAAc,KAAK;AACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,WAAS,OAAO,OAAO,QAAQ;AAC7B,WAAO,EAAE,OAAO,OAAO;AAAA,EACzB;AACA,kBAAgB,SAAS;AACzB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,cAAc,UAAU,MAAM,GAAG,UAAU,KAAK,MAAM,UAAU,WAAW,UAAU,gBAAgB,GAAG,UAAU,MAAM;AAAA,EACjI;AACA,kBAAgB,KAAK;AACvB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,OAAO,KAAK,YAAY,SAAS,SAAS;AACjD,WAAO,IAAI,iBAAiB,KAAK,YAAY,SAAS,OAAO;AAAA,EAC/D;AACA,gBAAc,SAAS;AACvB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,GAAG,UAAU,UAAU,UAAU,KAAK,GAAG,OAAO,UAAU,UAAU,MAAM,GAAG,SAAS,UAAU,SAAS,KAAK,GAAG,KAAK,UAAU,OAAO,KAAK,GAAG,KAAK,UAAU,UAAU,KAAK,GAAG,KAAK,UAAU,QAAQ,IAAI,OAAO;AAAA,EACjR;AACA,gBAAc,KAAK;AACnB,WAAS,WAAW,UAAU,OAAO;AACnC,QAAI,OAAO,SAAS,QAAQ;AAC5B,QAAI,cAAc,UAAU,OAAO,SAAS,GAAG,GAAG;AAChD,UAAI,OAAO,EAAE,MAAM,MAAM,OAAO,EAAE,MAAM,MAAM;AAC9C,UAAI,SAAS,GAAG;AACd,eAAO,EAAE,MAAM,MAAM,YAAY,EAAE,MAAM,MAAM;AAAA,MACjD;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,qBAAqB,KAAK;AAC9B,aAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,UAAI,IAAI,YAAY,CAAC;AACrB,UAAI,cAAc,SAAS,SAAS,EAAE,MAAM,KAAK;AACjD,UAAI,YAAY,SAAS,SAAS,EAAE,MAAM,GAAG;AAC7C,UAAI,aAAa,oBAAoB;AACnC,eAAO,KAAK,UAAU,GAAG,WAAW,IAAI,EAAE,UAAU,KAAK,UAAU,WAAW,KAAK,MAAM;AAAA,MAC3F,OAAO;AACL,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACpC;AACA,2BAAqB;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,gBAAc,aAAa;AAC3B,WAAS,UAAU,MAAM,SAAS;AAChC,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT;AACA,QAAI,IAAI,KAAK,SAAS,IAAI;AAC1B,QAAI,OAAO,KAAK,MAAM,GAAG,CAAC;AAC1B,QAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,cAAU,MAAM,OAAO;AACvB,cAAU,OAAO,OAAO;AACxB,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,IAAI;AACR,WAAO,UAAU,KAAK,UAAU,WAAW,MAAM,QAAQ;AACvD,UAAI,MAAM,QAAQ,KAAK,OAAO,GAAG,MAAM,QAAQ,CAAC;AAChD,UAAI,OAAO,GAAG;AACZ,aAAK,GAAG,IAAI,KAAK,SAAS;AAAA,MAC5B,OAAO;AACL,aAAK,GAAG,IAAI,MAAM,UAAU;AAAA,MAC9B;AAAA,IACF;AACA,WAAO,UAAU,KAAK,QAAQ;AAC5B,WAAK,GAAG,IAAI,KAAK,SAAS;AAAA,IAC5B;AACA,WAAO,WAAW,MAAM,QAAQ;AAC9B,WAAK,GAAG,IAAI,MAAM,UAAU;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACF,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI,mBAAmB,WAAW;AAChC,WAAS,kBAAkB,KAAK,YAAY,SAAS,SAAS;AAC5D,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACtB;AACA,SAAO,eAAe,kBAAkB,WAAW,OAAO;AAAA,IACxD,KAAK,WAAW;AACd,aAAO,KAAK;AAAA,IACd;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACD,SAAO,eAAe,kBAAkB,WAAW,cAAc;AAAA,IAC/D,KAAK,WAAW;AACd,aAAO,KAAK;AAAA,IACd;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACD,SAAO,eAAe,kBAAkB,WAAW,WAAW;AAAA,IAC5D,KAAK,WAAW;AACd,aAAO,KAAK;AAAA,IACd;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACD,oBAAkB,UAAU,UAAU,SAAS,OAAO;AACpD,QAAI,OAAO;AACT,UAAI,QAAQ,KAAK,SAAS,MAAM,KAAK;AACrC,UAAI,MAAM,KAAK,SAAS,MAAM,GAAG;AACjC,aAAO,KAAK,SAAS,UAAU,OAAO,GAAG;AAAA,IAC3C;AACA,WAAO,KAAK;AAAA,EACd;AACA,oBAAkB,UAAU,SAAS,SAAS,OAAO,SAAS;AAC5D,SAAK,WAAW,MAAM;AACtB,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACtB;AACA,oBAAkB,UAAU,iBAAiB,WAAW;AACtD,QAAI,KAAK,iBAAiB,QAAQ;AAChC,UAAI,cAAc,CAAC;AACnB,UAAI,OAAO,KAAK;AAChB,UAAI,cAAc;AAClB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,aAAa;AACf,sBAAY,KAAK,CAAC;AAClB,wBAAc;AAAA,QAChB;AACA,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,sBAAc,OAAO,QAAQ,OAAO;AACpC,YAAI,OAAO,QAAQ,IAAI,IAAI,KAAK,UAAU,KAAK,OAAO,IAAI,CAAC,MAAM,MAAM;AACrE;AAAA,QACF;AAAA,MACF;AACA,UAAI,eAAe,KAAK,SAAS,GAAG;AAClC,oBAAY,KAAK,KAAK,MAAM;AAAA,MAC9B;AACA,WAAK,eAAe;AAAA,IACtB;AACA,WAAO,KAAK;AAAA,EACd;AACA,oBAAkB,UAAU,aAAa,SAAS,QAAQ;AACxD,aAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,SAAS,MAAM,GAAG,CAAC;AAC3D,QAAI,cAAc,KAAK,eAAe;AACtC,QAAI,MAAM,GAAG,OAAO,YAAY;AAChC,QAAI,SAAS,GAAG;AACd,aAAO,SAAS,OAAO,GAAG,MAAM;AAAA,IAClC;AACA,WAAO,MAAM,MAAM;AACjB,UAAI,MAAM,KAAK,OAAO,MAAM,QAAQ,CAAC;AACrC,UAAI,YAAY,GAAG,IAAI,QAAQ;AAC7B,eAAO;AAAA,MACT,OAAO;AACL,cAAM,MAAM;AAAA,MACd;AAAA,IACF;AACA,QAAI,OAAO,MAAM;AACjB,WAAO,SAAS,OAAO,MAAM,SAAS,YAAY,IAAI,CAAC;AAAA,EACzD;AACA,oBAAkB,UAAU,WAAW,SAAS,UAAU;AACxD,QAAI,cAAc,KAAK,eAAe;AACtC,QAAI,SAAS,QAAQ,YAAY,QAAQ;AACvC,aAAO,KAAK,SAAS;AAAA,IACvB,WAAW,SAAS,OAAO,GAAG;AAC5B,aAAO;AAAA,IACT;AACA,QAAI,aAAa,YAAY,SAAS,IAAI;AAC1C,QAAI,iBAAiB,SAAS,OAAO,IAAI,YAAY,SAAS,YAAY,SAAS,OAAO,CAAC,IAAI,KAAK,SAAS;AAC7G,WAAO,KAAK,IAAI,KAAK,IAAI,aAAa,SAAS,WAAW,cAAc,GAAG,UAAU;AAAA,EACvF;AACA,SAAO,eAAe,kBAAkB,WAAW,aAAa;AAAA,IAC9D,KAAK,WAAW;AACd,aAAO,KAAK,eAAe,EAAE;AAAA,IAC/B;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACD,SAAO;AACT,EAAE;AACF,IAAI;AAAA,CACH,SAAS,KAAK;AACb,MAAI,WAAW,OAAO,UAAU;AAChC,WAAS,QAAQ,OAAO;AACtB,WAAO,OAAO,UAAU;AAAA,EAC1B;AACA,MAAI,UAAU;AACd,WAAS,WAAW,OAAO;AACzB,WAAO,OAAO,UAAU;AAAA,EAC1B;AACA,MAAI,YAAY;AAChB,WAAS,QAAQ,OAAO;AACtB,WAAO,UAAU,QAAQ,UAAU;AAAA,EACrC;AACA,MAAI,UAAU;AACd,WAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EAClC;AACA,MAAI,SAAS;AACb,WAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EAClC;AACA,MAAI,SAAS;AACb,WAAS,YAAY,OAAO,KAAK,KAAK;AACpC,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,OAAO,SAAS,SAAS;AAAA,EAChF;AACA,MAAI,cAAc;AAClB,WAAS,SAAS,OAAO;AACvB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,eAAe,SAAS,SAAS;AAAA,EACxF;AACA,MAAI,UAAU;AACd,WAAS,UAAU,OAAO;AACxB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,KAAK,SAAS,SAAS;AAAA,EAC9E;AACA,MAAI,WAAW;AACf,WAAS,KAAK,OAAO;AACnB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EAClC;AACA,MAAI,OAAO;AACX,WAAS,cAAc,OAAO;AAC5B,WAAO,UAAU,QAAQ,OAAO,UAAU;AAAA,EAC5C;AACA,MAAI,gBAAgB;AACpB,WAAS,WAAW,OAAO,OAAO;AAChC,WAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,EAClD;AACA,MAAI,aAAa;AACnB,GAAG,OAAO,KAAK,CAAC,EAAE;AAGlB,IAAI,qBAAqB,MAAM;AAAA,EAC7B,YAAY,aAAa,SAAS,mBAAmB;AAgDrD,wCAAe,CAAC;AAChB,qCAA4B,uBAAO,OAAO,IAAI;AAhD5C,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,UAAM,aAAa,CAAC,UAAU;AAC5B,UAAI,SAAS,MAAM,cAAc;AACjC,UAAI,WAAW,KAAK,aAAa;AAC/B;AAAA,MACF;AACA,UAAI;AACJ,WAAK,UAAU,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,mBAAmB,MAAM;AACpE,eAAO,aAAa,MAAM;AAC1B,iBAAS,OAAO,WAAW,MAAM,KAAK,YAAY,MAAM,KAAK,MAAM,GAAG,GAAG;AAAA,MAC3E,CAAC;AACD,WAAK,YAAY,MAAM,KAAK,MAAM;AAAA,IACpC;AACA,UAAM,iBAAiB,CAAC,UAAU;AAChC,iCAA2B,OAAO,gBAAgB,OAAO,KAAK,aAAa,CAAC,CAAC;AAC7E,UAAI,SAAS,MAAM,IAAI,SAAS;AAChC,UAAI,WAAW,KAAK,UAAU,MAAM;AACpC,UAAI,UAAU;AACZ,iBAAS,QAAQ;AACjB,eAAO,KAAK,UAAU,MAAM;AAAA,MAC9B;AAAA,IACF;AACA,SAAK,aAAa,KAAK,2BAA2B,OAAO,iBAAiB,UAAU,CAAC;AACrF,SAAK,aAAa,KAAK,2BAA2B,OAAO,mBAAmB,cAAc,CAAC;AAC3F,SAAK,aAAa,KAAK,2BAA2B,OAAO,yBAAyB,CAAC,UAAU;AAC3F,qBAAe,MAAM,KAAK;AAC1B,iBAAW,MAAM,KAAK;AAAA,IACxB,CAAC,CAAC;AACF,SAAK,aAAa,KAAK,kBAAkB,CAAC,MAAM;AAC9C,iCAA2B,OAAO,UAAU,EAAE,QAAQ,CAAC,UAAU;AAC/D,YAAI,MAAM,cAAc,MAAM,KAAK,aAAa;AAC9C,yBAAe,KAAK;AACpB,qBAAW,KAAK;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH,CAAC,CAAC;AACF,SAAK,aAAa,KAAK;AAAA,MACrB,SAAS,MAAM;AACb,mCAA2B,OAAO,UAAU,EAAE,QAAQ,cAAc;AACpE,iBAAS,OAAO,KAAK,WAAW;AAC9B,eAAK,UAAU,GAAG,EAAE,QAAQ;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,CAAC;AACD,+BAA2B,OAAO,UAAU,EAAE,QAAQ,UAAU;AAAA,EAClE;AAAA,EAGA,UAAU;AACR,SAAK,aAAa,QAAQ,CAAC,MAAM,KAAK,EAAE,QAAQ,CAAC;AACjD,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA,EACA,YAAY,UAAU,YAAY;AAChC,SAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW;AACtC,aAAO,OAAO,aAAa,SAAS,SAAS,CAAC;AAAA,IAChD,CAAC,EAAE,KAAK,CAAC,gBAAgB;AACvB,YAAM,UAAU,YAAY,IAAI,CAAC,MAAM,cAAc,UAAU,CAAC,CAAC;AACjE,UAAI,QAAQ,2BAA2B,OAAO,SAAS,QAAQ;AAC/D,UAAI,SAAS,MAAM,cAAc,MAAM,YAAY;AACjD,mCAA2B,OAAO,gBAAgB,OAAO,YAAY,OAAO;AAAA,MAC9E;AAAA,IACF,CAAC,EAAE,KAAK,QAAQ,CAAC,QAAQ;AACvB,cAAQ,MAAM,GAAG;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,YAAY;AAC9B,UAAQ,YAAY;AAAA,IAClB,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD;AACE,aAAO,2BAA2B,eAAe;AAAA,EACrD;AACF;AACA,SAAS,cAAc,UAAU,MAAM;AACrC,MAAI,OAAO,OAAO,KAAK,SAAS,WAAW,OAAO,KAAK,IAAI,IAAI,KAAK;AACpE,SAAO;AAAA,IACL,UAAU,WAAW,KAAK,QAAQ;AAAA,IAClC,iBAAiB,KAAK,MAAM,MAAM,OAAO;AAAA,IACzC,aAAa,KAAK,MAAM,MAAM,YAAY;AAAA,IAC1C,eAAe,KAAK,MAAM,IAAI,OAAO;AAAA,IACrC,WAAW,KAAK,MAAM,IAAI,YAAY;AAAA,IACtC,SAAS,KAAK;AAAA,IACd;AAAA,IACA,QAAQ,KAAK;AAAA,EACf;AACF;AACA,IAAI,oBAAoB,MAAM;AAAA,EAC5B,YAAY,SAAS,oBAAoB;AACvC,SAAK,UAAU;AACf,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,uBAAuB,OAAO,UAAU,SAAS,OAAO;AACtD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW;AAC7C,aAAO,OAAO,WAAW,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,IACtE,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,YAAM,WAAW,MAAM,qBAAqB,QAAQ;AACpD,YAAM,YAAY,IAAI,2BAA2B,MAAM,SAAS,YAAY,SAAS,aAAa,SAAS,YAAY,SAAS,SAAS;AACzI,YAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,UAAU;AACtC,cAAM,OAAO;AAAA,UACX,OAAO,MAAM;AAAA,UACb,YAAY,MAAM,cAAc,MAAM;AAAA,UACtC,UAAU,MAAM;AAAA,UAChB,YAAY,MAAM;AAAA,UAClB,eAAe,MAAM;AAAA,UACrB,QAAQ,MAAM;AAAA,UACd,SAAS,UAAU,MAAM,OAAO;AAAA,UAChC,OAAO;AAAA,UACP,MAAM,qBAAqB,MAAM,IAAI;AAAA,QACvC;AACA,YAAI,MAAM,UAAU;AAClB,cAAI,oBAAoB,MAAM,QAAQ,GAAG;AACvC,iBAAK,QAAQ;AAAA,cACX,QAAQ,QAAQ,MAAM,SAAS,MAAM;AAAA,cACrC,SAAS,QAAQ,MAAM,SAAS,OAAO;AAAA,YACzC;AAAA,UACF,OAAO;AACL,iBAAK,QAAQ,QAAQ,MAAM,SAAS,KAAK;AAAA,UAC3C;AACA,eAAK,aAAa,MAAM,SAAS;AAAA,QACnC;AACA,YAAI,MAAM,qBAAqB;AAC7B,eAAK,sBAAsB,MAAM,oBAAoB,IAAI,UAAU;AAAA,QACrE;AACA,YAAI,MAAM,qBAAqB,iBAAiB,SAAS;AACvD,eAAK,kBAAkB,2BAA2B,UAAU,6BAA6B;AAAA,QAC3F;AACA,eAAO;AAAA,MACT,CAAC;AACD,aAAO;AAAA,QACL,cAAc,KAAK;AAAA,QACnB,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,aAAa,UAAU;AAC9B,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,SAAO,EAAE,WAAW,SAAS,SAAS,GAAG,MAAM,SAAS,aAAa,EAAE;AACzE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM,MAAM,kBAAkB;AAAA,MAC9B,WAAW,MAAM,cAAc;AAAA,IACjC;AAAA,IACA,KAAK,EAAE,MAAM,MAAM,gBAAgB,GAAG,WAAW,MAAM,YAAY,EAAE;AAAA,EACvE;AACF;AACA,SAAS,QAAQ,OAAO;AACtB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,IAAI,2BAA2B,MAAM,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,YAAY,GAAG,MAAM,IAAI,OAAO,GAAG,MAAM,IAAI,YAAY,CAAC;AAC1I;AACA,SAAS,oBAAoB,MAAM;AACjC,SAAO,OAAO,KAAK,WAAW,eAAe,OAAO,KAAK,YAAY;AACvE;AACA,SAAS,qBAAqB,MAAM;AAClC,QAAM,YAAY,2BAA2B,UAAU;AACvD,UAAQ,MAAM;AAAA,IACZ,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,EACrB;AACA,SAAO,UAAU;AACnB;AACA,SAAS,WAAW,UAAU;AAC5B,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,OAAO,QAAQ,SAAS,KAAK;AAAA,IAC7B,MAAM,SAAS;AAAA,EACjB;AACF;AACA,SAAS,UAAU,GAAG;AACpB,SAAO,KAAK,EAAE,YAAY,iCAAiC,EAAE,IAAI,EAAE,SAAS,OAAO,EAAE,OAAO,WAAW,EAAE,UAAU,IAAI;AACzH;AACA,IAAI,eAAe,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa,OAAO,UAAU,OAAO;AACnC,QAAI,WAAW,MAAM;AACrB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW;AAC7C,aAAO,OAAO,QAAQ,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,IACnE,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO,QAAQ,KAAK,KAAK;AAAA,QACzB,UAAU,oBAAoB,KAAK,QAAQ;AAAA,MAC7C;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,SAAS,OAAO,UAAU,YAAY,OAAO,MAAM,SAAS;AACrE;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,gBAAgB,KAAK,GAAG;AAC1B,QAAI,MAAM,SAAS,aAAa;AAC9B,aAAO;AAAA,QACL,OAAO,MAAM,MAAM,QAAQ,yBAAyB,MAAM;AAAA,MAC5D;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO,MAAM;AAAA,IACf;AAAA,EACF;AACA,SAAO,EAAE,OAAO,QAAQ,MAAM,WAAW,OAAO,MAAM,QAAQ,UAAU;AAC1E;AACA,SAAS,oBAAoB,UAAU;AACrC,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,WAAO,SAAS,IAAI,gBAAgB;AAAA,EACtC;AACA,SAAO,CAAC,iBAAiB,QAAQ,CAAC;AACpC;AACA,IAAI,2BAA2B,MAAM;AAAA,EACnC,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,0BAA0B,OAAO,UAAU,OAAO;AAChD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,uBAAuB,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,YAAY;AAC3I,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,aAAO,QAAQ,IAAI,CAAC,UAAU;AAC5B,eAAO;AAAA,UACL,OAAO,QAAQ,MAAM,KAAK;AAAA,UAC1B,MAAM,wBAAwB,MAAM,IAAI;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,wBAAwB,MAAM;AACrC,UAAQ,MAAM;AAAA,IACZ,KAAK,sBAAsB;AACzB,aAAO,2BAA2B,UAAU,sBAAsB;AAAA,IACpE,KAAK,sBAAsB;AACzB,aAAO,2BAA2B,UAAU,sBAAsB;AAAA,IACpE,KAAK,sBAAsB;AACzB,aAAO,2BAA2B,UAAU,sBAAsB;AAAA,EACtE;AACA,SAAO,2BAA2B,UAAU,sBAAsB;AACpE;AACA,IAAI,oBAAoB,MAAM;AAAA,EAC5B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB,OAAO,UAAU,OAAO;AACxC,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW;AAC7C,aAAO,OAAO,eAAe,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,IAC1E,CAAC,EAAE,KAAK,CAAC,eAAe;AACtB,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AACA,aAAO,CAAC,WAAW,UAAU,CAAC;AAAA,IAChC,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,UAAU;AAC5B,SAAO;AAAA,IACL,KAAK,2BAA2B,IAAI,MAAM,SAAS,GAAG;AAAA,IACtD,OAAO,QAAQ,SAAS,KAAK;AAAA,EAC/B;AACF;AACA,IAAI,mBAAmB,MAAM;AAAA,EAC3B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB,OAAO,UAAU,SAAS,OAAO;AACjD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW;AAC7C,aAAO,OAAO,eAAe,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,IAC1E,CAAC,EAAE,KAAK,CAAC,YAAY;AACnB,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,aAAO,QAAQ,IAAI,UAAU;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AACA,IAAI,gBAAgB,MAAM;AAAA,EACxB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,mBAAmB,OAAO,UAAU,SAAS,OAAO;AAClD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW;AAC7C,aAAO,OAAO,SAAS,SAAS,SAAS,GAAG,aAAa,QAAQ,GAAG,OAAO;AAAA,IAC7E,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,aAAO,gBAAgB,IAAI;AAAA,IAC7B,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,CAAC,QAAQ,CAAC,KAAK,SAAS;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,CAAC;AACrB,WAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,OAAO,2BAA2B,IAAI,MAAM,GAAG;AACrD,aAAS,KAAK,KAAK,QAAQ,GAAG,GAAG;AAC/B,oBAAc,KAAK;AAAA,QACjB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,UACR,OAAO,QAAQ,EAAE,KAAK;AAAA,UACtB,MAAM,EAAE;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,IAAI,wBAAwB,MAAM;AAAA,EAChC,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,uBAAuB,OAAO,OAAO;AACnC,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,oBAAoB,SAAS,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9G,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,aAAO,MAAM,IAAI,CAAC,UAAU;AAAA,QAC1B,MAAM,KAAK;AAAA,QACX,QAAQ;AAAA,QACR,eAAe,KAAK;AAAA,QACpB,MAAM,aAAa,KAAK,IAAI;AAAA,QAC5B,OAAO,QAAQ,KAAK,SAAS,KAAK;AAAA,QAClC,gBAAgB,QAAQ,KAAK,SAAS,KAAK;AAAA,QAC3C,MAAM,CAAC;AAAA,MACT,EAAE;AAAA,IACJ,CAAC;AAAA,EACH;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,MAAI,QAAQ,2BAA2B,UAAU;AACjD,UAAQ,MAAM;AAAA,IACZ,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,EACjB;AACA,SAAO,MAAM;AACf;AACA,IAAI,sBAAsB,MAAM;AAAA,EAC9B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa,OAAO,OAAO;AACzB,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,kBAAkB,SAAS,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AAC5G,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO,MAAM,IAAI,CAAC,UAAU;AAAA,UAC1B,OAAO,QAAQ,KAAK,KAAK;AAAA,UACzB,KAAK,KAAK;AAAA,QACZ,EAAE;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAI,iCAAiC,MAAM;AAAA,EACzC,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,+BAA+B,OAAO,SAAS,OAAO;AACpD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW;AAC7C,aAAO,OAAO,OAAO,SAAS,SAAS,GAAG,MAAM,sBAAsB,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9F,YAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC;AAAA,QACF;AACA,eAAO,MAAM,IAAI,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAI,sCAAsC,MAAM;AAAA,EAC9C,YAAY,SAAS;AAGrB,mDAA0B;AAFxB,SAAK,UAAU;AAAA,EACjB;AAAA,EAEA,oCAAoC,OAAO,OAAO,SAAS,OAAO;AAChE,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW;AAC7C,aAAO,OAAO,OAAO,SAAS,SAAS,GAAG,UAAU,KAAK,GAAG,sBAAsB,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1G,YAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC;AAAA,QACF;AACA,eAAO,MAAM,IAAI,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,sBAAsB,SAAS;AACtC,SAAO;AAAA,IACL,SAAS,QAAQ;AAAA,IACjB,cAAc,QAAQ;AAAA,EACxB;AACF;AACA,IAAI,uBAAuB,MAAM;AAAA,EAC/B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,sBAAsB,OAAO,OAAO;AAClC,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,mBAAmB,SAAS,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AAC7G,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,aAAO,MAAM,IAAI,CAAC,UAAU;AAAA,QAC1B,OAAO,KAAK;AAAA,QACZ,OAAO,QAAQ,KAAK,KAAK;AAAA,MAC3B,EAAE;AAAA,IACJ,CAAC;AAAA,EACH;AAAA,EACA,0BAA0B,OAAO,MAAM,OAAO;AAC5C,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,sBAAsB,SAAS,SAAS,GAAG,KAAK,OAAO,UAAU,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,kBAAkB;AAC3J,UAAI,CAAC,eAAe;AAClB;AAAA,MACF;AACA,aAAO,cAAc,IAAI,CAAC,iBAAiB;AACzC,YAAI,OAAO;AAAA,UACT,OAAO,aAAa;AAAA,QACtB;AACA,YAAI,aAAa,UAAU;AACzB,eAAK,WAAW,WAAW,aAAa,QAAQ;AAAA,QAClD;AACA,YAAI,aAAa,qBAAqB;AACpC,eAAK,sBAAsB,aAAa,oBAAoB,IAAI,UAAU;AAAA,QAC5E;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAI,sBAAsB,MAAM;AAAA,EAC9B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,qBAAqB,OAAO,SAAS,OAAO;AAC1C,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,iBAAiB,SAAS,SAAS,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,WAAW;AACrH,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,aAAO,OAAO,IAAI,CAAC,UAAU;AAC3B,cAAM,SAAS;AAAA,UACb,OAAO,MAAM,YAAY;AAAA,UACzB,KAAK,MAAM,UAAU;AAAA,QACvB;AACA,YAAI,OAAO,MAAM,SAAS,aAAa;AACrC,iBAAO,OAAO,mBAAmB,MAAM,IAAI;AAAA,QAC7C;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,mBAAmB,MAAM;AAChC,UAAQ,MAAM;AAAA,IACZ,KAAK,iBAAiB;AACpB,aAAO,2BAA2B,UAAU,iBAAiB;AAAA,IAC/D,KAAK,iBAAiB;AACpB,aAAO,2BAA2B,UAAU,iBAAiB;AAAA,IAC/D,KAAK,iBAAiB;AACpB,aAAO,2BAA2B,UAAU,iBAAiB;AAAA,EACjE;AACA,SAAO;AACT;AACA,IAAI,wBAAwB,MAAM;AAAA,EAChC,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,uBAAuB,OAAO,WAAW,OAAO;AAC9C,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,mBAAmB,SAAS,SAAS,GAAG,UAAU,IAAI,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,oBAAoB;AACpJ,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF;AACA,aAAO,gBAAgB,IAAI,CAAC,mBAAmB;AAC7C,cAAM,SAAS,CAAC;AAChB,eAAO,gBAAgB;AACrB,iBAAO,KAAK,EAAE,OAAO,QAAQ,eAAe,KAAK,EAAE,CAAC;AACpD,2BAAiB,eAAe;AAAA,QAClC;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAGA,SAAS,cAAc,MAAM,cAAc;AACzC,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,GAAG,QAAQ,IAAI,cAAc,GAAG,QAAQ,IAAI,aAAa,GAAG,kBAAkB,GAAG,uBAAuB,GAAG,2BAA2B,GAAG,YAAY;AAC/J,WAAS,cAAc,OAAO,OAAO;AACnC,QAAI,SAAS;AACb,QAAI,SAAS;AACb,WAAO,SAAS,SAAS,CAAC,OAAO;AAC/B,UAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,UAAI,MAAM,MAAM,MAAM,IAAI;AACxB,iBAAS,SAAS,KAAK,KAAK;AAAA,MAC9B,WAAW,MAAM,MAAM,MAAM,IAAI;AAC/B,iBAAS,SAAS,KAAK,KAAK,KAAK;AAAA,MACnC,WAAW,MAAM,MAAM,MAAM,KAAK;AAChC,iBAAS,SAAS,KAAK,KAAK,KAAK;AAAA,MACnC,OAAO;AACL;AAAA,MACF;AACA;AACA;AAAA,IACF;AACA,QAAI,SAAS,OAAO;AAClB,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,WAAS,YAAY,aAAa;AAChC,UAAM;AACN,YAAQ;AACR,kBAAc;AACd,YAAQ;AACR,gBAAY;AAAA,EACd;AACA,WAAS,aAAa;AACpB,QAAI,QAAQ;AACZ,QAAI,KAAK,WAAW,GAAG,MAAM,IAAI;AAC/B;AAAA,IACF,OAAO;AACL;AACA,aAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACzD;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,KAAK,UAAU,KAAK,WAAW,GAAG,MAAM,IAAI;AACpD;AACA,UAAI,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACtD;AACA,eAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACzD;AAAA,QACF;AAAA,MACF,OAAO;AACL,oBAAY;AACZ,eAAO,KAAK,UAAU,OAAO,GAAG;AAAA,MAClC;AAAA,IACF;AACA,QAAI,MAAM;AACV,QAAI,MAAM,KAAK,WAAW,KAAK,WAAW,GAAG,MAAM,MAAM,KAAK,WAAW,GAAG,MAAM,MAAM;AACtF;AACA,UAAI,MAAM,KAAK,UAAU,KAAK,WAAW,GAAG,MAAM,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI;AACnF;AAAA,MACF;AACA,UAAI,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACtD;AACA,eAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACzD;AAAA,QACF;AACA,cAAM;AAAA,MACR,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AACA,WAAO,KAAK,UAAU,OAAO,GAAG;AAAA,EAClC;AACA,WAAS,aAAa;AACpB,QAAI,SAAS,IAAI,QAAQ;AACzB,WAAO,MAAM;AACX,UAAI,OAAO,KAAK;AACd,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC,oBAAY;AACZ;AAAA,MACF;AACA,UAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,UAAI,OAAO,IAAI;AACb,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC;AACA;AAAA,MACF;AACA,UAAI,OAAO,IAAI;AACb,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC;AACA,YAAI,OAAO,KAAK;AACd,sBAAY;AACZ;AAAA,QACF;AACA,YAAI,MAAM,KAAK,WAAW,KAAK;AAC/B,gBAAQ,KAAK;AAAA,UACX,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,gBAAI,MAAM,cAAc,GAAG,IAAI;AAC/B,gBAAI,OAAO,GAAG;AACZ,wBAAU,OAAO,aAAa,GAAG;AAAA,YACnC,OAAO;AACL,0BAAY;AAAA,YACd;AACA;AAAA,UACF;AACE,wBAAY;AAAA,QAChB;AACA,gBAAQ;AACR;AAAA,MACF;AACA,UAAI,MAAM,KAAK,MAAM,IAAI;AACvB,YAAI,YAAY,EAAE,GAAG;AACnB,oBAAU,KAAK,UAAU,OAAO,GAAG;AACnC,sBAAY;AACZ;AAAA,QACF,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,WAAS,WAAW;AAClB,YAAQ;AACR,gBAAY;AACZ,kBAAc;AACd,sBAAkB;AAClB,+BAA2B;AAC3B,QAAI,OAAO,KAAK;AACd,oBAAc;AACd,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,OAAO,KAAK,WAAW,GAAG;AAC9B,QAAI,aAAa,IAAI,GAAG;AACtB,SAAG;AACD;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC,eAAO,KAAK,WAAW,GAAG;AAAA,MAC5B,SAAS,aAAa,IAAI;AAC1B,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,YAAY,IAAI,GAAG;AACrB;AACA,eAAS,OAAO,aAAa,IAAI;AACjC,UAAI,SAAS,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI;AAC9C;AACA,iBAAS;AAAA,MACX;AACA;AACA,6BAAuB;AACvB,aAAO,QAAQ;AAAA,IACjB;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,gBAAQ,WAAW;AACnB,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,YAAI,QAAQ,MAAM;AAClB,YAAI,KAAK,WAAW,MAAM,CAAC,MAAM,IAAI;AACnC,iBAAO;AACP,iBAAO,MAAM,KAAK;AAChB,gBAAI,YAAY,KAAK,WAAW,GAAG,CAAC,GAAG;AACrC;AAAA,YACF;AACA;AAAA,UACF;AACA,kBAAQ,KAAK,UAAU,OAAO,GAAG;AACjC,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,KAAK,WAAW,MAAM,CAAC,MAAM,IAAI;AACnC,iBAAO;AACP,cAAI,aAAa,MAAM;AACvB,cAAI,gBAAgB;AACpB,iBAAO,MAAM,YAAY;AACvB,gBAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,gBAAI,OAAO,MAAM,KAAK,WAAW,MAAM,CAAC,MAAM,IAAI;AAChD,qBAAO;AACP,8BAAgB;AAChB;AAAA,YACF;AACA;AACA,gBAAI,YAAY,EAAE,GAAG;AACnB,kBAAI,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI;AAC5C;AAAA,cACF;AACA;AACA,qCAAuB;AAAA,YACzB;AAAA,UACF;AACA,cAAI,CAAC,eAAe;AAClB;AACA,wBAAY;AAAA,UACd;AACA,kBAAQ,KAAK,UAAU,OAAO,GAAG;AACjC,iBAAO,QAAQ;AAAA,QACjB;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,YAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACjD,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,WAAW;AACpB,eAAO,QAAQ;AAAA,MACjB;AACE,eAAO,MAAM,OAAO,0BAA0B,IAAI,GAAG;AACnD;AACA,iBAAO,KAAK,WAAW,GAAG;AAAA,QAC5B;AACA,YAAI,gBAAgB,KAAK;AACvB,kBAAQ,KAAK,UAAU,aAAa,GAAG;AACvC,kBAAQ,OAAO;AAAA,YACb,KAAK;AACH,qBAAO,QAAQ;AAAA,YACjB,KAAK;AACH,qBAAO,QAAQ;AAAA,YACjB,KAAK;AACH,qBAAO,QAAQ;AAAA,UACnB;AACA,iBAAO,QAAQ;AAAA,QACjB;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,eAAO,QAAQ;AAAA,IACnB;AAAA,EACF;AACA,WAAS,0BAA0B,MAAM;AACvC,QAAI,aAAa,IAAI,KAAK,YAAY,IAAI,GAAG;AAC3C,aAAO;AAAA,IACT;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,WAAS,oBAAoB;AAC3B,QAAI;AACJ,OAAG;AACD,eAAS,SAAS;AAAA,IACpB,SAAS,UAAU,MAAM,UAAU;AACnC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA,aAAa,WAAW;AACtB,aAAO;AAAA,IACT;AAAA,IACA,MAAM,eAAe,oBAAoB;AAAA,IACzC,UAAU,WAAW;AACnB,aAAO;AAAA,IACT;AAAA,IACA,eAAe,WAAW;AACxB,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB,WAAW;AACzB,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB,WAAW;AACzB,aAAO,MAAM;AAAA,IACf;AAAA,IACA,mBAAmB,WAAW;AAC5B,aAAO;AAAA,IACT;AAAA,IACA,wBAAwB,WAAW;AACjC,aAAO,cAAc;AAAA,IACvB;AAAA,IACA,eAAe,WAAW;AACxB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,aAAa,IAAI;AACxB,SAAO,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM,OAAO,MAAM,OAAO,OAAO,OAAO,QAAQ,MAAM,QAAQ,MAAM,QAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,SAAS,OAAO;AAC1K;AACA,SAAS,YAAY,IAAI;AACvB,SAAO,OAAO,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO;AACzD;AACA,SAAS,QAAQ,IAAI;AACnB,SAAO,MAAM,MAAM,MAAM;AAC3B;AAGA,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,gBAAc,UAAU;AAAA,IACtB,oBAAoB;AAAA,EACtB;AACF,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAGtC,IAAI,iBAAiB;AAGrB,SAAS,0BAA0B,iBAAiB;AAClD,SAAO;AAAA,IACL,iBAAiB,MAAM,IAAI,UAAU,MAAM,MAAM,OAAO,IAAI;AAAA,IAC5D,UAAU,CAAC,MAAM,UAAU,SAAS,iBAAiB,MAAM,KAAK;AAAA,EAClE;AACF;AACA,IAAI,qBAAqB;AACzB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,sBAAsB;AAC1B,IAAI,sBAAsB;AAC1B,IAAI,qBAAqB;AACzB,IAAI,eAAe,MAAM;AAAA,EACvB,YAAY,QAAQ,MAAM;AACxB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO,IAAI,SAAS;AAClB,QAAI,SAAS;AACX,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,SAAS,MAAM;AACzB,WAAO,IAAI,aAAa,SAAS,IAAI;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,GAAG,GAAG;AAClB,QAAI,CAAC,KAAK,CAAC,GAAG;AACZ,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,CAAC,GAAG;AACZ,aAAO;AAAA,IACT;AACA,WAAO,KAAK,GAAG;AACb,UAAI,MAAM,GAAG;AACX,eAAO;AAAA,MACT;AACA,UAAI,EAAE,SAAS,EAAE,MAAM;AACrB,eAAO;AAAA,MACT;AACA,UAAI,EAAE;AACN,UAAI,EAAE;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAI,YAAY,MAAM;AAAA,EAKpB,YAAY,OAAO,WAAW,cAAc,SAAS;AAJrD;AACA;AACA;AACA;AAEE,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,UAAU,KAAK,QAAQ,KAAK,WAAW,KAAK,cAAc,KAAK,OAAO;AAAA,EACnF;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,EAAE,iBAAiB,YAAY;AAC3C,aAAO;AAAA,IACT;AACA,WAAO,KAAK,cAAc,MAAM,aAAa,KAAK,iBAAiB,MAAM,gBAAgB,aAAa,OAAO,KAAK,SAAS,MAAM,OAAO;AAAA,EAC1I;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,SAAS;AAAA,EAChB;AACF;AACA,SAAS,SAAS,UAAU,MAAM,OAAO,cAAc,GAAG;AACxD,MAAI,6BAA6B;AACjC,MAAI,eAAe;AACnB,UAAQ,MAAM,WAAW;AAAA,IACvB,KAAK;AACH,aAAO,MAAM;AACb,mCAA6B;AAC7B;AAAA,IACF,KAAK;AACH,aAAO,OAAO;AACd,mCAA6B;AAC7B;AAAA,EACJ;AACA,QAAM,UAAU,eAAe,IAAI;AACnC,MAAI,eAAe,MAAM;AACzB,MAAI,UAAU,MAAM;AACpB,QAAM,MAAM;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,UAAU,MAAM,MAAM;AAAA,EACxB;AACA,SAAO,MAAM;AACX,QAAI,SAAS,cAAc,QAAQ,YAAY;AAC/C,QAAI,OAAO;AACX,UAAM,OAAO,QAAQ,KAAK;AAC1B,QAAI,SAAS,IAAc;AACzB;AAAA,IACF;AACA,QAAI,WAAW,cAAc,QAAQ,YAAY,GAAG;AAClD,YAAM,IAAI,MAAM,qDAAqD,KAAK,OAAO,QAAQ,YAAY,GAAG,CAAC,CAAC;AAAA,IAC5G;AACA,QAAI,cAAc;AAChB,gBAAU;AAAA,IACZ;AACA,mBAAe,6BAA6B;AAC5C,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,kBAAU,aAAa;AAAA,UAAK;AAAA,UAAS;AAAA;AAAA,QAAc;AACnD,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,kBAAU,aAAa,IAAI,OAAO;AAClC,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,kBAAU,aAAa;AAAA,UAAK;AAAA,UAAS;AAAA;AAAA,QAAa;AAClD,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,kBAAU,aAAa,IAAI,OAAO;AAClC,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,cAAM,gBAAgB,UAAU,QAAQ,OAAO;AAC/C,cAAM,UAAU,kBAAkB;AAClC,eAAO,gBAAgB,UAAU,qBAAqB;AACtD,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,IACJ;AACA,QAAI,UAAU;AACZ,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AACP;AAAA,QACF,KAAK;AACH,iBAAO;AACP;AAAA,MACJ;AAAA,IACF;AACA,QAAI,WAAW,IAAI,UAAU,MAAM,aAAa,GAAG,QAAQ,cAAc,GAAG,cAAc,OAAO;AACjG,QAAI,OAAO,KAAK;AAAA,MACd,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAGA,IAAI,yBAAyB,cAAc,mBAAmB;AAAA,EAC5D,YAAY,YAAY,QAAQ,UAAU;AACxC,UAAM,YAAY,QAAQ,SAAS,WAAW;AAC9C,SAAK,aAAa,KAAK,2BAA2B,OAAO,mBAAmB,CAAC,UAAU;AACrF,WAAK,aAAa,MAAM,GAAG;AAAA,IAC7B,CAAC,CAAC;AACF,SAAK,aAAa,KAAK,2BAA2B,OAAO,yBAAyB,CAAC,UAAU;AAC3F,WAAK,aAAa,MAAM,MAAM,GAAG;AAAA,IACnC,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,aAAa,UAAU;AACrB,SAAK,QAAQ,EAAE,KAAK,CAAC,WAAW;AAC9B,aAAO,YAAY,SAAS,SAAS,CAAC;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AACA,SAAS,UAAU,UAAU;AAC3B,QAAM,cAAc,CAAC;AACrB,QAAM,YAAY,CAAC;AACnB,QAAM,SAAS,IAAI,cAAc,QAAQ;AACzC,cAAY,KAAK,MAAM;AACvB,QAAM,SAAS,IAAI,SAAS;AAC1B,WAAO,OAAO,yBAAyB,GAAG,IAAI;AAAA,EAChD;AACA,WAAS,oBAAoB;AAC3B,UAAM,EAAE,YAAY,mBAAmB,mBAAmB,IAAI;AAC9D,eAAW,SAAS;AACpB,QAAI,mBAAmB,yBAAyB;AAC9C,gBAAU,KAAK,2BAA2B,UAAU,uCAAuC,YAAY,IAAI,+BAA+B,MAAM,CAAC,CAAC;AAAA,IACpJ;AACA,QAAI,mBAAmB,8BAA8B;AACnD,gBAAU,KAAK,2BAA2B,UAAU,4CAA4C,YAAY,IAAI,oCAAoC,MAAM,CAAC,CAAC;AAAA,IAC9J;AACA,QAAI,mBAAmB,iBAAiB;AACtC,gBAAU,KAAK,2BAA2B,UAAU,+BAA+B,YAAY,IAAI,kBAAkB,QAAQ,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC;AAAA,IAChJ;AACA,QAAI,mBAAmB,QAAQ;AAC7B,gBAAU,KAAK,2BAA2B,UAAU,sBAAsB,YAAY,IAAI,aAAa,MAAM,CAAC,CAAC;AAAA,IACjH;AACA,QAAI,mBAAmB,iBAAiB;AACtC,gBAAU,KAAK,2BAA2B,UAAU,+BAA+B,YAAY,IAAI,sBAAsB,MAAM,CAAC,CAAC;AAAA,IACnI;AACA,QAAI,mBAAmB,QAAQ;AAC7B,gBAAU,KAAK,2BAA2B,UAAU,kBAAkB,YAAY,0BAA0B,IAAI,CAAC,CAAC;AAAA,IACpH;AACA,QAAI,mBAAmB,QAAQ;AAC7B,gBAAU,KAAK,2BAA2B,UAAU,sBAAsB,YAAY,IAAI,qBAAqB,MAAM,CAAC,CAAC;AAAA,IACzH;AACA,QAAI,mBAAmB,eAAe;AACpC,gBAAU,KAAK,2BAA2B,UAAU,6BAA6B,YAAY,IAAI,oBAAoB,MAAM,CAAC,CAAC;AAAA,IAC/H;AACA,QAAI,mBAAmB,aAAa;AAClC,gBAAU,KAAK,IAAI,uBAAuB,YAAY,QAAQ,QAAQ,CAAC;AAAA,IACzE;AACA,QAAI,mBAAmB,iBAAiB;AACtC,gBAAU,KAAK,2BAA2B,UAAU,+BAA+B,YAAY,IAAI,sBAAsB,MAAM,CAAC,CAAC;AAAA,IACnI;AAAA,EACF;AACA,oBAAkB;AAClB,cAAY,KAAK,2BAA2B,UAAU,yBAAyB,SAAS,YAAY,qBAAqB,CAAC;AAC1H,MAAI,oBAAoB,SAAS;AACjC,WAAS,YAAY,CAAC,gBAAgB;AACpC,QAAI,YAAY,sBAAsB,mBAAmB;AACvD,0BAAoB,YAAY;AAChC,wBAAkB;AAAA,IACpB;AAAA,EACF,CAAC;AACD,cAAY,KAAK,aAAa,SAAS,CAAC;AACxC,SAAO,aAAa,WAAW;AACjC;AACA,SAAS,aAAa,aAAa;AACjC,SAAO,EAAE,SAAS,MAAM,WAAW,WAAW,EAAE;AAClD;AACA,SAAS,WAAW,aAAa;AAC/B,SAAO,YAAY,QAAQ;AACzB,gBAAY,IAAI,EAAE,QAAQ;AAAA,EAC5B;AACF;AACA,IAAI,wBAAwB;AAAA,EAC1B,aAAa;AAAA,EACb,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC7C;AACF;", "names": []}