{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/liquid/liquid.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/liquid/liquid.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{{\", \"}}\"],\n    [\"{%\", \"%}\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"%\", close: \"%\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(`<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(`<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  builtinTags: [\n    \"if\",\n    \"else\",\n    \"elseif\",\n    \"endif\",\n    \"render\",\n    \"assign\",\n    \"capture\",\n    \"endcapture\",\n    \"case\",\n    \"endcase\",\n    \"comment\",\n    \"endcomment\",\n    \"cycle\",\n    \"decrement\",\n    \"for\",\n    \"endfor\",\n    \"include\",\n    \"increment\",\n    \"layout\",\n    \"raw\",\n    \"endraw\",\n    \"render\",\n    \"tablerow\",\n    \"endtablerow\",\n    \"unless\",\n    \"endunless\"\n  ],\n  builtinFilters: [\n    \"abs\",\n    \"append\",\n    \"at_least\",\n    \"at_most\",\n    \"capitalize\",\n    \"ceil\",\n    \"compact\",\n    \"date\",\n    \"default\",\n    \"divided_by\",\n    \"downcase\",\n    \"escape\",\n    \"escape_once\",\n    \"first\",\n    \"floor\",\n    \"join\",\n    \"json\",\n    \"last\",\n    \"lstrip\",\n    \"map\",\n    \"minus\",\n    \"modulo\",\n    \"newline_to_br\",\n    \"plus\",\n    \"prepend\",\n    \"remove\",\n    \"remove_first\",\n    \"replace\",\n    \"replace_first\",\n    \"reverse\",\n    \"round\",\n    \"rstrip\",\n    \"size\",\n    \"slice\",\n    \"sort\",\n    \"sort_natural\",\n    \"split\",\n    \"strip\",\n    \"strip_html\",\n    \"strip_newlines\",\n    \"times\",\n    \"truncate\",\n    \"truncatewords\",\n    \"uniq\",\n    \"upcase\",\n    \"url_decode\",\n    \"url_encode\",\n    \"where\"\n  ],\n  constants: [\"true\", \"false\"],\n  operators: [\"==\", \"!=\", \">\", \"<\", \">=\", \"<=\"],\n  symbol: /[=><!]+/,\n  identifier: /[a-zA-Z_][\\w]*/,\n  tokenizer: {\n    root: [\n      [/\\{\\%\\s*comment\\s*\\%\\}/, \"comment.start.liquid\", \"@comment\"],\n      [/\\{\\{/, { token: \"@rematch\", switchTo: \"@liquidState.root\" }],\n      [/\\{\\%/, { token: \"@rematch\", switchTo: \"@liquidState.root\" }],\n      [/(<)([\\w\\-]+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)([\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/\\{/, \"delimiter.html\"],\n      [/[^<{]+/]\n    ],\n    comment: [\n      [/\\{\\%\\s*endcomment\\s*\\%\\}/, \"comment.end.liquid\", \"@pop\"],\n      [/./, \"comment.content.liquid\"]\n    ],\n    otherTag: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@liquidState.otherTag\"\n        }\n      ],\n      [\n        /\\{\\%/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@liquidState.otherTag\"\n        }\n      ],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n    ],\n    liquidState: [\n      [/\\{\\{/, \"delimiter.output.liquid\"],\n      [/\\}\\}/, { token: \"delimiter.output.liquid\", switchTo: \"@$S2.$S3\" }],\n      [/\\{\\%/, \"delimiter.tag.liquid\"],\n      [/raw\\s*\\%\\}/, \"delimiter.tag.liquid\", \"@liquidRaw\"],\n      [/\\%\\}/, { token: \"delimiter.tag.liquid\", switchTo: \"@$S2.$S3\" }],\n      { include: \"liquidRoot\" }\n    ],\n    liquidRaw: [\n      [/^(?!\\{\\%\\s*endraw\\s*\\%\\}).+/],\n      [/\\{\\%/, \"delimiter.tag.liquid\"],\n      [/@identifier/],\n      [/\\%\\}/, { token: \"delimiter.tag.liquid\", next: \"@root\" }]\n    ],\n    liquidRoot: [\n      [/\\d+(\\.\\d+)?/, \"number.liquid\"],\n      [/\"[^\"]*\"/, \"string.liquid\"],\n      [/'[^']*'/, \"string.liquid\"],\n      [/\\s+/],\n      [\n        /@symbol/,\n        {\n          cases: {\n            \"@operators\": \"operator.liquid\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/\\./],\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@constants\": \"keyword.liquid\",\n            \"@builtinFilters\": \"predefined.liquid\",\n            \"@builtinTags\": \"predefined.liquid\",\n            \"@default\": \"variable.liquid\"\n          }\n        }\n      ],\n      [/[^}|%]/, \"variable.liquid\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,CAAC,QAAQ,KAAK;AAAA,IACd,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,MAAM,IAAI;AAAA,IACX,CAAC,MAAM,IAAI;AAAA,IACX,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,MACE,YAAY,IAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,wCAAwC,GAAG;AAAA,MACpG,WAAW;AAAA,MACX,QAAQ;AAAA,QACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,MAClE;AAAA,IACF;AAAA,IACA;AAAA,MACE,YAAY,IAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,wCAAwC,GAAG;AAAA,MACpG,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,IACnF;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,QAAQ,OAAO;AAAA,EAC3B,WAAW,CAAC,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI;AAAA,EAC5C,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,yBAAyB,wBAAwB,UAAU;AAAA,MAC5D,CAAC,QAAQ,EAAE,OAAO,YAAY,UAAU,oBAAoB,CAAC;AAAA,MAC7D,CAAC,QAAQ,EAAE,OAAO,YAAY,UAAU,oBAAoB,CAAC;AAAA,MAC7D,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MACtE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC5E,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC/E,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,MAAM,gBAAgB;AAAA,MACvB,CAAC,QAAQ;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,CAAC,4BAA4B,sBAAsB,MAAM;AAAA,MACzD,CAAC,KAAK,wBAAwB;AAAA,IAChC;AAAA,IACA,UAAU;AAAA,MACR;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,QAAQ,kBAAkB,MAAM;AAAA,MACjC,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,IACf;AAAA,IACA,aAAa;AAAA,MACX,CAAC,QAAQ,yBAAyB;AAAA,MAClC,CAAC,QAAQ,EAAE,OAAO,2BAA2B,UAAU,WAAW,CAAC;AAAA,MACnE,CAAC,QAAQ,sBAAsB;AAAA,MAC/B,CAAC,cAAc,wBAAwB,YAAY;AAAA,MACnD,CAAC,QAAQ,EAAE,OAAO,wBAAwB,UAAU,WAAW,CAAC;AAAA,MAChE,EAAE,SAAS,aAAa;AAAA,IAC1B;AAAA,IACA,WAAW;AAAA,MACT,CAAC,6BAA6B;AAAA,MAC9B,CAAC,QAAQ,sBAAsB;AAAA,MAC/B,CAAC,aAAa;AAAA,MACd,CAAC,QAAQ,EAAE,OAAO,wBAAwB,MAAM,QAAQ,CAAC;AAAA,IAC3D;AAAA,IACA,YAAY;AAAA,MACV,CAAC,eAAe,eAAe;AAAA,MAC/B,CAAC,WAAW,eAAe;AAAA,MAC3B,CAAC,WAAW,eAAe;AAAA,MAC3B,CAAC,KAAK;AAAA,MACN;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,IAAI;AAAA,MACL;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,UAAU,iBAAiB;AAAA,IAC9B;AAAA,EACF;AACF;", "names": []}