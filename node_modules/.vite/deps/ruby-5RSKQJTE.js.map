{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/ruby/ruby.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/ruby/ruby.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"=begin\", \"=end\"]\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: new RegExp(`^\\\\s*((begin|class|(private|protected)\\\\s+def|def|else|elsif|ensure|for|if|module|rescue|unless|until|when|while|case)|([^#]*\\\\sdo\\\\b)|([^#]*=\\\\s*(case|if|unless)))\\\\b([^#\\\\{;]|(\"|'|/).*\\\\4)*(#.*)?$`),\n    decreaseIndentPattern: new RegExp(\"^\\\\s*([}\\\\]]([,)]?\\\\s*(#|$)|\\\\.[a-zA-Z_]\\\\w*\\\\b)|(end|rescue|ensure|else|elsif|when)\\\\b)\")\n  }\n};\nvar language = {\n  tokenPostfix: \".ruby\",\n  keywords: [\n    \"__LINE__\",\n    \"__ENCODING__\",\n    \"__FILE__\",\n    \"BEGIN\",\n    \"END\",\n    \"alias\",\n    \"and\",\n    \"begin\",\n    \"break\",\n    \"case\",\n    \"class\",\n    \"def\",\n    \"defined?\",\n    \"do\",\n    \"else\",\n    \"elsif\",\n    \"end\",\n    \"ensure\",\n    \"for\",\n    \"false\",\n    \"if\",\n    \"in\",\n    \"module\",\n    \"next\",\n    \"nil\",\n    \"not\",\n    \"or\",\n    \"redo\",\n    \"rescue\",\n    \"retry\",\n    \"return\",\n    \"self\",\n    \"super\",\n    \"then\",\n    \"true\",\n    \"undef\",\n    \"unless\",\n    \"until\",\n    \"when\",\n    \"while\",\n    \"yield\"\n  ],\n  keywordops: [\"::\", \"..\", \"...\", \"?\", \":\", \"=>\"],\n  builtins: [\n    \"require\",\n    \"public\",\n    \"private\",\n    \"include\",\n    \"extend\",\n    \"attr_reader\",\n    \"protected\",\n    \"private_class_method\",\n    \"protected_class_method\",\n    \"new\"\n  ],\n  declarations: [\n    \"module\",\n    \"class\",\n    \"def\",\n    \"case\",\n    \"do\",\n    \"begin\",\n    \"for\",\n    \"if\",\n    \"while\",\n    \"until\",\n    \"unless\"\n  ],\n  linedecls: [\"def\", \"case\", \"do\", \"begin\", \"for\", \"if\", \"while\", \"until\", \"unless\"],\n  operators: [\n    \"^\",\n    \"&\",\n    \"|\",\n    \"<=>\",\n    \"==\",\n    \"===\",\n    \"!~\",\n    \"=~\",\n    \">\",\n    \">=\",\n    \"<\",\n    \"<=\",\n    \"<<\",\n    \">>\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"**\",\n    \"~\",\n    \"+@\",\n    \"-@\",\n    \"[]\",\n    \"[]=\",\n    \"`\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"**=\",\n    \"/=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \"&=\",\n    \"&&=\",\n    \"||=\",\n    \"|=\"\n  ],\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%\\.]+/,\n  escape: /(?:[abefnrstv\\\\\"'\\n\\r]|[0-7]{1,3}|x[0-9A-Fa-f]{1,2}|u[0-9A-Fa-f]{4})/,\n  escapes: /\\\\(?:C\\-(@escape|.)|c(@escape|.)|@escape)/,\n  decpart: /\\d(_?\\d)*/,\n  decimal: /0|@decpart/,\n  delim: /[^a-zA-Z0-9\\s\\n\\r]/,\n  heredelim: /(?:\\w+|'[^']*'|\"[^\"]*\"|`[^`]*`)/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[AzZbBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})?/,\n  tokenizer: {\n    root: [\n      [\n        /^(\\s*)([a-z_]\\w*[!?=]?)/,\n        [\n          \"white\",\n          {\n            cases: {\n              \"for|until|while\": {\n                token: \"keyword.$2\",\n                next: \"@dodecl.$2\"\n              },\n              \"@declarations\": {\n                token: \"keyword.$2\",\n                next: \"@root.$2\"\n              },\n              end: { token: \"keyword.$S2\", next: \"@pop\" },\n              \"@keywords\": \"keyword\",\n              \"@builtins\": \"predefined\",\n              \"@default\": \"identifier\"\n            }\n          }\n        ]\n      ],\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            \"if|unless|while|until\": {\n              token: \"keyword.$0x\",\n              next: \"@modifier.$0x\"\n            },\n            for: { token: \"keyword.$2\", next: \"@dodecl.$2\" },\n            \"@linedecls\": { token: \"keyword.$0\", next: \"@root.$0\" },\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z][\\w]*[!?=]?/, \"constructor.identifier\"],\n      [/\\$[\\w]*/, \"global.constant\"],\n      [/@[\\w]*/, \"namespace.instance.identifier\"],\n      [/@@@[\\w]*/, \"namespace.class.identifier\"],\n      [/<<[-~](@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/[ \\t\\r\\n]+<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/^<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      { include: \"@whitespace\" },\n      [/\"/, { token: \"string.d.delim\", next: '@dstring.d.\"' }],\n      [/'/, { token: \"string.sq.delim\", next: \"@sstring.sq\" }],\n      [/%([rsqxwW]|Q?)/, { token: \"@rematch\", next: \"pstring\" }],\n      [/`/, { token: \"string.x.delim\", next: \"@dstring.x.`\" }],\n      [/:(\\w|[$@])\\w*[!?=]?/, \"string.s\"],\n      [/:\"/, { token: \"string.s.delim\", next: '@dstring.s.\"' }],\n      [/:'/, { token: \"string.s.delim\", next: \"@sstring.s\" }],\n      [/\\/(?=(\\\\\\/|[^\\/\\n])+\\/)/, { token: \"regexp.delim\", next: \"@regexp\" }],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@keywordops\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/[;,]/, \"delimiter\"],\n      [/0[xX][0-9a-fA-F](_?[0-9a-fA-F])*/, \"number.hex\"],\n      [/0[_oO][0-7](_?[0-7])*/, \"number.octal\"],\n      [/0[bB][01](_?[01])*/, \"number.binary\"],\n      [/0[dD]@decpart/, \"number\"],\n      [\n        /@decimal((\\.@decpart)?([eE][\\-+]?@decpart)?)/,\n        {\n          cases: {\n            $1: \"number.float\",\n            \"@default\": \"number\"\n          }\n        }\n      ]\n    ],\n    dodecl: [\n      [/^/, { token: \"\", switchTo: \"@root.$S2\" }],\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            do: { token: \"keyword\", switchTo: \"@root.$S2\" },\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    modifier: [\n      [/^/, \"\", \"@pop\"],\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            \"then|else|elsif|do\": {\n              token: \"keyword\",\n              switchTo: \"@root.$S2\"\n            },\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    sstring: [\n      [/[^\\\\']+/, \"string.$S2\"],\n      [/\\\\\\\\|\\\\'|\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.invalid\"],\n      [/'/, { token: \"string.$S2.delim\", next: \"@pop\" }]\n    ],\n    dstring: [\n      [/[^\\\\`\"#]+/, \"string.$S2\"],\n      [/#/, \"string.$S2.escape\", \"@interpolated\"],\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/@escapes/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape.invalid\"],\n      [\n        /[`\"]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    heredoc: [\n      [\n        /^(\\s*)(@heredelim)$/,\n        {\n          cases: {\n            \"$2==$S2\": [\"string.heredoc\", { token: \"string.heredoc.delimiter\", next: \"@pop\" }],\n            \"@default\": [\"string.heredoc\", \"string.heredoc\"]\n          }\n        }\n      ],\n      [/.*/, \"string.heredoc\"]\n    ],\n    interpolated: [\n      [/\\$\\w*/, \"global.constant\", \"@pop\"],\n      [/@\\w*/, \"namespace.class.identifier\", \"@pop\"],\n      [/@@@\\w*/, \"namespace.instance.identifier\", \"@pop\"],\n      [\n        /[{]/,\n        {\n          token: \"string.escape.curly\",\n          switchTo: \"@interpolated_compound\"\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n    ],\n    interpolated_compound: [\n      [/[}]/, { token: \"string.escape.curly\", next: \"@pop\" }],\n      { include: \"@root\" }\n    ],\n    pregexp: [\n      { include: \"@whitespace\" },\n      [\n        /[^\\(\\{\\[\\\\]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"regexp.delim\", next: \"@pop\" },\n            \"$#==$S2\": { token: \"regexp.delim\", next: \"@push\" },\n            \"~[)}\\\\]]\": \"@brackets.regexp.escape.control\",\n            \"~@regexpctl\": \"regexp.escape.control\",\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexcontrol\" }\n    ],\n    regexp: [\n      { include: \"@regexcontrol\" },\n      [/[^\\\\\\/]/, \"regexp\"],\n      [\"/[ixmp]*\", { token: \"regexp.delim\" }, \"@pop\"]\n    ],\n    regexcontrol: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\n          \"@brackets.regexp.escape.control\",\n          \"regexp.escape.control\",\n          \"@brackets.regexp.escape.control\"\n        ]\n      ],\n      [\n        /(\\[)(\\^?)/,\n        [\"@brackets.regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?[:=!])/, [\"@brackets.regexp.escape.control\", \"regexp.escape.control\"]],\n      [/\\(\\?#/, { token: \"regexp.escape.control\", next: \"@regexpcomment\" }],\n      [/[()]/, \"@brackets.regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/#/, \"regexp.escape\", \"@interpolated\"]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [/\\]/, \"@brackets.regexp.escape.control\", \"@pop\"]\n    ],\n    regexpcomment: [\n      [/[^)]+/, \"comment\"],\n      [/\\)/, { token: \"regexp.escape.control\", next: \"@pop\" }]\n    ],\n    pstring: [\n      [/%([qws])\\(/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.(.)\" }],\n      [/%([qws])\\[/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.[.]\" }],\n      [/%([qws])\\{/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.{.}\" }],\n      [/%([qws])</, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.<.>\" }],\n      [/%([qws])(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.$2.$2\" }],\n      [/%r\\(/, { token: \"regexp.delim\", switchTo: \"@pregexp.(.)\" }],\n      [/%r\\[/, { token: \"regexp.delim\", switchTo: \"@pregexp.[.]\" }],\n      [/%r\\{/, { token: \"regexp.delim\", switchTo: \"@pregexp.{.}\" }],\n      [/%r</, { token: \"regexp.delim\", switchTo: \"@pregexp.<.>\" }],\n      [/%r(@delim)/, { token: \"regexp.delim\", switchTo: \"@pregexp.$1.$1\" }],\n      [/%(x|W|Q?)\\(/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.(.)\" }],\n      [/%(x|W|Q?)\\[/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.[.]\" }],\n      [/%(x|W|Q?)\\{/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.{.}\" }],\n      [/%(x|W|Q?)</, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.<.>\" }],\n      [/%(x|W|Q?)(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.$2.$2\" }],\n      [/%([rqwsxW]|Q?)./, { token: \"invalid\", next: \"@pop\" }],\n      [/./, { token: \"invalid\", next: \"@pop\" }]\n    ],\n    qstring: [\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape\"],\n      [\n        /./,\n        {\n          cases: {\n            \"$#==$S4\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@push\" },\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    qqstring: [[/#/, \"string.$S2.escape\", \"@interpolated\"], { include: \"@qstring\" }],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/^\\s*=begin\\b/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^=]+/, \"comment\"],\n      [/^\\s*=begin\\b/, \"comment.invalid\"],\n      [/^\\s*=end\\b.*/, \"comment\", \"@pop\"],\n      [/[=]/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,UAAU,MAAM;AAAA,EACjC;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,uBAAuB,IAAI,OAAO,wMAAwM;AAAA,IAC1O,uBAAuB,IAAI,OAAO,0FAA0F;AAAA,EAC9H;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,YAAY,CAAC,MAAM,MAAM,OAAO,KAAK,KAAK,IAAI;AAAA,EAC9C,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,OAAO,QAAQ,MAAM,SAAS,OAAO,MAAM,SAAS,SAAS,QAAQ;AAAA,EACjF,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,EACrD;AAAA,EACA,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,IACT,MAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,mBAAmB;AAAA,gBACjB,OAAO;AAAA,gBACP,MAAM;AAAA,cACR;AAAA,cACA,iBAAiB;AAAA,gBACf,OAAO;AAAA,gBACP,MAAM;AAAA,cACR;AAAA,cACA,KAAK,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA,cAC1C,aAAa;AAAA,cACb,aAAa;AAAA,cACb,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,yBAAyB;AAAA,cACvB,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,KAAK,EAAE,OAAO,cAAc,MAAM,aAAa;AAAA,YAC/C,cAAc,EAAE,OAAO,cAAc,MAAM,WAAW;AAAA,YACtD,KAAK,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA,YAC1C,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,oBAAoB,wBAAwB;AAAA,MAC7C,CAAC,WAAW,iBAAiB;AAAA,MAC7B,CAAC,UAAU,+BAA+B;AAAA,MAC1C,CAAC,YAAY,4BAA4B;AAAA,MACzC,CAAC,wBAAwB,EAAE,OAAO,4BAA4B,MAAM,cAAc,CAAC;AAAA,MACnF,CAAC,8BAA8B,EAAE,OAAO,4BAA4B,MAAM,cAAc,CAAC;AAAA,MACzF,CAAC,qBAAqB,EAAE,OAAO,4BAA4B,MAAM,cAAc,CAAC;AAAA,MAChF,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,KAAK,EAAE,OAAO,kBAAkB,MAAM,eAAe,CAAC;AAAA,MACvD,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,cAAc,CAAC;AAAA,MACvD,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC;AAAA,MACzD,CAAC,KAAK,EAAE,OAAO,kBAAkB,MAAM,eAAe,CAAC;AAAA,MACvD,CAAC,uBAAuB,UAAU;AAAA,MAClC,CAAC,MAAM,EAAE,OAAO,kBAAkB,MAAM,eAAe,CAAC;AAAA,MACxD,CAAC,MAAM,EAAE,OAAO,kBAAkB,MAAM,aAAa,CAAC;AAAA,MACtD,CAAC,2BAA2B,EAAE,OAAO,gBAAgB,MAAM,UAAU,CAAC;AAAA,MACtE,CAAC,cAAc,WAAW;AAAA,MAC1B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,eAAe;AAAA,YACf,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,QAAQ,WAAW;AAAA,MACpB,CAAC,oCAAoC,YAAY;AAAA,MACjD,CAAC,yBAAyB,cAAc;AAAA,MACxC,CAAC,sBAAsB,eAAe;AAAA,MACtC,CAAC,iBAAiB,QAAQ;AAAA,MAC1B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,IAAI;AAAA,YACJ,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,KAAK,EAAE,OAAO,IAAI,UAAU,YAAY,CAAC;AAAA,MAC1C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,KAAK,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA,YAC1C,IAAI,EAAE,OAAO,WAAW,UAAU,YAAY;AAAA,YAC9C,cAAc;AAAA,cACZ,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,YACA,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,QAAQ;AAAA,IACrB;AAAA,IACA,UAAU;AAAA,MACR,CAAC,KAAK,IAAI,MAAM;AAAA,MAChB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,KAAK,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA,YAC1C,sBAAsB;AAAA,cACpB,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,YACA,cAAc;AAAA,cACZ,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,YACA,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,QAAQ;AAAA,IACrB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,gBAAgB,mBAAmB;AAAA,MACpC,CAAC,OAAO,oBAAoB;AAAA,MAC5B,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,aAAa,YAAY;AAAA,MAC1B,CAAC,KAAK,qBAAqB,eAAe;AAAA,MAC1C,CAAC,OAAO,mBAAmB;AAAA,MAC3B,CAAC,YAAY,mBAAmB;AAAA,MAChC,CAAC,OAAO,2BAA2B;AAAA,MACnC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,CAAC,kBAAkB,EAAE,OAAO,4BAA4B,MAAM,OAAO,CAAC;AAAA,YACjF,YAAY,CAAC,kBAAkB,gBAAgB;AAAA,UACjD;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,MAAM,gBAAgB;AAAA,IACzB;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,SAAS,mBAAmB,MAAM;AAAA,MACnC,CAAC,QAAQ,8BAA8B,MAAM;AAAA,MAC7C,CAAC,UAAU,iCAAiC,MAAM;AAAA,MAClD;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,IAAI,IAAI,MAAM;AAAA,IACjB;AAAA,IACA,uBAAuB;AAAA,MACrB,CAAC,OAAO,EAAE,OAAO,uBAAuB,MAAM,OAAO,CAAC;AAAA,MACtD,EAAE,SAAS,QAAQ;AAAA,IACrB;AAAA,IACA,SAAS;AAAA,MACP,EAAE,SAAS,cAAc;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,gBAAgB,MAAM,OAAO;AAAA,YACjD,WAAW,EAAE,OAAO,gBAAgB,MAAM,QAAQ;AAAA,YAClD,YAAY;AAAA,YACZ,eAAe;AAAA,YACf,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,gBAAgB;AAAA,IAC7B;AAAA,IACA,QAAQ;AAAA,MACN,EAAE,SAAS,gBAAgB;AAAA,MAC3B,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,EAAE,OAAO,eAAe,GAAG,MAAM;AAAA,IAChD;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,mCAAmC,EAAE,OAAO,yBAAyB,MAAM,cAAc,CAAC;AAAA,MAC7F;AAAA,MACA,CAAC,iBAAiB,CAAC,mCAAmC,uBAAuB,CAAC;AAAA,MAC9E,CAAC,SAAS,EAAE,OAAO,yBAAyB,MAAM,iBAAiB,CAAC;AAAA,MACpE,CAAC,QAAQ,iCAAiC;AAAA,MAC1C,CAAC,cAAc,uBAAuB;AAAA,MACtC,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,cAAc,eAAe;AAAA,MAC9B,CAAC,QAAQ,gBAAgB;AAAA,MACzB,CAAC,KAAK,iBAAiB,eAAe;AAAA,IACxC;AAAA,IACA,YAAY;AAAA,MACV,CAAC,KAAK,uBAAuB;AAAA,MAC7B,CAAC,MAAM,gBAAgB;AAAA,MACvB,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,cAAc,eAAe;AAAA,MAC9B,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,mCAAmC,MAAM;AAAA,IAClD;AAAA,IACA,eAAe;AAAA,MACb,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,MAAM,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,cAAc,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,MACxE,CAAC,cAAc,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,MACxE,CAAC,cAAc,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,MACxE,CAAC,aAAa,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,MACvE,CAAC,oBAAoB,EAAE,OAAO,mBAAmB,UAAU,oBAAoB,CAAC;AAAA,MAChF,CAAC,QAAQ,EAAE,OAAO,gBAAgB,UAAU,eAAe,CAAC;AAAA,MAC5D,CAAC,QAAQ,EAAE,OAAO,gBAAgB,UAAU,eAAe,CAAC;AAAA,MAC5D,CAAC,QAAQ,EAAE,OAAO,gBAAgB,UAAU,eAAe,CAAC;AAAA,MAC5D,CAAC,OAAO,EAAE,OAAO,gBAAgB,UAAU,eAAe,CAAC;AAAA,MAC3D,CAAC,cAAc,EAAE,OAAO,gBAAgB,UAAU,iBAAiB,CAAC;AAAA,MACpE,CAAC,eAAe,EAAE,OAAO,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,MAC1E,CAAC,eAAe,EAAE,OAAO,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,MAC1E,CAAC,eAAe,EAAE,OAAO,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,MAC1E,CAAC,cAAc,EAAE,OAAO,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,MACzE,CAAC,qBAAqB,EAAE,OAAO,mBAAmB,UAAU,qBAAqB,CAAC;AAAA,MAClF,CAAC,mBAAmB,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,MACtD,CAAC,KAAK,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,IAC1C;AAAA,IACA,SAAS;AAAA,MACP,CAAC,OAAO,mBAAmB;AAAA,MAC3B,CAAC,OAAO,mBAAmB;AAAA,MAC3B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,WAAW,EAAE,OAAO,oBAAoB,MAAM,QAAQ;AAAA,YACtD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,UAAU,CAAC,CAAC,KAAK,qBAAqB,eAAe,GAAG,EAAE,SAAS,WAAW,CAAC;AAAA,IAC/E,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,gBAAgB,WAAW,UAAU;AAAA,MACtC,CAAC,QAAQ,SAAS;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,gBAAgB,iBAAiB;AAAA,MAClC,CAAC,gBAAgB,WAAW,MAAM;AAAA,MAClC,CAAC,OAAO,SAAS;AAAA,IACnB;AAAA,EACF;AACF;", "names": []}