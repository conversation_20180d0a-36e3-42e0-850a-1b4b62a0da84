{"version": 3, "sources": ["out-editor/vs/base/common/worker/simpleWorker.nls.it.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/base/common/worker/simpleWorker.nls.it\", {\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"matrice\",\n\t\t\"valore booleano\",\n\t\t\"classe\",\n\t\t\"costante\",\n\t\t\"costruttore\",\n\t\t\"enumerazione\",\n\t\t\"membro di enumerazione\",\n\t\t\"evento\",\n\t\t\"campo\",\n\t\t\"file\",\n\t\t\"funzione\",\n\t\t\"interfaccia\",\n\t\t\"chiave\",\n\t\t\"metodo\",\n\t\t\"modulo\",\n\t\t\"spazio dei nomi\",\n\t\t\"Null\",\n\t\t\"numero\",\n\t\t\"oggetto\",\n\t\t\"operatore\",\n\t\t\"pacchetto\",\n\t\t\"proprietà\",\n\t\t\"stringa\",\n\t\t\"struct\",\n\t\t\"parametro di tipo\",\n\t\t\"variabile\",\n\t\t\"{0} ({1})\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,OAAO,4CAA6C,CACnD,0BAA2B,CAC1B,GACD,EACA,6BAA8B,CAC7B,UACA,kBACA,SACA,WACA,cACA,eACA,yBACA,SACA,QACA,OACA,WACA,cACA,SACA,SACA,SACA,kBACA,OACA,SACA,UACA,YACA,YACA,eACA,UACA,SACA,oBACA,YACA,WACD,CACD,CAAC", "names": [], "file": "simpleWorker.nls.it.js"}