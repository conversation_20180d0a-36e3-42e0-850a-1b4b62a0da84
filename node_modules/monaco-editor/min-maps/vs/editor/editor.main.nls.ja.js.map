{"version": 3, "sources": ["out-editor/vs/editor/editor.main.nls.ja.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/editor/editor.main.nls.ja\", {\n\t\"vs/base/browser/ui/actionbar/actionViewItems\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInput\": [\n\t\t\"入力\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInputToggles\": [\n\t\t\"大文字と小文字を区別する\",\n\t\t\"単語単位で検索する\",\n\t\t\"正規表現を使用する\",\n\t],\n\t\"vs/base/browser/ui/findinput/replaceInput\": [\n\t\t\"入力\",\n\t\t\"保持する\",\n\t],\n\t\"vs/base/browser/ui/hover/hoverWidget\": [\n\t\t\"{0} を使用して、ユーザー補助対応のビューでこれを検査します。\",\n\t\t\"キー バインドを介して現在トリガーできない [ユーザー補助対応のビューを開く] コマンドを使用して、ユーザー補助対応のビューでこれを検査します。\",\n\t],\n\t\"vs/base/browser/ui/iconLabel/iconLabelHover\": [\n\t\t\"読み込み中...\",\n\t],\n\t\"vs/base/browser/ui/inputbox/inputBox\": [\n\t\t\"エラー: {0}\",\n\t\t\"警告: {0}\",\n\t\t\"情報: {0}\",\n\t\t\" または履歴の {0}\",\n\t\t\" (履歴の {0})\",\n\t\t\"クリアされた入力\",\n\t],\n\t\"vs/base/browser/ui/keybindingLabel/keybindingLabel\": [\n\t\t\"バインドなし\",\n\t],\n\t\"vs/base/browser/ui/selectBox/selectBoxCustom\": [\n\t\t\"ボックスを選択\",\n\t],\n\t\"vs/base/browser/ui/toolbar/toolbar\": [\n\t\t\"その他の操作...\",\n\t],\n\t\"vs/base/browser/ui/tree/abstractTree\": [\n\t\t\"フィルター\",\n\t\t\"あいまい一致\",\n\t\t\"入力してフィルター\",\n\t\t\"入力して検索\",\n\t\t\"入力して検索\",\n\t\t\"閉じる\",\n\t\t\"要素が見つかりません。\",\n\t],\n\t\"vs/base/common/actions\": [\n\t\t\"(空)\",\n\t],\n\t\"vs/base/common/errorMessage\": [\n\t\t\"{0}: {1}\",\n\t\t\"システム エラーが発生しました ({0})\",\n\t\t\"不明なエラーが発生しました。ログで詳細を確認してください。\",\n\t\t\"不明なエラーが発生しました。ログで詳細を確認してください。\",\n\t\t\"{0} (合計 {1} エラー)\",\n\t\t\"不明なエラーが発生しました。ログで詳細を確認してください。\",\n\t],\n\t\"vs/base/common/keybindingLabels\": [\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Super\",\n\t\t\"Control\",\n\t\t\"Shift\",\n\t\t\"オプション\",\n\t\t\"コマンド\",\n\t\t\"Control\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Control\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Super\",\n\t],\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/browser/controller/textAreaHandler\": [\n\t\t\"エディター\",\n\t\t\"この時点では、エディターにアクセスできません。\",\n\t\t\"{0} スクリーン リーダー最適化モードを有効にするには、{1} を使用します\",\n\t\t\"{0} スクリーン リーダー最適化モードを有効にするには、{1} でクイック ピックを開き、[スクリーン リーダー アクセシビリティ モードの切り替え] コマンドを実行します。これは現在キーボードからトリガーできません。\",\n\t\t\"{0} {1} でキーバインド エディターにアクセスし、スクリーン リーダー アクセシビリティ モードの切り替えコマンドにキーバインドを割り当てて実行してください。\",\n\t],\n\t\"vs/editor/browser/coreCommands\": [\n\t\t\"長い行に移動しても行末に位置します\",\n\t\t\"長い行に移動しても行末に位置します\",\n\t\t\"セカンダリ カーソルが削除されました\",\n\t],\n\t\"vs/editor/browser/editorExtensions\": [\n\t\t\"元に戻す(&&U)\",\n\t\t\"元に戻す\",\n\t\t\"やり直し(&&R)\",\n\t\t\"やり直し\",\n\t\t\"すべて選択(&&S)\",\n\t\t\"すべてを選択\",\n\t],\n\t\"vs/editor/browser/widget/codeEditorWidget\": [\n\t\t\"カーソルの数は {0} に制限されています。大きな変更を行う場合は、[検索と置換](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) を使用することを検討してください。\",\n\t\t\"マルチ カーソルの上限を増やす\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/accessibleDiffViewer\": [\n\t\t\"アクセシビリティの高い差分ビューアーの [挿入] のアイコン。\",\n\t\t\"アクセシビリティの高い差分ビューアーの [削除] のアイコン。\",\n\t\t\"アクセシビリティの高い差分ビューアーの [閉じる] のアイコン。\",\n\t\t\"閉じる\",\n\t\t\"アクセス可能な Diff Viewer。上下方向キーを使用して移動します。\",\n\t\t\"変更された行はありません\",\n\t\t\"1 行が変更されました\",\n\t\t\"{0} 行が変更されました\",\n\t\t\"相違 {0}/{1}: 元の行 {2}、{3}。変更された行 {4}、{5}\",\n\t\t\"空白\",\n\t\t\"{0} 変更されていない行 {1}\",\n\t\t\"{0} 元の行 {1} 変更された行 {2}\",\n\t\t\"+ {0} 変更された行 {1}\",\n\t\t\"- {0} 元の行 {1}\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/colors\": [\n\t\t\"差分エディターで移動されたテキストの境界線の色。\",\n\t\t\"差分エディターで移動されたテキストのアクティブな境界線の色。\",\n\t\t\"変更されていないリージョン ウィジェットの周りの影の色。\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/decorations\": [\n\t\t\"差分エディターで挿入を示す行の装飾。\",\n\t\t\"差分エディターで削除を示す行の装飾。\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditor.contribution\": [\n\t\t\"変更されていない領域の折りたたみの切り替え\",\n\t\t\"移動したコード ブロックの表示の切り替え\",\n\t\t\"スペースが制限されている場合に [インライン ビューの使用] を切り替える\",\n\t\t\"スペースが制限されている場合はインライン ビューを使用する\",\n\t\t\"移動されたコード ブロックの表示\",\n\t\t\"差分エディター\",\n\t\t\"サイドの切り替え\",\n\t\t\"比較移動の終了\",\n\t\t\"変更されていないすべてのリージョンを折りたたむ\",\n\t\t\"変更されていないすべてのリージョンを表示する\",\n\t\t\"アクセシビリティの高い差分ビューアー\",\n\t\t\"次の差分に移動\",\n\t\t\"アクセシビリティの高い差分ビューアーを開く\",\n\t\t\"前の差分に移動\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorDecorations\": [\n\t\t\"選択した変更を元に戻す\",\n\t\t\"変更を元に戻す\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorEditors\": [\n\t\t\" {0}を使用して、アクセシビリティのヘルプを開きます。\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature\": [\n\t\t\"変更されていない領域を折りたたむ\",\n\t\t\"クリックまたはドラッグして上にもっと表示する\",\n\t\t\"変更されていない領域の表示\",\n\t\t\"クリックまたはドラッグして下にもっと表示する\",\n\t\t\"非表示 {0} 行\",\n\t\t\"ダブルクリックして展開する\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin\": [\n\t\t\"削除された行のコピー\",\n\t\t\"削除された行のコピー\",\n\t\t\"変更された行のコピー\",\n\t\t\"変更された行のコピー\",\n\t\t\"削除された行のコピー ({0})\",\n\t\t\"変更された行のコピー ({0})\",\n\t\t\"この変更を元に戻す\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/movedBlocksLines\": [\n\t\t\"行 {0}-{1} に変更を加えてコードを移動しました\",\n\t\t\"行 {0}-{1} から変更を加えてコードが移動されました\",\n\t\t\"コードを行 {0}-{1} に移動しました\",\n\t\t\"行 {0}-{1} から移動されたコード\",\n\t],\n\t\"vs/editor/browser/widget/multiDiffEditorWidget/colors\": [\n\t\t\"diff エディターのヘッダーの背景色\",\n\t],\n\t\"vs/editor/common/config/editorConfigurationSchema\": [\n\t\t\"エディター\",\n\t\t\"1 つのタブに相当するスペースの数。{0} がオンの場合、この設定はファイル コンテンツに基づいて上書きされます。\",\n\t\t\"インデントまたは `\\\"tabSize\\\"` で `#editor.tabSize#` の値を使用するために使用されるスペースの数。この設定は、 `#editor.detectIndentation#` がオンの場合、ファイルの内容に基づいてオーバーライドされます。\",\n\t\t\"`Tab` キーを押すとスペースが挿入されます。{0} がオンの場合、この設定はファイル コンテンツに基づいて上書きされます。\",\n\t\t\"ファイルがファイルの内容に基づいて開かれる場合、{0} と {1} を自動的に検出するかどうかを制御します。\",\n\t\t\"自動挿入された末尾の空白を削除します。\",\n\t\t\"大きなファイルでメモリが集中する特定の機能を無効にするための特別な処理。\",\n\t\t\"単語ベースの候補をオフにします。\",\n\t\t\"アクティブなドキュメントからのみ単語の候補を表示します。\",\n\t\t\"同じ言語の開いているすべてのドキュメントから単語の候補を表示します。\",\n\t\t\"開いているすべてのドキュメントから単語の候補を表示します。\",\n\t\t\"ドキュメントの単語に基づいて入力候補を計算するかどうか、またどのドキュメントから入力候補を計算するかを制御します。\",\n\t\t\"セマンティックの強調表示がすべての配色テーマについて有効になりました。\",\n\t\t\"セマンティックの強調表示がすべての配色テーマについて無効になりました。\",\n\t\t\"セマンティックの強調表示は、現在の配色テーマの \\'semanticHighlighting\\' 設定によって構成されています。\",\n\t\t\"semanticHighlighting をサポートされる言語で表示するかどうかを制御します。\",\n\t\t\"エディターのコンテンツをダブルクリックするか、`Escape` キーを押しても、ピーク エディターを開いたままにします。\",\n\t\t\"この長さを越える行は、パフォーマンス上の理由によりトークン化されません。\",\n\t\t\"Web ワーカーでトークン化を非同期的に行うかどうかを制御します。\",\n\t\t\"非同期トークン化をログに記録するかどうかを制御します。デバッグ用のみ。\",\n\t\t\"従来のバックグラウンド トークン化に対して非同期トークン化を検証するかどうかを制御します。トークン化が遅くなる可能性があります。デバッグ専用です。\",\n\t\t\"インデントを増減する角かっこを定義します。\",\n\t\t\"左角かっこまたは文字列シーケンス。\",\n\t\t\"右角かっこまたは文字列シーケンス。\",\n\t\t\"角かっこのペアの色付けが有効になっている場合、入れ子のレベルによって色付けされる角かっこのペアを定義します。\",\n\t\t\"左角かっこまたは文字列シーケンス。\",\n\t\t\"右角かっこまたは文字列シーケンス。\",\n\t\t\"差分計算が取り消された後のタイムアウト (ミリ秒単位)。タイムアウトなしには 0 を使用します。\",\n\t\t\"差分を計算する場合の最大ファイル サイズ (MB)。制限なしの場合は 0 を使用します。\",\n\t\t\"差分エディターが差分を横に並べて表示するか、行内に表示するかを制御します。\",\n\t\t\"差分エディターの幅がこの値より小さい場合は、インライン ビューが使用されます。\",\n\t\t\"有効になっていると、エディターの幅が小さすぎる場合はインライン ビューが使用されます。\",\n\t\t\"有効にすると、差分エディターでグリフ余白に、変更を元に戻すための矢印が表示されます。\",\n\t\t\"有効にすると、差分エディターは先頭または末尾の空白文字の変更を無視します。\",\n\t\t\"差分エディターが追加/削除された変更に +/- インジケーターを示すかどうかを制御します。\",\n\t\t\"エディターで CodeLens を表示するかどうかを制御します。\",\n\t\t\"行を折り返しません。\",\n\t\t\"行をビューポートの幅で折り返します。\",\n\t\t\"行は、{0} の設定に従って折り返されます。\",\n\t\t\"従来の差分アルゴリズムを使用します。\",\n\t\t\"高度な差分アルゴリズムを使用します。\",\n\t\t\"差分エディターに変更されていない領域を表示するかどうかを制御します。\",\n\t\t\"未変更の領域に使用される線の数を制御します。\",\n\t\t\"変更されていない領域の最小値として使用される線の数を制御します。\",\n\t\t\"変更されていない領域を比較するときにコンテキストとして使用される行の数を制御します。\",\n\t\t\"差分エディターで検出されたコードの移動を表示するかどうかを制御します。\",\n\t\t\"文字が挿入または削除された場所を確認するために、差分エディターに空の装飾を表示するかどうかを制御します。\",\n\t],\n\t\"vs/editor/common/config/editorOptions\": [\n\t\t\"プラットフォーム API を使用して、スクリーン リーダーがいつ接続されたかを検出します。\",\n\t\t\"スクリーン リーダーでの使用を最適化します。\",\n\t\t\"スクリーン リーダーが接続されていないとします。\",\n\t\t\"この UI をスクリーン リーダーに最適化されたモードで実行するかどうかを制御します。\",\n\t\t\"コメント時に空白文字を挿入するかどうかを制御します。\",\n\t\t\"行コメントの追加または削除アクションの切り替えで、空の行を無視するかどうかを制御します。\",\n\t\t\"選択範囲を指定しないでコピーする場合に現在の行をコピーするかどうかを制御します。\",\n\t\t\"入力中に一致を検索するためにカーソルをジャンプさせるかどうかを制御します。\",\n\t\t\"エディターの選択範囲から検索文字列をシードしません。\",\n\t\t\"カーソル位置にある単語を含め、エディターの選択範囲から検索文字列を常にシードします。\",\n\t\t\"エディターの選択範囲から検索文字列のみをシードします。\",\n\t\t\"エディターの選択範囲から検索ウィジェット内の検索文字列を与えるかどうかを制御します。\",\n\t\t\"[選択範囲を検索] を自動的にオンにしません (既定)。\",\n\t\t\"[選択範囲を検索] を常に自動的にオンにします。\",\n\t\t\"複数行のコンテンツが選択されている場合は、[選択範囲を検索] を自動的にオンにします。\",\n\t\t\"[選択範囲を検索] を自動的にオンにする条件を制御します。\",\n\t\t\"macOS で検索ウィジェットが共有の検索クリップボードを読み取りまたは変更するかどうかを制御します。\",\n\t\t\"検索ウィジェットがエディターの上に行をさらに追加するかどうかを制御します。true の場合、検索ウィジェットが表示されているときに最初の行を超えてスクロールできます。\",\n\t\t\"以降で一致が見つからない場合に、検索を先頭から (または末尾から) 自動的に再実行するかどうか制御します。\",\n\t\t\"フォントの合字 (\\'calt\\' および \\'liga\\' フォントの機能) を有効または無効にします。\\'font-feature-settings\\' CSS プロパティを詳細に制御するには、これを文字列に変更します。\",\n\t\t\"明示的な \\'font-feature-settings\\' CSS プロパティ。合字を有効または無効にする必要があるのが 1 つだけである場合は、代わりにブール値を渡すことができます。\",\n\t\t\"フォントの合字やフォントの機能を構成します。合字を有効または無効にするブール値または CSS \\'font-feature-settings\\' プロパティの値の文字列を指定できます。\",\n\t\t\"font-weight から font-variation-settings への変換を有効/無効にします。\\'font-variation-settings\\' CSS プロパティを細かく制御するために、これを文字列に変更します。\",\n\t\t\"明示的な \\'font-variation-settings\\' CSS プロパティ。font-weight を font-variation-settings に変換する必要があるだけであれば、代わりにブール値を渡すことができます。\",\n\t\t\"フォントのバリエーションを構成します。font-weight から font-variation-settings への変換を有効/無効にするブール値、または CSS \\'font-variation-settings\\' プロパティの値の文字列のいずれかです。\",\n\t\t\"フォント サイズ (ピクセル単位) を制御します。\",\n\t\t\"使用できるのは \\\"標準\\\" および \\\"太字\\\" のキーワードまたは 1 ～ 1000 の数字のみです。\",\n\t\t\"フォントの太さを制御します。\\\"標準\\\" および \\\"太字\\\" のキーワードまたは 1 ～ 1000 の数字を受け入れます。\",\n\t\t\"結果のピーク ビューを表示 (既定)\",\n\t\t\"主な結果に移動し、ピーク ビューを表示します\",\n\t\t\"プライマリ結果に移動し、他のユーザーへのピークレス ナビゲーションを有効にします\",\n\t\t\"この設定は非推奨です。代わりに、\\'editor.editor.gotoLocation.multipleDefinitions\\' や \\'editor.editor.gotoLocation.multipleImplementations\\' などの個別の設定を使用してください。\",\n\t\t\"複数のターゲットの場所があるときの \\'定義へ移動\\' コマンドの動作を制御します。\",\n\t\t\"複数のターゲットの場所があるときの \\'型定義へ移動\\' コマンドの動作を制御します。\",\n\t\t\"複数のターゲットの場所があるときの \\'宣言へ移動\\' コマンドの動作を制御します。\",\n\t\t\"複数のターゲットの場所があるときの \\'実装に移動\\' コマンドの動作を制御します。\",\n\t\t\"ターゲットの場所が複数存在する場合の \\'参照へ移動\\' コマンドの動作を制御します。\",\n\t\t\"\\'定義へ移動\\' の結果が現在の場所である場合に実行される代替コマンド ID。\",\n\t\t\"\\'型定義へ移動\\' の結果が現在の場所である場合に実行される代替コマンド ID。\",\n\t\t\"\\'宣言へ移動\\' の結果が現在の場所である場合に実行される代替コマンド ID。\",\n\t\t\"\\'実装へ移動\\' の結果が現在の場所である場合に実行される代替コマンド ID。\",\n\t\t\"\\'参照へ移動\\' の結果が現在の場所である場合に実行される代替コマンド ID。\",\n\t\t\"ホバーを表示するかどうかを制御します。\",\n\t\t\"ホバーを表示後の待ち時間 (ミリ秒) を制御します。\",\n\t\t\"ホバーにマウスを移動したときに、ホバーを表示し続けるかどうかを制御します。\",\n\t\t\"ホバーが非表示になるまでの遅延をミリ秒単位で制御します。`editor.hover.sticky` を有効にする必要があります。\",\n\t\t\"スペースがある場合は、行の上にマウス カーソルを被せて表示する。\",\n\t\t\"すべての文字の幅が同じであると仮定します。これは、モノスペース フォントや、グリフの幅が等しい特定のスクリプト (ラテン文字など) で正しく動作する高速アルゴリズムです。\",\n\t\t\"折り返しポイントの計算をブラウザーにデリゲートします。これは、大きなファイルのフリーズを引き起こす可能性があるものの、すべてのケースで正しく動作する低速なアルゴリズムです。\",\n\t\t\"折り返しポイントを計算するアルゴリズムを制御します。アクセシビリティ モードでは、最高のエクスペリエンスを実現するために詳細設定が使用されることにご注意ください。\",\n\t\t\"エディターでコード アクションの電球を有効にします。\",\n\t\t\"AI アイコンを表示しません。\",\n\t\t\"コード アクション メニューに AI アクションが含まれている場合、コードにのみ AI アイコンを表示します。\",\n\t\t\"コード アクション メニューに AI アクションが含まれている場合、コードと空の行に AI アイコンを表示します。\",\n\t\t\"コード アクション メニューに AI アクションが含まれている場合は、電球と共に AI アイコンを表示します。\",\n\t\t\"スクロール中にエディターの上部に入れ子になった現在のスコープを表示します。\",\n\t\t\"表示する追従行の最大数を定義します。\",\n\t\t\"固定する行を決定するために使用するモデルを定義します。アウトライン モデルが存在しない場合、インデント モデルにフォールバックする折りたたみプロバイダー モデルにフォールバックします。この順序は、3 つのケースすべてで優先されます。\",\n\t\t\"エディターの水平スクロール バーで固定スクロールのスクロールを有効にします。\",\n\t\t\"エディターでインレー ヒントを有効にします。\",\n\t\t\"インレイ ヒントが有効になっています\",\n\t\t\"インレイ ヒントは既定で表示され、{0} を押したままにすると非表示になります\",\n\t\t\"インレイ ヒントは既定では非表示になり、{0} を押したままにすると表示されます\",\n\t\t\"インレイ ヒントが無効になっています\",\n\t\t\"エディターでの解説ヒントのフォント サイズを制御します。既定では、{0} は、構成された値が {1} より小さいか、エディターのフォント サイズより大きい場合に使用されます。\",\n\t\t\"エディターで解説ヒントのフォント ファミリを制御します。空に設定すると、 {0} が使用されます。\",\n\t\t\"エディターでのインレイ ヒントに関するパディングを有効にします。\",\n\t\t\"行の高さを制御します。\\r\\n - 0 を使用してフォント サイズから行の高さを自動的に計算します。\\r\\n - 0 から 8 までの値は、フォント サイズの乗数として使用されます。\\r\\n - 8 以上の値は有効値として使用されます。\",\n\t\t\"ミニマップを表示するかどうかを制御します。\",\n\t\t\"ミニマップを自動的に非表示するかどうかを制御します。\",\n\t\t\"ミニマップのサイズは、エディターのコンテンツと同じです (スクロールする場合があります)。\",\n\t\t\"ミニマップは、必要に応じて、エディターの高さを埋めるため、拡大または縮小します (スクロールしません)。\",\n\t\t\"ミニマップは必要に応じて縮小し、エディターより大きくなることはありません (スクロールしません)。\",\n\t\t\"ミニマップのサイズを制御します。\",\n\t\t\"ミニマップを表示する場所を制御します。\",\n\t\t\"ミニマップ スライダーを表示するタイミングを制御します。\",\n\t\t\"ミニマップに描画されるコンテンツのスケール: 1、2、または 3。\",\n\t\t\"行にカラー ブロックではなく実際の文字を表示します。\",\n\t\t\"表示するミニマップの最大幅を特定の列数に制限します。\",\n\t\t\"エディターの上端と最初の行の間の余白の大きさを制御します。\",\n\t\t\"エディターの下端と最後の行の間の余白の大きさを制御します。\",\n\t\t\"入力時にパラメーター ドキュメントと型情報を表示するポップアップを有効にします。\",\n\t\t\"パラメーター ヒント メニューを周回するか、リストの最後で閉じるかどうかを制御します。\",\n\t\t\"提案ウィジェット内にクイック候補が表示される\",\n\t\t\"クイック候補がゴースト テキストとして表示される\",\n\t\t\"クイック候補が無効になっています\",\n\t\t\"文字列内でクイック候補を有効にします。\",\n\t\t\"コメント内でクイック候補を有効にします。\",\n\t\t\"文字列およびコメント外でクイック候補を有効にします。\",\n\t\t\"入力中に候補を自動的に表示するかどうかを制御します。これは、コメント、文字列、その他コードの入力用に設定できます。クイック提案は、ゴースト テキストとして表示するか、提案ウィジェットで表示するように構成できます。また、\\'{0}\\' に注意してください。これは、提案が特殊文字によってトリガーされるかどうかを制御する設定です。\",\n\t\t\"行番号は表示されません。\",\n\t\t\"行番号は、絶対値として表示されます。\",\n\t\t\"行番号は、カーソル位置までの行数として表示されます。\",\n\t\t\"行番号は 10 行ごとに表示されます。\",\n\t\t\"行番号の表示を制御します。\",\n\t\t\"このエディターのルーラーがレンダリングする単一領域の文字数。\",\n\t\t\"このエディターのルーラーの色です。\",\n\t\t\"特定の等幅文字数の後に垂直ルーラーを表示します。複数のルーラーには複数の値を使用します。配列が空の場合はルーラーを表示しません。\",\n\t\t\"垂直スクロールバーは、必要な場合にのみ表示されます。\",\n\t\t\"垂直スクロールバーは常に表示されます。\",\n\t\t\"垂直スクロールバーは常に非表示になります。\",\n\t\t\"垂直スクロールバーの表示を制御します。\",\n\t\t\"水平スクロールバーは、必要な場合にのみ表示されます。\",\n\t\t\"水平スクロールバーは常に表示されます。\",\n\t\t\"水平スクロールバーは常に非表示になります。\",\n\t\t\"水平スクロールバーの表示を制御します。\",\n\t\t\"垂直スクロールバーの幅。\",\n\t\t\"水平スクロールバーの高さ。\",\n\t\t\"クリックするとページ単位でスクロールするか、クリック位置にジャンプするかを制御します。\",\n\t\t\"設定すると、水平スクロール バーはエディターのコンテンツのサイズを大きくしません。\",\n\t\t\"基本 ASCII 以外のすべての文字を強調表示するかどうかを制御します。U+0020 から U+007E の間の文字、タブ、改行 (LF)、行頭復帰のみが基本 ASCII と見なされます。\",\n\t\t\"空白を占めるだけの文字や幅がまったくない文字を強調表示するかどうかを制御します。\",\n\t\t\"現在のユーザー ロケールで一般的な文字を除き、基本的な ASCII 文字と混同される可能性のある文字を強調表示するかどうかを制御します。\",\n\t\t\"コメント内の文字を Unicode 強調表示の対象にするかどうかを制御します。\",\n\t\t\"文字列内の文字を Unicode 強調表示の対象にするかどうかを制御します。\",\n\t\t\"強調表示せず許可される文字を定義します。\",\n\t\t\"許可されているロケールで一般的な Unicode 文字は強調表示されません。\",\n\t\t\"エディターにインライン候補を自動的に表示するかどうかを制御します。\",\n\t\t\"インライン候補が表示されるたびに、インライン候補ツール バーを表示します。\",\n\t\t\"インライン候補にカーソルを合わせるたびに、インライン候補ツール バーを表示します。\",\n\t\t\"インライン候補ツール バーを今後は表示しないでください。\",\n\t\t\"インライン候補ツール バーを表示するタイミングを制御します。\",\n\t\t\"インライン提案と提案ウィジェットの相互作用の方法を制御します。有効すると、インライン候補が使用可能な場合は、提案ウィジェットが自動的に表示されません。\",\n\t\t\"ブラケットのペアの色付けが有効かどうかを制御します。 {0} を使用して、ブラケットの強調表示の色をオーバーライドします。\",\n\t\t\"括弧の各種別が、個別のカラー プールを保持するかどうかを制御します。\",\n\t\t\"ブラケット ペア ガイドを有効にする。\",\n\t\t\"アクティブなブラケット ペアに対してのみブラケット ペア ガイドを有効にします。\",\n\t\t\"ブラケット ペア ガイドを無効にします。\",\n\t\t\"ブラケット ペアのガイドを有効にするかどうかを制御します。\",\n\t\t\"縦のブラケット ペアのガイドに加えて、同じく水平のガイドを有効にします。\",\n\t\t\"アクティブなブラケット ペアに対してのみ、水平のガイドを有効にします。\",\n\t\t\"水平ブラケット ペア ガイドを無効にします。\",\n\t\t\"水平方向のブラケット ペアのガイドを有効にするかどうかを制御します。\",\n\t\t\"エディターでアクティブな角かっこのペアを強調表示するかどうかを制御します。\",\n\t\t\"エディターでインデント ガイドを表示するかどうかを制御します。\",\n\t\t\"アクティブなインデント ガイドを強調表示します。\",\n\t\t\"角かっこガイドが強調表示されている場合でも、アクティブなインデント ガイドを強調表示します。\",\n\t\t\"アクティブなインデント ガイドを強調表示しないでください。\",\n\t\t\"エディターでアクティブなインデントのガイドを強調表示するかどうかを制御します。\",\n\t\t\"カーソルの右のテキストを上書きせずに候補を挿入します。\",\n\t\t\"候補を挿入し、カーソルの右のテキストを上書きします。\",\n\t\t\"入力候補を受け入れるときに単語を上書きするかどうかを制御します。これは、この機能の利用を選択する拡張機能に依存することにご注意ください。\",\n\t\t\"候補のフィルター処理と並び替えでささいな入力ミスを考慮するかどうかを制御します。\",\n\t\t\"並べ替えがカーソル付近に表示される単語を優先するかどうかを制御します。\",\n\t\t\"保存された候補セクションを複数のワークプレースとウィンドウで共有するかどうかを制御します (`#editor.suggestSelection#` が必要)。\",\n\t\t\"IntelliSense を自動でトリガーする場合に、常に候補を選択します。\",\n\t\t\"IntelliSense を自動でトリガーする場合に、候補を選択しません。\",\n\t\t\"トリガー文字から IntelliSense をトリガーする場合にのみ、候補を選択します。\",\n\t\t\"入力時に IntelliSense をトリガーする場合にのみ、候補を選択します。\",\n\t\t\"ウィジェットを表示する際に候補を選択するかどうかを制御します。こちらは自動的にトリガーされる候補 (\\'#editor.quickSuggestions#\\' と \\'#editor.suggestOnTriggerCharacters#\\') にのみ適用され、(\\'Ctrl+Space\\' などを通じて) 明示的に呼び出される際には常に候補が選択されることにご注意ください。\",\n\t\t\"アクティブ スニペットがクイック候補を防止するかどうかを制御します。\",\n\t\t\"提案のアイコンを表示するか、非表示にするかを制御します。\",\n\t\t\"候補ウィジェットの下部にあるステータス バーの表示を制御します。\",\n\t\t\"提案の結果をエディターでプレビューするかどうかを制御します。\",\n\t\t\"候補の詳細をラベル付きのインラインで表示するか、詳細ウィジェットにのみ表示するかを制御します。\",\n\t\t\"この設定は非推奨です。候補ウィジェットのサイズ変更ができるようになりました。\",\n\t\t\"この設定は非推奨です。代わりに、\\'editor.suggest.showKeywords\\' や \\'editor.suggest.showSnippets\\' などの個別の設定を使用してください。\",\n\t\t\"有効にすると、IntelliSense に `メソッド` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `関数` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `コンストラクター` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `非推奨` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense のフィルター処理では、単語の先頭で最初の文字が一致する必要があります。たとえば、`Console` や `WebContext` の場合は `c`、`description` の場合は _not_ です。無効にすると、IntelliSense はより多くの結果を表示しますが、一致品質で並べ替えられます。\",\n\t\t\"有効にすると、IntelliSense に `フィールド` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `変数` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に \\'クラス\\' 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `構造体` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `インターフェイス` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `モジュール` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `プロパティ` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `イベント` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `演算子` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `ユニット` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `値` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `定数` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `列挙型` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `enumMember` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `キーワード` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に \\'テキスト\\' -候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `色` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に \\'ファイル\\' 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `参照` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `customcolor` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `フォルダー` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `typeParameter` 候補が表示されます。\",\n\t\t\"有効にすると、IntelliSense に `スニペット` 候補が表示されます。\",\n\t\t\"有効な場合、IntelliSense によって \\'ユーザー\\' 候補が示されます。\",\n\t\t\"有効にすると、IntelliSense によって \\'問題\\' 候補が示されます。\",\n\t\t\"先頭と末尾の空白を常に選択するかどうか。\",\n\t\t\"サブワード (\\'fooBar\\' の \\'foo\\' または \\'foo_bar\\' など) を選択する必要があるかどうか。\",\n\t\t\"インデントしません。 折り返し行は列 1 から始まります。\",\n\t\t\"折り返し行は、親と同じインデントになります。\",\n\t\t\"折り返し行は、親 +1 のインデントになります。\",\n\t\t\"折り返し行は、親 +2 のインデントになります。\",\n\t\t\"折り返し行のインデントを制御します。\",\n\t\t\"(エディターでファイルを開く代わりに) \\'shift\\' キーを押しながらテキスト エディターにファイルをドラッグ アンド ドロップできるかどうかを制御します。\",\n\t\t\"エディターにファイルをドロップするときにウィジェットを表示するかどうかを制御します。このウィジェットでは、ファイルのドロップ方法を制御できます。\",\n\t\t\"ファイルがエディターにドロップされた後に、ドロップ セレクター ウィジェットを表示します。\",\n\t\t\"ドロップ セレクター ウィジェットを表示しません。代わりに、既定のドロップ プロバイダーが常に使用されます。\",\n\t\t\"さまざまな方法でコンテンツを貼り付けることができるかどうかを制御します。\",\n\t\t\"エディターにコンテンツを貼り付けるときにウィジェットを表示するかどうかを制御します。このウィジェットを使用すると、ファイルの貼り付け方法を制御できます。\",\n\t\t\"コンテンツをエディターに貼り付けた後、貼り付けセレクター ウィジェットを表示します。\",\n\t\t\"貼り付けセレクター ウィジェットを表示しないでください。代わりに、既定の貼り付け動作が常に使用されます。\",\n\t\t\"コミット文字で候補を受け入れるかどうかを制御します。たとえば、JavaScript ではセミコロン (`;`) をコミット文字にして、候補を受け入れてその文字を入力することができます。\",\n\t\t\"テキストの変更を行うとき、`Enter` を使用する場合にのみ候補を受け付けます。\",\n\t\t\"`Tab` キーに加えて `Enter` キーで候補を受け入れるかどうかを制御します。改行の挿入や候補の反映の間であいまいさを解消するのに役立ちます。\",\n\t\t\"一度にスクリーン リーダーによって読み上げることができるエディターの行数を制御します。スクリーン リーダーが検出されると、既定値が 500 に自動的に設定されます。警告: 既定値より大きい数値の場合は、パフォーマンスに影響があります。\",\n\t\t\"エディターのコンテンツ\",\n\t\t\"スクリーン リーダーによってインライン候補が読み上げられるかどうかを制御します。\",\n\t\t\"言語設定を使用して、いつかっこを自動クローズするか決定します。\",\n\t\t\"カーソルが空白文字の左にあるときだけ、かっこを自動クローズします。\",\n\t\t\"エディターで左角かっこを追加した後に自動的に右角かっこを挿入するかどうかを制御します。\",\n\t\t\"言語設定を使用して、いつかっこを自動クローズするか決定します。\",\n\t\t\"カーソルが空白文字の左にあるときだけ、コメントを自動クローズします。\",\n\t\t\"エディターで左角かっこを追加した後に自動的に右角かっこを挿入するかどうかを制御します。\",\n\t\t\"隣接する終わり引用符または括弧が自動的に挿入された場合にのみ、それらを削除します。\",\n\t\t\"削除時にエディターで隣接する終わり引用符または括弧を削除するかどうかを制御します。\",\n\t\t\"終わり引用符または括弧が自動的に挿入された場合にのみ、それらを上書きします。\",\n\t\t\"エディターで終わり引用符または括弧を上書きするかどうかを制御します。\",\n\t\t\"言語設定を使用して、いつ引用符を自動クローズするか決定します。\",\n\t\t\"カーソルが空白文字の左にあるときだけ、引用符を自動クローズします。\",\n\t\t\"ユーザーが開始引用符を追加した後、エディター自動的に引用符を閉じるかどうかを制御します。\",\n\t\t\"エディターはインデントを自動的に挿入しません。\",\n\t\t\"エディターは、現在の行のインデントを保持します。\",\n\t\t\"エディターは、現在の行のインデントを保持し、言語が定義されたかっこを優先します。\",\n\t\t\"エディターは、現在の行のインデントを保持し、言語が定義されたかっこを優先し、言語で定義された特別な onEnterRules を呼び出します。\",\n\t\t\"エディターは、現在の行のインデントを保持し、言語が定義されたかっこを優先し、言語で定義された特別な onEnterRules を呼び出し、言語で定義された indentationRules を優先します。\",\n\t\t\"ユーザーが行を入力、貼り付け、移動、またはインデントするときに、エディターでインデントを自動的に調整するかどうかを制御します。\",\n\t\t\"言語構成を使用して、選択範囲をいつ自動的に囲むかを判断します。\",\n\t\t\"角かっこではなく、引用符で囲みます。\",\n\t\t\"引用符ではなく、角かっこで囲みます。\",\n\t\t\"引用符または角かっこを入力するときに、エディターが選択範囲を自動的に囲むかどうかを制御します。\",\n\t\t\"インデントにスペースを使用するときは、タブ文字の選択動作をエミュレートします。選択範囲はタブ位置に留まります。\",\n\t\t\"エディターで CodeLens を表示するかどうかを制御します。\",\n\t\t\"CodeLens のフォント ファミリを制御します。\",\n\t\t\"CodeLens のフォント サイズをピクセル単位で制御します。0 に設定すると、`#editor.fontSize#` の 90% が使用されます。\",\n\t\t\"エディターでインライン カラー デコレーターと色の選択を表示する必要があるかどうかを制御します。\",\n\t\t\"カラー デコレーターのクリック時とポイント時の両方にカラー ピッカーを表示する\",\n\t\t\"カラー デコレーターのポイント時にカラー ピッカーを表示する\",\n\t\t\"カラー デコレーターのクリック時にカラー ピッカーを表示する\",\n\t\t\"カラー デコレーターからカラー ピッカーを表示する条件を制御します\",\n\t\t\"エディターで一度にレンダリングできるカラー デコレーターの最大数を制御します。\",\n\t\t\"マウスとキーでの選択により列の選択を実行できるようにします。\",\n\t\t\"構文ハイライトをクリップボードにコピーするかどうかを制御します。\",\n\t\t\"カーソルのアニメーション方式を制御します。\",\n\t\t\"スムーズ キャレット アニメーションが無効になっています。\",\n\t\t\"スムーズ キャレット アニメーションは、ユーザーが明示的なジェスチャでカーソルを移動した場合にのみ有効になります。\",\n\t\t\"スムーズ キャレット アニメーションは常に有効です。\",\n\t\t\"滑らかなキャレットアニメーションを有効にするかどうかを制御します。\",\n\t\t\"カーソルのスタイルを制御します。\",\n\t\t\"カーソル前後の表示可能な先頭の行 (最小 0) と末尾の行 (最小 1) の最小数を制御します。他の一部のエディターでは \\'scrollOff\\' または \\'scrollOffset\\' と呼ばれます。\",\n\t\t\"`cursorSurroundingLines` は、キーボードまたは API でトリガーされた場合にのみ強制されます。\",\n\t\t\"`cursorSurroundingLines` は常に適用されます。\",\n\t\t\"`#cursorSurroundingLines#` を適用するタイミングを制御します。\",\n\t\t\"`#editor.cursorStyle#` が `line` に設定されている場合、カーソルの幅を制御します。\",\n\t\t\"ドラッグ アンド ドロップによる選択範囲の移動をエディターが許可するかどうかを制御します。\",\n\t\t\"SVGS で新しいレンダリング方法を使用します。\",\n\t\t\"フォント文字に新しいレンダリング方法を使用します。\",\n\t\t\"安定したレンダリング方法を使用します。\",\n\t\t\"新しい試験的なメソッドを使用して空白をレンダリングするかどうかを制御します。\",\n\t\t\"`Alt` を押すと、スクロール速度が倍増します。\",\n\t\t\"エディターでコードの折りたたみを有効にするかどうかを制御します。\",\n\t\t\"利用可能な場合は言語固有の折りたたみ方法を使用し、利用可能ではない場合はインデントベースの方法を使用します。\",\n\t\t\"インデントベースの折りたたみ方法を使用します。\",\n\t\t\"折りたたみ範囲の計算方法を制御します。\",\n\t\t\"エディターで折りたたまれた範囲を強調表示するかどうかをコントロールします。\",\n\t\t\"エディターがインポート範囲を自動的に折りたたむかどうかを制御します。\",\n\t\t\"折りたたみ可能な領域の最大数です。この値を大きくすると、現在のソースに多数の折りたたみ可能な領域がある場合にエディターの応答性が低下する可能性があります。\",\n\t\t\"折りたたまれた行の後の空のコンテンツをクリックすると行が展開されるかどうかを制御します。\",\n\t\t\"フォント ファミリを制御します。\",\n\t\t\"貼り付けた内容がエディターにより自動的にフォーマットされるかどうかを制御します。フォーマッタを使用可能にする必要があります。また、フォーマッタがドキュメント内の範囲をフォーマットできなければなりません。\",\n\t\t\"エディターで入力後に自動的に行のフォーマットを行うかどうかを制御します。\",\n\t\t\"エディターで縦のグリフ余白が表示されるかどうかを制御します。ほとんどの場合、グリフ余白はデバッグに使用されます。\",\n\t\t\"概要ルーラーでカーソルを非表示にするかどうかを制御します。\",\n\t\t\"文字間隔 (ピクセル単位) を制御します。\",\n\t\t\"リンクされた編集がエディターで有効にされるかどうかを制御します。言語によっては、編集中に HTML タグなどの関連する記号が更新されます。\",\n\t\t\"エディターがリンクを検出してクリック可能な状態にするかどうかを制御します。\",\n\t\t\"対応するかっこを強調表示します。\",\n\t\t\"マウス ホイール スクロール イベントの `deltaX` と `deltaY` で使用される乗数。\",\n\t\t\"`Ctrl` キーを押しながらマウス ホイールを使用してエディターのフォントをズームします。\",\n\t\t\"複数のカーソルが重なっているときは、マージします。\",\n\t\t\"Windows および Linux 上の `Control` キーと macOS 上の `Command` キーに割り当てます。\",\n\t\t\"Windows および Linux 上の `Alt` キーと macOS 上の `Option` キーに割り当てます。\",\n\t\t\"マウスを使用して複数のカーソルを追加するために使用する修飾子。[定義に移動] および [リンクを開く] マウス ジェスチャは、[multicursor 修飾子](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier) と競合しないように調整されます。\",\n\t\t\"カーソルごとにテキストを 1 行ずつ貼り付けます。\",\n\t\t\"各カーソルは全文を貼り付けます。\",\n\t\t\"貼り付けたテキストの行数がカーソル数と一致する場合の貼り付けを制御します。\",\n\t\t\"アクティブなエディターに一度に配置できるカーソルの最大数を制御します。\",\n\t\t\"発生回数を強調表示しません。\",\n\t\t\"現在のファイル内の発生回数のみを強調表示します。\",\n\t\t\"試験段階: すべての有効な開いているファイルの発生回数を強調表示します。\",\n\t\t\"開いているファイル間で発生回数を強調表示するかどうかを制御します。\",\n\t\t\"概要ルーラーの周囲に境界線が描画されるかどうかを制御します。\",\n\t\t\"ピークを開くときにツリーにフォーカスする\",\n\t\t\"ピークを開くときにエディターにフォーカスする\",\n\t\t\"ピーク ウィジェットのインライン エディターまたはツリーをフォーカスするかどうかを制御します。\",\n\t\t\"[定義へ移動] マウス ジェスチャーで、常にピーク ウィジェットを開くかどうかを制御します。\",\n\t\t\"クイック候補が表示されるまでのミリ秒を制御します。\",\n\t\t\"エディターでの型の自動名前変更を制御します。\",\n\t\t\"非推奨です。代わりに、`editor.linkedEditing` を使用してください。\",\n\t\t\"エディターで制御文字を表示するかどうかを制御します。\",\n\t\t\"ファイルの末尾が改行の場合は、最後の行番号を表示します。\",\n\t\t\"余白と現在の行を強調表示します。\",\n\t\t\"エディターが現在の行をどのように強調表示するかを制御します。\",\n\t\t\"エディターにフォーカスがある場合にのみ現在の行をエディターで強調表示する必要があるかどうかを制御します。\",\n\t\t\"単語間の単一スペース以外の空白文字を表示します。\",\n\t\t\"選択したテキストにのみ空白文字を表示します。\",\n\t\t\"末尾の空白文字のみを表示します。\",\n\t\t\"エディターで空白文字を表示するかどうかを制御します。\",\n\t\t\"選択範囲の角を丸くするかどうかを制御します。\",\n\t\t\"エディターが水平方向に余分にスクロールする文字数を制御します。\",\n\t\t\"エディターが最後の行を越えてスクロールするかどうかを制御します。\",\n\t\t\"垂直および水平方向の両方に同時にスクロールする場合は、主要な軸に沿ってスクロールします。トラックパッド上で垂直方向にスクロールする場合は、水平ドリフトを防止します。\",\n\t\t\"Linux の PRIMARY クリップボードをサポートするかどうかを制御します。\",\n\t\t\"エディターが選択項目と類似の一致項目を強調表示するかどうかを制御します。\",\n\t\t\"常に折りたたみコントロールを表示します。\",\n\t\t\"折りたたみコントロールを表示せず、余白のサイズを小さくします。\",\n\t\t\"マウスがとじしろの上にあるときにのみ、折りたたみコントロールを表示します。\",\n\t\t\"とじしろの折りたたみコントロールを表示するタイミングを制御します。\",\n\t\t\"使用されていないコードのフェードアウトを制御します。\",\n\t\t\"非推奨の変数の取り消し線を制御します。\",\n\t\t\"他の候補の上にスニペットの候補を表示します。\",\n\t\t\"他の候補の下にスニペットの候補を表示します。\",\n\t\t\"他の候補と一緒にスニペットの候補を表示します。\",\n\t\t\"スニペットの候補を表示しません。\",\n\t\t\"他の修正候補と一緒にスニペットを表示するかどうか、およびその並び替えの方法を制御します。\",\n\t\t\"アニメーションでエディターをスクロールするかどうかを制御します。\",\n\t\t\"インライン入力候補が表示されたときに、スクリーン リーダー ユーザーにユーザー補助ヒントを提供するかどうかを制御します。\",\n\t\t\"候補ウィジェットのフォント サイズ。{0} に設定すると、値 {1} が使用されます。\",\n\t\t\"候補ウィジェットの行の高さ。{0} に設定すると、{1} の値が使用されます。最小値は 8 です。\",\n\t\t\"トリガー文字の入力時に候補が自動的に表示されるようにするかどうかを制御します。\",\n\t\t\"常に最初の候補を選択します。\",\n\t\t\"`console.| -> console.log` などと選択対象に関して入力しない限りは、最近の候補を選択します。`log` は最近完了したためです。\",\n\t\t\"これらの候補を完了した以前のプレフィックスに基づいて候補を選択します。例: `co -> console` および `con -> const`。\",\n\t\t\"候補リストを表示するときに候補を事前に選択する方法を制御します。\",\n\t\t\"タブ補完は、tab キーを押したときに最適な候補を挿入します。\",\n\t\t\"タブ補完を無効にします。\",\n\t\t\"プレフィックスが一致する場合に、タブでスニペットを補完します。\\'quickSuggestions\\' が無効な場合に最適です。\",\n\t\t\"タブ補完を有効にします。\",\n\t\t\"通常とは異なる行の終端文字は自動的に削除される。\",\n\t\t\"通常とは異なる行の終端文字は無視される。\",\n\t\t\"通常とは異なる行の終端文字の削除プロンプトが表示される。\",\n\t\t\"問題を起こす可能性がある、普通ではない行終端記号は削除してください。\",\n\t\t\"空白の挿入や削除はタブ位置に従って行われます。\",\n\t\t\"既定の改行ルールを使用します。\",\n\t\t\"中国語/日本語/韓国語 (CJK) のテキストには単語区切りを使用しないでください。CJK 以外のテキストの動作は、通常の場合と同じです。\",\n\t\t\"中国語/日本語/韓国語 (CJK) テキストに使用される単語区切り規則を制御します。\",\n\t\t\"単語に関連したナビゲーションまたは操作を実行するときに、単語の区切り文字として使用される文字。\",\n\t\t\"行を折り返しません。\",\n\t\t\"行をビューポートの幅で折り返します。\",\n\t\t\"`#editor.wordWrapColumn#` で行を折り返します。\",\n\t\t\"ビューポートと `#editor.wordWrapColumn#` の最小値で行を折り返します。\",\n\t\t\"行の折り返し方法を制御します。\",\n\t\t\"`#editor.wordWrap#` が `wordWrapColumn` または `bounded` の場合に、エディターの折り返し桁を制御します。\",\n\t\t\"既定のドキュメント カラー プロバイダーを使用してインラインの色の装飾を表示するかどうかを制御します\",\n\t\t\"エディターがタブを受け取るか、ワークベンチに委ねてナビゲーションするかを制御します。\",\n\t],\n\t\"vs/editor/common/core/editorColorRegistry\": [\n\t\t\"カーソル位置の行を強調表示する背景色。\",\n\t\t\"カーソル位置の行の境界線を強調表示する背景色。\",\n\t\t\"(Quick Open や検出機能などにより) 強調表示されている範囲の色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"強調表示された範囲の境界線の背景色。\",\n\t\t\"強調表示された記号の背景色 (定義へ移動、次または前の記号へ移動など)。基になる装飾が覆われないようにするため、色を不透明にすることはできません。\",\n\t\t\"強調表示された記号の周りの境界線の背景色。\",\n\t\t\"エディターのカーソルの色。\",\n\t\t\"選択された文字列の背景色です。選択された文字列の背景色をカスタマイズ出来ます。\",\n\t\t\"エディターのスペース文字の色。\",\n\t\t\"エディターの行番号の色。\",\n\t\t\"エディター インデント ガイドの色。\",\n\t\t\"\\'editorIndentGuide.background\\' は非推奨です。代わりに \\'editorIndentGuide.background1\\' を使用してください。\",\n\t\t\"アクティブなエディターのインデント ガイドの色。\",\n\t\t\"\\'editorIndentGuide.activeBackground\\' は非推奨です。代わりに \\'editorIndentGuide.activeBackground1\\' を使用してください。\",\n\t\t\"エディター インデント ガイドの色 (1)。\",\n\t\t\"エディター インデント ガイドの色 (2)。\",\n\t\t\"エディター インデント ガイドの色 (3)。\",\n\t\t\"エディター インデント ガイドの色 (4)。\",\n\t\t\"エディター インデント ガイドの色 (5)。\",\n\t\t\"エディター インデント ガイドの色 (6)。\",\n\t\t\"アクティブなエディターのインデント ガイドの色 (1)。\",\n\t\t\"アクティブなエディターのインデント ガイドの色 (2)。\",\n\t\t\"アクティブなエディターのインデント ガイドの色 (3)。\",\n\t\t\"アクティブなエディターのインデント ガイドの色 (4)。\",\n\t\t\"アクティブなエディターのインデント ガイドの色 (5)。\",\n\t\t\"アクティブなエディターのインデント ガイドの色 (6)。\",\n\t\t\"エディターのアクティブ行番号の色\",\n\t\t\"id は使用しないでください。代わりに \\'EditorLineNumber.activeForeground\\' を使用してください。\",\n\t\t\"エディターのアクティブ行番号の色\",\n\t\t\"editor.renderFinalNewline が dimmed に設定されている場合のエディターの最終行の色。\",\n\t\t\"エディター ルーラーの色。\",\n\t\t\"CodeLens エディターの前景色。\",\n\t\t\"一致するかっこの背景色\",\n\t\t\"一致するかっこ内のボックスの色\",\n\t\t\"概要ルーラーの境界色。\",\n\t\t\"エディターの概要ルーラーの背景色。\",\n\t\t\"エディターの余白の背景色。余白にはグリフ マージンと行番号が含まれます。\",\n\t\t\"エディターでの不要な (未使用の) ソース コードの罫線の色。\",\n\t\t\"エディター内の不要な (未使用の) ソース コードの不透明度。たとえば、\\\"#000000c0\\\" は不透明度 75% でコードを表示します。ハイ コントラストのテーマの場合、\\'editorUnnecessaryCode.border\\' テーマ色を使用して、不要なコードをフェードアウトするのではなく下線を付けます。\",\n\t\t\"エディター内の透かし文字の境界線の色です。\",\n\t\t\"エディターの透かし文字の前景色です。\",\n\t\t\"エディターのゴースト テキストの背景色。\",\n\t\t\"範囲強調表示のための概要ルーラー マーカーの色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"エラーを示す概要ルーラーのマーカー色。\",\n\t\t\"警告を示す概要ルーラーのマーカー色。\",\n\t\t\"情報を示す概要ルーラーのマーカー色。\",\n\t\t\"角かっこ (1) の前景色。角かっこのペアの色付けを有効にする必要があります。\",\n\t\t\"角かっこ (2) の前景色。角かっこのペアの色付けを有効にする必要があります。\",\n\t\t\"角かっこ (3) の前景色。角かっこのペアの色付けを有効にする必要があります。\",\n\t\t\"角かっこ (4) の前景色。角かっこのペアの色付けを有効にする必要があります。\",\n\t\t\"角かっこ (5) の前景色。角かっこのペアの色付けを有効にする必要があります。\",\n\t\t\"角かっこ (6) の前景色。角かっこのペアの色付けを有効にする必要があります。\",\n\t\t\"予期しないブラケットの前景色。\",\n\t\t\"非アクティブな角かっこのペア ガイドの背景色 (1)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"非アクティブな角かっこのペア ガイドの背景色 (2)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"非アクティブな角かっこのペア ガイドの背景色 (3)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"非アクティブな角かっこのペア ガイドの背景色 (4)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"非アクティブな角かっこのペア ガイドの背景色 (5)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"非アクティブな角かっこのペア ガイドの背景色 (6)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"アクティブな角かっこのペア ガイドの背景色 (1)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"アクティブな角かっこのペア ガイドの背景色 (2)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"アクティブな角かっこのペア ガイドの背景色 (3)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"アクティブな角かっこのペア ガイドの背景色 (4)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"アクティブな角かっこのペア ガイドの背景色 (5)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"アクティブな角かっこのペア ガイドの背景色 (6)。角かっこのペア ガイドを有効にする必要があります。\",\n\t\t\"Unicode 文字を強調表示するために使用される境界線の色。\",\n\t\t\"Unicode 文字を強調表示するために使用される背景色。\",\n\t],\n\t\"vs/editor/common/editorContextKeys\": [\n\t\t\"エディターのテキストにフォーカスがある (カーソルが点滅している) かどうか\",\n\t\t\"エディターまたはエディター ウィジェットにフォーカスがある (例: 検索ウィジェットにフォーカスがある) かどうか\",\n\t\t\"エディターまたはリッチ テキスト入力にフォーカスがある (カーソルが点滅している) かどうか\",\n\t\t\"エディターが読み取り専用かどうか\",\n\t\t\"コンテキストが差分エディターであるかどうか\",\n\t\t\"コンテキストが埋め込み差分エディターであるかどうか\",\n\t\t\"コンテキストがマルチ差分エディターであるかどうか\",\n\t\t\"マルチ差分エディター内のすべてのファイルを折りたたむかどうか\",\n\t\t\"差分エディターに変更があるかどうか\",\n\t\t\"移動されたコード ブロックが比較対象として選択されているかどうか\",\n\t\t\"アクセシビリティの高い差分ビューアーが表示されているかどうか\",\n\t\t\"差分エディターがインライン ブレークポイントを並べてレンダリングするかどうか\",\n\t\t\"`editor.columnSelection` が有効になっているかどうか\",\n\t\t\"エディターでテキストが選択されているかどうか\",\n\t\t\"エディターに複数の選択範囲があるかどうか\",\n\t\t\"`Tab` によってフォーカスがエディターの外に移動するかどうか\",\n\t\t\"エディターのホバーが表示されているかどうか\",\n\t\t\"エディターのホバーがフォーカスされているかどうか\",\n\t\t\"固定スクロールがフォーカスされているかどうか\",\n\t\t\"固定スクロールが表示されているかどうか\",\n\t\t\"スタンドアロン カラー ピッカーを表示するかどうか\",\n\t\t\"スタンドアロン カラー ピッカーがフォーカスされているかどうか\",\n\t\t\"エディターがより大きなエディター (例: Notebooks) の一部であるかどうか\",\n\t\t\"エディターの言語識別子\",\n\t\t\"エディターに入力候補項目プロバイダーがあるかどうか\",\n\t\t\"エディターにコード アクション プロバイダーがあるかどうか\",\n\t\t\"エディターにコード レンズ プロバイダーがあるかどうか\",\n\t\t\"エディターに定義プロバイダーがあるかどうか\",\n\t\t\"エディターに宣言プロバイダーがあるかどうか\",\n\t\t\"エディターに実装プロバイダーがあるかどうか\",\n\t\t\"エディターに型定義プロバイダーがあるかどうか\",\n\t\t\"エディターにホバー プロバイダーがあるかどうか\",\n\t\t\"エディターにドキュメント強調表示プロバイダーがあるかどうか\",\n\t\t\"エディターにドキュメント シンボル プロバイダーがあるかどうか\",\n\t\t\"エディターに参照プロバイダーがあるかどうか\",\n\t\t\"エディターに名前変更プロバイダーがあるかどうか\",\n\t\t\"エディターにシグネチャ ヘルプ プロバイダーがあるかどうか\",\n\t\t\"エディターにインライン ヒント プロバイダーがあるかどうか\",\n\t\t\"エディターにドキュメント書式設定プロバイダーがあるかどうか\",\n\t\t\"エディターにドキュメント選択書式設定プロバイダーがあるかどうか\",\n\t\t\"エディターに複数のドキュメント書式設定プロバイダーがあるかどうか\",\n\t\t\"エディターに複数のドキュメント選択書式設定プロバイダーがあるかどうか\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"配列\",\n\t\t\"ブール値\",\n\t\t\"クラス\",\n\t\t\"定数\",\n\t\t\"コンストラクター\",\n\t\t\"列挙型\",\n\t\t\"列挙型メンバー\",\n\t\t\"イベント\",\n\t\t\"フィールド\",\n\t\t\"ファイル\",\n\t\t\"関数\",\n\t\t\"インターフェイス\",\n\t\t\"キー\",\n\t\t\"メソッド\",\n\t\t\"モジュール\",\n\t\t\"名前空間\",\n\t\t\"NULL\",\n\t\t\"数値\",\n\t\t\"オブジェクト\",\n\t\t\"演算子\",\n\t\t\"パッケージ\",\n\t\t\"プロパティ\",\n\t\t\"文字列\",\n\t\t\"構造体\",\n\t\t\"型パラメーター\",\n\t\t\"変数\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/common/languages/modesRegistry\": [\n\t\t\"プレーンテキスト\",\n\t],\n\t\"vs/editor/common/model/editStack\": [\n\t\t\"入力しています\",\n\t],\n\t\"vs/editor/common/standaloneStrings\": [\n\t\t\"開発者: トークンの検査\",\n\t\t\"行/列に移動する...\",\n\t\t\"すべてのクイック アクセス プロバイダーを表示\",\n\t\t\"コマンド パレット\",\n\t\t\"コマンドの表示と実行\",\n\t\t\"シンボルに移動...\",\n\t\t\"カテゴリ別のシンボルへ移動...\",\n\t\t\"エディターのコンテンツ\",\n\t\t\"アクティビティ オプションを表示するには、Alt+F1 キーを押します。\",\n\t\t\"ハイ コントラスト テーマの切り替え\",\n\t\t\"{1} 個のファイルに {0} 個の編集が行われました\",\n\t],\n\t\"vs/editor/common/viewLayout/viewLineRenderer\": [\n\t\t\"表示数を増やす ({0})\",\n\t\t\"{0} 文字\",\n\t],\n\t\"vs/editor/contrib/anchorSelect/browser/anchorSelect\": [\n\t\t\"選択アンカー\",\n\t\t\"アンカーが {0}:{1} に設定されました\",\n\t\t\"選択アンカーの設定\",\n\t\t\"選択アンカーへ移動\",\n\t\t\"アンカーからカーソルへ選択\",\n\t\t\"選択アンカーの取り消し\",\n\t],\n\t\"vs/editor/contrib/bracketMatching/browser/bracketMatching\": [\n\t\t\"一致するブラケットを示す概要ルーラーのマーカー色。\",\n\t\t\"ブラケットへ移動\",\n\t\t\"ブラケットに選択\",\n\t\t\"かっこを外す\",\n\t\t\"ブラケットに移動(&&B)\",\n\t\t\"中かっこまたは波かっこを含むテキストを選択します\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/caretOperations\": [\n\t\t\"選択したテキストを左に移動\",\n\t\t\"選択したテキストを右に移動\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/transpose\": [\n\t\t\"文字の入れ替え\",\n\t],\n\t\"vs/editor/contrib/clipboard/browser/clipboard\": [\n\t\t\"切り取り(&&T)\",\n\t\t\"切り取り\",\n\t\t\"切り取り\",\n\t\t\"切り取り\",\n\t\t\"コピー(&&C)\",\n\t\t\"コピー\",\n\t\t\"コピー\",\n\t\t\"コピー\",\n\t\t\"形式を指定してコピー\",\n\t\t\"形式を指定してコピー\",\n\t\t\"共有\",\n\t\t\"共有\",\n\t\t\"共有\",\n\t\t\"貼り付け(&&P)\",\n\t\t\"貼り付け\",\n\t\t\"貼り付け\",\n\t\t\"貼り付け\",\n\t\t\"構文を強調表示してコピー\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeAction\": [\n\t\t\"コード アクションの適用中に不明なエラーが発生しました\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionCommands\": [\n\t\t\"実行するコード アクションの種類。\",\n\t\t\"返されたアクションが適用されるタイミングを制御します。\",\n\t\t\"最初に返されたコード アクションを常に適用します。\",\n\t\t\"最初に返されたコード アクション以外に返されたコード アクションがない場合は、そのアクションを適用します。\",\n\t\t\"返されたコード アクションは適用しないでください。\",\n\t\t\"優先コード アクションのみを返すかどうかを制御します。\",\n\t\t\"クイック フィックス...\",\n\t\t\"利用可能なコード アクションはありません\",\n\t\t\"\\'{0}\\' に対して使用できる優先コード アクションがありません\",\n\t\t\"{0}\\' に対して使用できるコード アクションがありません\",\n\t\t\"使用できる優先コード アクションがありません\",\n\t\t\"利用可能なコード アクションはありません\",\n\t\t\"リファクター...\",\n\t\t\"\\'{0}\\' に対して使用できる優先リファクタリングがありません\",\n\t\t\"\\'{0}\\' に対して使用できるリファクタリングがありません\",\n\t\t\"使用できる優先リファクタリングがありません\",\n\t\t\"利用可能なリファクタリングはありません\",\n\t\t\"ソース アクション...\",\n\t\t\"\\'{0}\\' に対して使用できる優先ソース アクションがありません\",\n\t\t\"\\'{0}\\' に対して使用できるソース アクションがありません\",\n\t\t\"使用できる優先ソース アクションがありません\",\n\t\t\"利用可能なソース アクションはありません\",\n\t\t\"インポートを整理\",\n\t\t\"利用可能なインポートの整理アクションはありません\",\n\t\t\"すべて修正\",\n\t\t\"すべてを修正するアクションは利用できません\",\n\t\t\"自動修正...\",\n\t\t\"利用可能な自動修正はありません\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionContributions\": [\n\t\t\"コード アクション メニューでのグループ ヘッダーの表示の有効/無効を切り替えます。\",\n\t\t\"現在診断を行っていないときに、行内の最も近い クイック修正 を表示する機能を有効または無効にします。\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionController\": [\n\t\t\"コンテキスト: {1} 行 {2} 列 の {0}。\",\n\t\t\"無効なものを非表示\",\n\t\t\"無効を表示\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionMenu\": [\n\t\t\"その他の操作...\",\n\t\t\"クイック修正\",\n\t\t\"抽出\",\n\t\t\"インライン\",\n\t\t\"再書き込みする\",\n\t\t\"移動\",\n\t\t\"ブロックの挿入\",\n\t\t\"ソース アクション...\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/lightBulbWidget\": [\n\t\t\"コードアクションを表示します。使用可能な優先のクイック修正 ({0})\",\n\t\t\"コード アクションの表示 ({0})\",\n\t\t\"コード アクションの表示\",\n\t\t\"インライン チャットを開始する ({0})\",\n\t\t\"インライン チャットを開始する\",\n\t\t\"AI アクションのトリガー\",\n\t],\n\t\"vs/editor/contrib/codelens/browser/codelensController\": [\n\t\t\"現在の行のコード レンズ コマンドを表示\",\n\t\t\"コマンドの選択\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/colorPickerWidget\": [\n\t\t\"クリックして色オプションを切り替えます (rgb/hsl/hex)\",\n\t\t\"カラー ピッカーを閉じるアイコン\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions\": [\n\t\t\"スタンドアロン カラー ピッカーの表示またはフォーカス\",\n\t\t\"スタンドアロン カラー ピッカーの表示またはフォーカス(&S)\",\n\t\t\"カラー ピッカーを非表示にする\",\n\t\t\"スタンドアロン カラー ピッカーで色を挿入\",\n\t],\n\t\"vs/editor/contrib/comment/browser/comment\": [\n\t\t\"行コメントの切り替え\",\n\t\t\"行コメントの切り替え(&&T)\",\n\t\t\"行コメントの追加\",\n\t\t\"行コメントの削除\",\n\t\t\"ブロック コメントの切り替え\",\n\t\t\"ブロック コメントの切り替え(&&B)\",\n\t],\n\t\"vs/editor/contrib/contextmenu/browser/contextmenu\": [\n\t\t\"ミニマップ\",\n\t\t\"レンダリング文字\",\n\t\t\"垂直方向のサイズ\",\n\t\t\"均等\",\n\t\t\"塗りつぶし\",\n\t\t\"サイズに合わせて調整\",\n\t\t\"スライダー\",\n\t\t\"マウス オーバー\",\n\t\t\"常に\",\n\t\t\"エディターのコンテキスト メニューの表示\",\n\t],\n\t\"vs/editor/contrib/cursorUndo/browser/cursorUndo\": [\n\t\t\"カーソルを元に戻す\",\n\t\t\"カーソルのやり直し\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution\": [\n\t\t\"貼り付けのオプション...\",\n\t\t\"適用しようとする貼り付け編集の ID。指定しない場合、エディターにピッカーが表示されます。\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController\": [\n\t\t\"貼り付けウィジェットが表示されているかどうか\",\n\t\t\"貼り付けオプションを表示...\",\n\t\t\"貼り付けハンドラーを実行しています。クリックしてキャンセルします\",\n\t\t\"貼り付け操作の選択\",\n\t\t\"貼り付けハンドラーを実行しています...\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders\": [\n\t\t\"ビルトイン\",\n\t\t\"プレーンテキストの挿入\",\n\t\t\"URI の挿入\",\n\t\t\"URI の挿入\",\n\t\t\"パスの挿入\",\n\t\t\"パスの挿入\",\n\t\t\"相対パスの挿入\",\n\t\t\"相対パスの挿入\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution\": [\n\t\t\"特定の MIME タイプのコンテンツに使用する既定のドロップ プロバイダーを構成します。\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController\": [\n\t\t\"ドロップ ウィジェットが表示されているかどうか\",\n\t\t\"ドロップ オプションを表示...\",\n\t\t\"ドロップ ハンドラーを実行しています。クリックしてキャンセルします\",\n\t],\n\t\"vs/editor/contrib/editorState/browser/keybindingCancellation\": [\n\t\t\"エディターで取り消し可能な操作 (\\'参照をここに表示\\' など) を実行するかどうか\",\n\t],\n\t\"vs/editor/contrib/find/browser/findController\": [\n\t\t\"ファイルが大きすぎるため、すべての置換アクションを実行できません。\",\n\t\t\"検索\",\n\t\t\"検索(&&F)\",\n\t\t\"\\\"正規表現を使用する\\\" フラグをオーバーライドします。\\r\\nフラグは今後保存されません。\\r\\n0: 何もしない\\r\\n1: True\\r\\n2: False\",\n\t\t\"\\\"単語単位で検索する\\\" フラグをオーバーライドします。\\r\\nフラグは今後保存されません。\\r\\n0: 何もしない\\r\\n1: True\\r\\n2: False\",\n\t\t\"\\\"数式ケース\\\" フラグをオーバーライドします。\\r\\nフラグは今後保存されません。\\r\\n0: 何もしない\\r\\n1: True\\r\\n2: False\",\n\t\t\"\\\"ケースの保持\\\" フラグをオーバーライドします。\\r\\nフラグは今後保存されません。\\r\\n0: 何もしない\\r\\n1: True\\r\\n2: False\",\n\t\t\"引数を使用した検索\",\n\t\t\"選択範囲で検索\",\n\t\t\"次を検索\",\n\t\t\"前を検索\",\n\t\t\"[一致] に移動...\",\n\t\t\"一致しません。他の項目を検索してみてください。\",\n\t\t\"特定の一致に移動する数値を入力します (1 から {0})\",\n\t\t\"1 ~ {0} の数を入力してください。\",\n\t\t\"1 ~ {0} の数を入力してください。\",\n\t\t\"次の選択項目を検索\",\n\t\t\"前の選択項目を検索\",\n\t\t\"置換\",\n\t\t\"置換(&&R)\",\n\t],\n\t\"vs/editor/contrib/find/browser/findWidget\": [\n\t\t\"エディターの検索ウィジェット内の \\'選択範囲を検索\\' のアイコン。\",\n\t\t\"エディターの検索ウィジェットが折りたたまれていることを示すアイコン。\",\n\t\t\"エディターの検索ウィジェットが展開されていることを示すアイコン。\",\n\t\t\"エディターの検索ウィジェット内の \\'置換\\' のアイコン。\",\n\t\t\"エディターの検索ウィジェット内の \\'すべて置換\\' のアイコン。\",\n\t\t\"エディターの検索ウィジェット内の \\'前を検索\\' のアイコン。\",\n\t\t\"エディターの検索ウィジェット内の \\'次を検索\\' のアイコン。\",\n\t\t\"検索/置換\",\n\t\t\"検索\",\n\t\t\"検索\",\n\t\t\"前の一致項目\",\n\t\t\"次の一致項目\",\n\t\t\"選択範囲を検索\",\n\t\t\"閉じる\",\n\t\t\"置換\",\n\t\t\"置換\",\n\t\t\"置換\",\n\t\t\"すべて置換\",\n\t\t\"置換の切り替え\",\n\t\t\"最初の {0} 件の結果だけが強調表示されますが、すべての検索操作はテキスト全体で機能します。\",\n\t\t\"{0} / {1} 件\",\n\t\t\"結果はありません。\",\n\t\t\"{0} が見つかりました\",\n\t\t\"{0} が \\'{1}\\' で見つかりました\",\n\t\t\"{0} は \\'{1}\\' で {2} に見つかりました\",\n\t\t\"{0} が \\'{1}\\' で見つかりました\",\n\t\t\"Ctrl + Enter キーを押すと、すべて置換するのではなく、改行が挿入されるようになりました。editor.action.replaceAll のキーバインドを変更して、この動作をオーバーライドできます。\",\n\t],\n\t\"vs/editor/contrib/folding/browser/folding\": [\n\t\t\"展開\",\n\t\t\"再帰的に展開する\",\n\t\t\"折りたたみ\",\n\t\t\"折りたたみの切り替え\",\n\t\t\"再帰的に折りたたむ\",\n\t\t\"すべてのブロック コメントの折りたたみ\",\n\t\t\"すべての領域を折りたたむ\",\n\t\t\"すべての領域を展開\",\n\t\t\"選択した項目を除くすべて折りたたみ\",\n\t\t\"選択した項目を除くすべて展開\",\n\t\t\"すべて折りたたみ\",\n\t\t\"すべて展開\",\n\t\t\"親フォールドに移動する\",\n\t\t\"前のフォールディング範囲に移動する\",\n\t\t\"次のフォールディング範囲に移動する\",\n\t\t\"選択範囲から折りたたみ範囲を作成する\",\n\t\t\"手動折りたたみ範囲を削除する\",\n\t\t\"レベル {0} で折りたたむ\",\n\t],\n\t\"vs/editor/contrib/folding/browser/foldingDecorations\": [\n\t\t\"折り曲げる範囲の背景色。基の装飾を隠さないように、色は不透明であってはなりません。\",\n\t\t\"エディターの余白にある折りたたみコントロールの色。\",\n\t\t\"エディターのグリフ余白内の展開された範囲のアイコン。\",\n\t\t\"エディターのグリフ余白内の折りたたまれた範囲のアイコン。\",\n\t\t\"エディターのグリフ余白内の折りたたまれた範囲のアイコン。\",\n\t\t\"エディターのグリフ余白内で手動で展開された範囲のアイコン。\",\n\t],\n\t\"vs/editor/contrib/fontZoom/browser/fontZoom\": [\n\t\t\"エディターのフォントを拡大\",\n\t\t\"エディターのフォントを縮小\",\n\t\t\"エディターのフォントのズームをリセット\",\n\t],\n\t\"vs/editor/contrib/format/browser/formatActions\": [\n\t\t\"ドキュメントのフォーマット\",\n\t\t\"選択範囲のフォーマット\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoError\": [\n\t\t\"次の問題 (エラー、警告、情報) へ移動\",\n\t\t\"次のマーカーへ移動するためのアイコン。\",\n\t\t\"前の問題 (エラー、警告、情報) へ移動\",\n\t\t\"前のマーカーへ移動するためのアイコン。\",\n\t\t\"ファイル内の次の問題 (エラー、警告、情報) へ移動\",\n\t\t\"次の問題箇所(&&P)\",\n\t\t\"ファイル内の前の問題 (エラー、警告、情報) へ移動\",\n\t\t\"前の問題箇所(&&P)\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoErrorWidget\": [\n\t\t\"エラー\",\n\t\t\"警告\",\n\t\t\"情報\",\n\t\t\"ヒント\",\n\t\t\"{0} ({1})。\",\n\t\t\"{1} 件中 {0} 件の問題\",\n\t\t\"問題 {0} / {1}\",\n\t\t\"エディターのマーカー ナビゲーション ウィジェットのエラーの色。\",\n\t\t\"エディターのマーカー ナビゲーション ウィジェット エラーの見出しの背景。\",\n\t\t\"エディターのマーカー ナビゲーション ウィジェットの警告の色。\",\n\t\t\"エディターのマーカー ナビゲーション ウィジェット警告の見出しの背景。\",\n\t\t\"エディターのマーカー ナビゲーション ウィジェットの情報の色。\",\n\t\t\"エディターのマーカー ナビゲーション ウィジェット情報の見出しの背景。\",\n\t\t\"エディターのマーカー ナビゲーション ウィジェットの背景。\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/goToCommands\": [\n\t\t\"ピーク\",\n\t\t\"定義\",\n\t\t\"\\'{0}\\' の定義は見つかりません\",\n\t\t\"定義が見つかりません\",\n\t\t\"定義へ移動\",\n\t\t\"定義に移動(&&D)\",\n\t\t\"定義を横に開く\",\n\t\t\"定義をここに表示\",\n\t\t\"宣言\",\n\t\t\"\\'{0}\\' の宣言が見つかりません\",\n\t\t\"宣言が見つかりません\",\n\t\t\"宣言へ移動\",\n\t\t\"宣言へ移動(&&D)\",\n\t\t\"\\'{0}\\' の宣言が見つかりません\",\n\t\t\"宣言が見つかりません\",\n\t\t\"宣言をここに表示\",\n\t\t\"型定義\",\n\t\t\"\\'{0}\\' の型定義が見つかりません\",\n\t\t\"型定義が見つかりません\",\n\t\t\"型定義へ移動\",\n\t\t\"型定義に移動(&&T)\",\n\t\t\"型定義を表示\",\n\t\t\"実装\",\n\t\t\"\\'{0}\\' の実装が見つかりません\",\n\t\t\"実装が見つかりません\",\n\t\t\"実装へ移動\",\n\t\t\"実装箇所に移動(&&I)\",\n\t\t\"実装のピーク\",\n\t\t\"\\'{0}\\' の参照が見つかりません\",\n\t\t\"参照が見つかりません\",\n\t\t\"参照へ移動\",\n\t\t\"参照へ移動(&&R)\",\n\t\t\"参照\",\n\t\t\"参照をここに表示\",\n\t\t\"参照\",\n\t\t\"任意のシンボルへ移動\",\n\t\t\"場所\",\n\t\t\"\\'{0}\\' に一致する結果は見つかりませんでした\",\n\t\t\"参照\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition\": [\n\t\t\"クリックして、{0} の定義を表示します。\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesController\": [\n\t\t\"参照のプレビューが表示されるかどうか (\\'参照のプレビュー\\' または \\'定義をここに表示\\' など)\",\n\t\t\"読み込んでいます...\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree\": [\n\t\t\"{0} 個の参照\",\n\t\t\"{0} 個の参照\",\n\t\t\"参照設定\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget\": [\n\t\t\"プレビューを表示できません\",\n\t\t\"結果はありません。\",\n\t\t\"参照設定\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/referencesModel\": [\n\t\t\"列 {2} の行 {1} の {0}\",\n\t\t\"列 {3} の行 {2} の {1} に {0}\",\n\t\t\"{0} に 1 個のシンボル、完全なパス {1}\",\n\t\t\"{1} に {0} 個のシンボル、完全なパス {2}\",\n\t\t\"一致する項目はありません\",\n\t\t\"{0} に 1 個のシンボルが見つかりました\",\n\t\t\"{1} に {0} 個のシンボルが見つかりました\",\n\t\t\"{1} 個のファイルに {0} 個のシンボルが見つかりました\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/symbolNavigation\": [\n\t\t\"キーボードのみで移動できるシンボルの場所があるかどうか。\",\n\t\t\"{1} のシンボル {0}、次に {2}\",\n\t\t\"シンボル {0}/{1}\",\n\t],\n\t\"vs/editor/contrib/hover/browser/hover\": [\n\t\t\"[表示またはフォーカス] ホバー\",\n\t\t\"ホバーは自動的にフォーカスを取得しません。\",\n\t\t\"ホバーは、それが既に表示されている場合にのみフォーカスを取得します。\",\n\t\t\"ホバーが表示されると、自動的にフォーカスを取得します。\",\n\t\t\"定義プレビューのホバーを表示する\",\n\t\t\"[上にスクロール] ホバー\",\n\t\t\"[下にスクロール] ホバー\",\n\t\t\"[左にスクロール] ホバー\",\n\t\t\"[右にスクロール] ホバー\",\n\t\t\"[ページを上に] ホバー\",\n\t\t\"[ページを下に] ホバー\",\n\t\t\"[上に移動] ホバー\",\n\t\t\"[下に移動] ホバー\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markdownHoverParticipant\": [\n\t\t\"読み込んでいます...\",\n\t\t\"パフォーマンス上の理由から、長い行のためにレンダリングが一時停止されました。これは `editor.stopRenderingLineAfter` で設定できます。\",\n\t\t\"パフォーマンス上の理由からトークン化はスキップされます。その長い行の長さは `editor.maxTokenizationLineLength` で構成できます。\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markerHoverParticipant\": [\n\t\t\"問題の表示\",\n\t\t\"利用できるクイックフィックスはありません\",\n\t\t\"クイックフィックスを確認しています...\",\n\t\t\"利用できるクイックフィックスはありません\",\n\t\t\"クイック フィックス...\",\n\t],\n\t\"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace\": [\n\t\t\"前の値に置換\",\n\t\t\"次の値に置換\",\n\t],\n\t\"vs/editor/contrib/indentation/browser/indentation\": [\n\t\t\"インデントをスペースに変換\",\n\t\t\"インデントをタブに変換\",\n\t\t\"構成されたタブのサイズ\",\n\t\t\"既定のタブ サイズ\",\n\t\t\"現在のタブ サイズ\",\n\t\t\"現在のファイルのタブのサイズを選択\",\n\t\t\"タブによるインデント\",\n\t\t\"スペースによるインデント\",\n\t\t\"タブの表示サイズの変更\",\n\t\t\"内容からインデントを検出\",\n\t\t\"行の再インデント\",\n\t\t\"選択行を再インデント\",\n\t],\n\t\"vs/editor/contrib/inlayHints/browser/inlayHintsHover\": [\n\t\t\"ダブルクリックして挿入する\",\n\t\t\"cmd キーを押しながらクリック\",\n\t\t\"ctrl キーを押しながら クリック\",\n\t\t\"option キーを押しながらクリック\",\n\t\t\"alt キーを押しながらクリック\",\n\t\t\"[定義] ({0}) に移動し、右クリックして詳細を表示します\",\n\t\t\"定義に移動 ({0})\",\n\t\t\"コマンドの実行\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/commands\": [\n\t\t\"次のインライン候補を表示する\",\n\t\t\"前のインライン候補を表示する\",\n\t\t\"インライン候補をトリガーする\",\n\t\t\"インライン提案の次の単語を承諾する\",\n\t\t\"ワードを承諾する\",\n\t\t\"インライン提案の次の行を承諾する\",\n\t\t\"行を承諾する\",\n\t\t\"インライン候補を承諾する\",\n\t\t\"承諾する\",\n\t\t\"インライン候補を非表示にする\",\n\t\t\"常にツール バーを表示する\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/hoverParticipant\": [\n\t\t\"おすすめ:\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys\": [\n\t\t\"インライン候補を表示するかどうか\",\n\t\t\"インライン候補がスペースで始まるかどうか\",\n\t\t\"インライン候補が、タブで挿入されるものよりも小さいスペースで始まるかどうか\",\n\t\t\"現在の候補について候補表示を止めるかどうか\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController\": [\n\t\t\"ユーザー補助対応のビューでこれを検査します ({0})\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget\": [\n\t\t\"次のパラメーター ヒントを表示するためのアイコン。\",\n\t\t\"前のパラメーター ヒントを表示するためのアイコン。\",\n\t\t\"{0} ({1})\",\n\t\t\"前へ\",\n\t\t\"次へ\",\n\t],\n\t\"vs/editor/contrib/lineSelection/browser/lineSelection\": [\n\t\t\"行全体を選択する\",\n\t],\n\t\"vs/editor/contrib/linesOperations/browser/linesOperations\": [\n\t\t\"行を上へコピー\",\n\t\t\"行を上へコピー(&&C)\",\n\t\t\"行を下へコピー\",\n\t\t\"行を下へコピー(&&P)\",\n\t\t\"選択範囲の複製\",\n\t\t\"選択範囲の複製(&&D)\",\n\t\t\"行を上へ移動\",\n\t\t\"行を上へ移動(&&V)\",\n\t\t\"行を下へ移動\",\n\t\t\"行を下へ移動(&&L)\",\n\t\t\"行を昇順に並べ替え\",\n\t\t\"行を降順に並べ替え\",\n\t\t\"重複する行を削除\",\n\t\t\"末尾の空白のトリミング\",\n\t\t\"行の削除\",\n\t\t\"行のインデント\",\n\t\t\"行のインデント解除\",\n\t\t\"行を上に挿入\",\n\t\t\"行を下に挿入\",\n\t\t\"左側をすべて削除\",\n\t\t\"右側をすべて削除\",\n\t\t\"行をつなげる\",\n\t\t\"カーソルの周囲の文字を入れ替える\",\n\t\t\"大文字に変換\",\n\t\t\"小文字に変換\",\n\t\t\"先頭文字を大文字に変換する\",\n\t\t\"スネーク ケースに変換する\",\n\t\t\"キャメル ケースに変換する\",\n\t\t\"Kebab ケースへの変換\",\n\t],\n\t\"vs/editor/contrib/linkedEditing/browser/linkedEditing\": [\n\t\t\"リンクされた編集の開始\",\n\t\t\"エディターが型の名前の自動変更を行うときの背景色です。\",\n\t],\n\t\"vs/editor/contrib/links/browser/links\": [\n\t\t\"このリンクは形式が正しくないため開くことができませんでした: {0}\",\n\t\t\"このリンクはターゲットが存在しないため開くことができませんでした。\",\n\t\t\"コマンドの実行\",\n\t\t\"リンク先を表示\",\n\t\t\"cmd + クリック\",\n\t\t\"ctrl + クリック\",\n\t\t\"option + クリック\",\n\t\t\"alt + クリック\",\n\t\t\"コマンド {0} の実行\",\n\t\t\"リンクを開く\",\n\t],\n\t\"vs/editor/contrib/message/browser/messageController\": [\n\t\t\"エディターに現在インライン メッセージが表示されているかどうか\",\n\t],\n\t\"vs/editor/contrib/multicursor/browser/multicursor\": [\n\t\t\"追加されたカーソル: {0}\",\n\t\t\"追加されたカーソル: {0}\",\n\t\t\"カーソルを上に挿入\",\n\t\t\"カーソルを上に挿入(&&A)\",\n\t\t\"カーソルを下に挿入\",\n\t\t\"カーソルを下に挿入(&&D)\",\n\t\t\"カーソルを行末に挿入\",\n\t\t\"カーソルを行末に挿入(&&U)\",\n\t\t\"カーソルを下に挿入\",\n\t\t\"カーソルを上に挿入\",\n\t\t\"選択した項目を次の一致項目に追加\",\n\t\t\"次の出現個所を追加(&&N)\",\n\t\t\"選択項目を次の一致項目に追加\",\n\t\t\"前の出現箇所を追加(&&R)\",\n\t\t\"最後に選択した項目を次の一致項目に移動\",\n\t\t\"最後に選んだ項目を前の一致項目に移動する\",\n\t\t\"一致するすべての出現箇所を選択します\",\n\t\t\"すべての出現箇所を選択(&&O)\",\n\t\t\"すべての出現箇所を変更\",\n\t\t\"次のカーソルにフォーカス\",\n\t\t\"次のカーソルにフォーカスを合わせる\",\n\t\t\"前のカーソルにフォーカスする\",\n\t\t\"前のカーソルにフォーカスを合わせる\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHints\": [\n\t\t\"パラメーター ヒントをトリガー\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHintsWidget\": [\n\t\t\"次のパラメーター ヒントを表示するためのアイコン。\",\n\t\t\"前のパラメーター ヒントを表示するためのアイコン。\",\n\t\t\"{0}、ヒント\",\n\t\t\"パラメーター ヒント内のアクティブな項目の前景色。\",\n\t],\n\t\"vs/editor/contrib/peekView/browser/peekView\": [\n\t\t\"現在のコード エディターがプレビュー内に埋め込まれるかどうか\",\n\t\t\"閉じる\",\n\t\t\"ピーク ビューのタイトル領域の背景色。\",\n\t\t\"ピーク ビュー タイトルの色。\",\n\t\t\"ピーク ビューのタイトル情報の色。\",\n\t\t\"ピーク ビューの境界と矢印の色。\",\n\t\t\"ピーク ビュー結果リストの背景色。\",\n\t\t\"ピーク ビュー結果リストのライン ノードの前景色。\",\n\t\t\"ピーク ビュー結果リストのファイル ノードの前景色。\",\n\t\t\"ピーク ビュー結果リストの選択済みエントリの背景色。\",\n\t\t\"ピーク ビュー結果リストの選択済みエントリの前景色。\",\n\t\t\"ピーク ビュー エディターの背景色。\",\n\t\t\"ピーク ビュー エディターの余白の背景色。\",\n\t\t\"ピーク ビュー エディターでの固定スクロールの背景色。\",\n\t\t\"ピーク ビュー結果リストの一致した強調表示色。\",\n\t\t\"ピーク ビュー エディターの一致した強調表示色。\",\n\t\t\"ピーク ビュー エディターの一致した強調境界色。\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess\": [\n\t\t\"最初にテキスト エディターを開いて、行に移動します。\",\n\t\t\"行 {0}、文字 {1} に移動します。\",\n\t\t\"{0} 行に移動します。\",\n\t\t\"現在の行: {0}、文字: {1}。移動先となる、1 から {2} までの行番号を入力します。\",\n\t\t\"現在の行: {0}、文字: {1}。移動先の行番号を入力します。\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess\": [\n\t\t\"シンボルに移動するには、まずシンボル情報を含むテキスト エディターを開きます。\",\n\t\t\"アクティブなテキスト エディターでは、シンボル情報は表示されません。\",\n\t\t\"一致するエディター シンボルがありません\",\n\t\t\"エディター シンボルがありません\",\n\t\t\"横に並べて開く\",\n\t\t\"一番下で開く\",\n\t\t\"シンボル ({0})\",\n\t\t\"プロパティ ({0})\",\n\t\t\"メソッド ({0})\",\n\t\t\"関数 ({0})\",\n\t\t\"コンストラクター ({0})\",\n\t\t\"変数 ({0})\",\n\t\t\"クラス ({0})\",\n\t\t\"構造体 ({0})\",\n\t\t\"イベント ({0})\",\n\t\t\"演算子 ({0})\",\n\t\t\"インターフェイス ({0})\",\n\t\t\"名前空間 ({0})\",\n\t\t\"パッケージ ({0})\",\n\t\t\"型パラメーター ({0})\",\n\t\t\"モジュール ({0})\",\n\t\t\"プロパティ ({0})\",\n\t\t\"列挙型 ({0})\",\n\t\t\"列挙型メンバー ({0})\",\n\t\t\"文字列 ({0})\",\n\t\t\"ファイル ({0})\",\n\t\t\"配列 ({0})\",\n\t\t\"数値 ({0})\",\n\t\t\"ブール値 ({0})\",\n\t\t\"オブジェクト ({0})\",\n\t\t\"キー ({0})\",\n\t\t\"フィールド ({0})\",\n\t\t\"定数 ({0})\",\n\t],\n\t\"vs/editor/contrib/readOnlyMessage/browser/contribution\": [\n\t\t\"読み取り専用の入力では編集できません\",\n\t\t\"読み取り専用のエディターは編集できません\",\n\t],\n\t\"vs/editor/contrib/rename/browser/rename\": [\n\t\t\"結果がありません。\",\n\t\t\"名前変更の場所を解決しようとして不明なエラーが発生しました\",\n\t\t\"名前を \\'{0}\\' から \\'{1}\\' に変更しています\",\n\t\t\"{0} の名前を {1} に変更しています\",\n\t\t\"\\'{0}\\' から \\'{1}\\' への名前変更が正常に完了しました。概要: {2}\",\n\t\t\"名前の変更で編集を適用できませんでした\",\n\t\t\"名前の変更によって編集の計算に失敗しました\",\n\t\t\"シンボルの名前変更\",\n\t\t\"名前を変更する前に変更をプレビューする機能を有効または無効にする\",\n\t],\n\t\"vs/editor/contrib/rename/browser/renameInputField\": [\n\t\t\"名前の変更入力ウィジェットが表示されるかどうか\",\n\t\t\"名前変更入力。新しい名前を入力し、Enter キーを押してコミットしてください。\",\n\t\t\"名前を変更するには {0}、プレビューするには {1}\",\n\t],\n\t\"vs/editor/contrib/smartSelect/browser/smartSelect\": [\n\t\t\"選択範囲を拡張\",\n\t\t\"選択範囲の展開(&&E)\",\n\t\t\"選択範囲を縮小\",\n\t\t\"選択範囲の縮小(&&S)\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetController2\": [\n\t\t\"現在のエディターがスニペット モードであるかどうか\",\n\t\t\"スニペット モードのときに、次のタブ位置があるかどうか\",\n\t\t\"スニペット モードのときに、前のタブ位置があるかどうか\",\n\t\t\"次のプレースホルダーに移動...\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetVariables\": [\n\t\t\"日曜日\",\n\t\t\"月曜日\",\n\t\t\"火曜日\",\n\t\t\"水曜日\",\n\t\t\"木曜日\",\n\t\t\"金曜日\",\n\t\t\"土曜日\",\n\t\t\"日\",\n\t\t\"月\",\n\t\t\"火\",\n\t\t\"水\",\n\t\t\"木\",\n\t\t\"金\",\n\t\t\"土\",\n\t\t\"1 月\",\n\t\t\"2 月\",\n\t\t\"3 月\",\n\t\t\"4 月\",\n\t\t\"5 月\",\n\t\t\"6 月\",\n\t\t\"7 月\",\n\t\t\"8 月\",\n\t\t\"9 月\",\n\t\t\"10 月\",\n\t\t\"11 月\",\n\t\t\"12 月\",\n\t\t\"1 月\",\n\t\t\"2 月\",\n\t\t\"3 月\",\n\t\t\"4 月\",\n\t\t\"5 月\",\n\t\t\"6 月\",\n\t\t\"7 月\",\n\t\t\"8 月\",\n\t\t\"9 月\",\n\t\t\"10 月\",\n\t\t\"11 月\",\n\t\t\"12 月\",\n\t],\n\t\"vs/editor/contrib/stickyScroll/browser/stickyScrollActions\": [\n\t\t\"固定スクロールの切り替え\",\n\t\t\"固定スクロールの切り替え(&&T)\",\n\t\t\"固定スクロール\",\n\t\t\"固定スクロール(&&S)\",\n\t\t\"固定スクロールへのフォーカス\",\n\t\t\"固定スクロールへのフォーカス(&F)\",\n\t\t\"次の固定スクロール行を選択\",\n\t\t\"前の固定スクロール行を選択\",\n\t\t\"フォーカスされた固定スクロール行に移動\",\n\t\t\"エディターを選択\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggest\": [\n\t\t\"候補がフォーカスされているかどうか\",\n\t\t\"候補の詳細が表示されるかどうか\",\n\t\t\"選択する複数の候補があるかどうか\",\n\t\t\"現在の候補を挿入したとき、変更を行うか、または既に入力した内容をすべて入力するかどうか\",\n\t\t\"Enter キーを押したときに候補を挿入するかどうか\",\n\t\t\"現在の候補に挿入と置換の動作があるかどうか\",\n\t\t\"既定の動作が挿入または置換であるかどうか\",\n\t\t\"現在の候補からの詳細の解決をサポートするかどうか\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestController\": [\n\t\t\"{1} が追加編集した \\'{0}\\' を受け入れる\",\n\t\t\"候補をトリガー\",\n\t\t\"挿入\",\n\t\t\"挿入\",\n\t\t\"置換\",\n\t\t\"置換\",\n\t\t\"挿入\",\n\t\t\"表示を減らす\",\n\t\t\"さらに表示\",\n\t\t\"候補のウィジェットのサイズをリセット\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidget\": [\n\t\t\"候補のウィジェットの背景色。\",\n\t\t\"候補ウィジェットの境界線色。\",\n\t\t\"候補ウィジェットの前景色。\",\n\t\t\"候補ウィジェット内で選択済み入力の前景色。\",\n\t\t\"候補ウィジェット内で選択済み入力のアイコン前景色。\",\n\t\t\"候補ウィジェット内で選択済みエントリの背景色。\",\n\t\t\"候補のウィジェット内で一致したハイライトの色。\",\n\t\t\"項目がフォーカスされている場合に、候補ウィジェットでの一致の強調表示の色です。\",\n\t\t\"ウィジェット状態の提案の前景色。\",\n\t\t\"読み込んでいます...\",\n\t\t\"候補はありません。\",\n\t\t\"提案\",\n\t\t\"{0} {1}、{2}\",\n\t\t\"{0} {1}\",\n\t\t\"{0}、 {1}\",\n\t\t\"{0}、ドキュメント: {1}\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetDetails\": [\n\t\t\"閉じる\",\n\t\t\"読み込んでいます...\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetRenderer\": [\n\t\t\"提案ウィジェットの詳細情報のアイコン。\",\n\t\t\"詳細を参照\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetStatus\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/symbolIcons/browser/symbolIcons\": [\n\t\t\"配列記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"ブール値記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"クラス記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"色記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"定数記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"コンストラクター記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"列挙子記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"列挙子メンバー記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"イベント記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"フィールド記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"ファイル記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"フォルダー記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"関数記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"インターフェイス記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"キー記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"キーワード記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"メソッド記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"モジュール記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"名前空間記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"Null 記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"数値記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"オブジェクト記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"演算子記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"パッケージ記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"プロパティ記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"参照記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"スニペット記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"文字列記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"構造体記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"テキスト記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"パラメーター記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"単位記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t\t\"変数記号の前景色。これらの記号は、アウトライン、階層リンク、および候補のウィジェットに表示されます。\",\n\t],\n\t\"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode\": [\n\t\t\"Tab キーを切り替えるとフォーカスが移動します\",\n\t\t\"Tab キーを押すと、次のフォーカス可能な要素にフォーカスを移動します\",\n\t\t\"Tab キーを押すと、タブ文字が挿入されます\",\n\t],\n\t\"vs/editor/contrib/tokenization/browser/tokenization\": [\n\t\t\"開発者: トークン再作成の強制\",\n\t],\n\t\"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter\": [\n\t\t\"拡張機能のエディターで警告メッセージと共に表示されるアイコン。\",\n\t\t\"このドキュメントには、基本 ASCII 外の Unicode 文字が多数含まれています\",\n\t\t\"このドキュメントにはまぎらわしい Unicode 文字が多数含まれています\",\n\t\t\"このドキュメントには不可視の Unicode 文字が多数含まれています\",\n\t\t\"文字 {0} は、ソース コードでより一般的な ASCII 文字 {1} と混同される可能性があります。\",\n\t\t\"文字 {0}は、ソース コードでより一般的な文字{1}と混同される可能性があります。\",\n\t\t\"文字 {0}は非表示です。\",\n\t\t\"文字 {0} は基本 ASCII 文字ではありません。\",\n\t\t\"設定の調整\",\n\t\t\"コメントの強調表示を無効にする\",\n\t\t\"コメントの文字の強調表示を無効にする\",\n\t\t\"文字列の強調表示を無効にする\",\n\t\t\"文字列の文字の強調表示を無効にする\",\n\t\t\"まぎらわしい文字の強調表示を無効にする\",\n\t\t\"まぎらわしい文字の強調表示を無効にする\",\n\t\t\"不可視文字の強調表示を無効にする\",\n\t\t\"不可視の文字の強調表示を無効にする\",\n\t\t\"非 ASCII 文字の強調表示を無効にする\",\n\t\t\"基本 ASCII 以外の文字の強調表示を無効にする\",\n\t\t\"除外オプションの表示\",\n\t\t\"{0} (不可視の文字) を強調表示から除外する\",\n\t\t\"強調表示から {0} を除外します\",\n\t\t\"言語 \\\"{0}\\\" でより一般的な Unicode 文字を許可します。\",\n\t\t\"Unicode の強調表示オプションを構成する\",\n\t],\n\t\"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators\": [\n\t\t\"普通ではない行終端記号\",\n\t\t\"普通ではない行終端記号が検出されました\",\n\t\t\"このファイル \\'{0}\\' には、行区切り文字 (LS) や段落区切り記号 (PS) などの特殊な行の終端文字が 1 つ以上含まれています。\\r\\n\\r\\nそれらをファイルから削除することをお勧めします。これは \\'editor.unusualLineTerminators\\' を使用して構成できます。\",\n\t\t\"特殊な行の終端記号を削除する(&&R)\",\n\t\t\"無視する\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/highlightDecorations\": [\n\t\t\"変数の読み取りなど、読み取りアクセス中のシンボルの背景色。下にある装飾を隠さないために、色は不透過であってはなりません。\",\n\t\t\"変数への書き込みなど、書き込みアクセス中のシンボル背景色。下にある装飾を隠さないために、色は不透過であってはなりません。\",\n\t\t\"記号のテキスト出現の背景色。基になる装飾が非表示ならないように、この色を不透明にすることはできません。\",\n\t\t\"変数の読み取りなど読み取りアクセス中のシンボルの境界線の色。\",\n\t\t\"変数への書き込みなど書き込みアクセス中のシンボルの境界線の色。\",\n\t\t\"記号のテキスト出現箇所の境界線の色。\",\n\t\t\"シンボルによって強調表示される概要ルーラーのマーカーの色。マーカーの色は、基になる装飾を隠さないように不透明以外にします。\",\n\t\t\"書き込みアクセス シンボルを強調表示する概要ルーラーのマーカー色。下にある装飾を隠さないために、色は不透過であってはなりません。\",\n\t\t\"記号のテキスト出現の概要ルール マーカーの色。基になる装飾が非表示ならないように、この色を不透明にすることはできません。\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/wordHighlighter\": [\n\t\t\"次のシンボル ハイライトに移動\",\n\t\t\"前のシンボル ハイライトに移動\",\n\t\t\"シンボル ハイライトをトリガー\",\n\t],\n\t\"vs/editor/contrib/wordOperations/browser/wordOperations\": [\n\t\t\"単語の削除\",\n\t],\n\t\"vs/platform/action/common/actionCommonCategories\": [\n\t\t\"表示\",\n\t\t\"ヘルプ\",\n\t\t\"テスト\",\n\t\t\"ファイル\",\n\t\t\"基本設定\",\n\t\t\"開発者\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionList\": [\n\t\t\"{0} で適用する、{1} でプレビューする\",\n\t\t\"適用するには {0}\",\n\t\t\"{0}、無効になった理由: {1}\",\n\t\t\"アクション ウィジェット\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionWidget\": [\n\t\t\"アクション バーの切り替え済みアクション項目の背景色。\",\n\t\t\"アクション ウィジェットの一覧が表示されるかどうか\",\n\t\t\"アクション ウィジェットを非表示にする\",\n\t\t\"前のアクションを選択\",\n\t\t\"次のアクションを選択\",\n\t\t\"選択した操作を承諾\",\n\t\t\"選択したアクションのプレビュー\",\n\t],\n\t\"vs/platform/actions/browser/menuEntryActionViewItem\": [\n\t\t\"{0} ({1})\",\n\t\t\"{0} ({1})\",\n\t\t\"{0}\\r\\n[{1}] {2}\",\n\t],\n\t\"vs/platform/actions/browser/toolbar\": [\n\t\t\"非表示\",\n\t\t\"メニューのリセット\",\n\t],\n\t\"vs/platform/actions/common/menuService\": [\n\t\t\"\\'{0}\\' の非表示\",\n\t],\n\t\"vs/platform/audioCues/browser/audioCueService\": [\n\t\t\"行のエラー\",\n\t\t\"行の警告\",\n\t\t\"行の折りたたまれた面\",\n\t\t\"行のブレークポイント\",\n\t\t\"行のインライン候補\",\n\t\t\"ターミナル クイック修正\",\n\t\t\"ブレークポイントでデバッガーが停止しました\",\n\t\t\"行にインレイ ヒントがありません\",\n\t\t\"タスクが完了しました\",\n\t\t\"タスクが失敗しました\",\n\t\t\"ターミナル コマンドが失敗しました\",\n\t\t\"ターミナル ベル\",\n\t\t\"ノートブック セルが完了しました\",\n\t\t\"ノートブック セルが失敗しました\",\n\t\t\"差分行が挿入されました\",\n\t\t\"差分行が削除されました\",\n\t\t\"変更された差分行\",\n\t\t\"チャット要求が送信されました\",\n\t\t\"チャット応答を受信しました\",\n\t\t\"チャットの応答を保留中\",\n\t\t\"クリア\",\n\t\t\"保存\",\n\t\t\"形式\",\n\t],\n\t\"vs/platform/configuration/common/configurationRegistry\": [\n\t\t\"既定の言語構成のオーバーライド\",\n\t\t\"{0} 言語が優先される設定を構成します。\",\n\t\t\"言語に対して上書きされるエディター設定を構成します。\",\n\t\t\"この設定では、言語ごとの構成はサポートされていません。\",\n\t\t\"言語に対して上書きされるエディター設定を構成します。\",\n\t\t\"この設定では、言語ごとの構成はサポートされていません。\",\n\t\t\"空のプロパティは登録できません\",\n\t\t\"\\'{0}\\' を登録できません。これは、言語固有のエディター設定を記述するプロパティ パターン \\'\\\\\\\\[.*\\\\\\\\]$\\' に一致しています。\\'configurationDefaults\\' コントリビューションを使用してください。\",\n\t\t\"\\'{0}\\' を登録できません。このプロパティは既に登録されています。\",\n\t\t\"\\'{0}\\' を登録できません。関連付けられたポリシー {1} は既に {2} に登録されています。\",\n\t],\n\t\"vs/platform/contextkey/browser/contextKeyService\": [\n\t\t\"コンテキスト キーに関する情報を返すコマンド\",\n\t],\n\t\"vs/platform/contextkey/common/contextkey\": [\n\t\t\"空のコンテキスト キー式\",\n\t\t\"式を書き忘れましたか? \\'false\\' または \\'true\\' を指定すると、それぞれ常に false または true と評価できます。\",\n\t\t\"\\'not\\' の後に \\'in\\' があります。\",\n\t\t\"終わりかっこ \\')\\'\",\n\t\t\"予期しないトークン\",\n\t\t\"トークンの前に && または || を指定し忘れましたか?\",\n\t\t\"予期しない式の終わり\",\n\t\t\"コンテキスト キーを指定し忘れましたか?\",\n\t\t\"期待値: {0}\\r\\n受取済み: \\'{1}\\'。\",\n\t],\n\t\"vs/platform/contextkey/common/contextkeys\": [\n\t\t\"オペレーティング システムが macOS であるかどうか\",\n\t\t\"オペレーティング システムが Linux であるかどうか\",\n\t\t\"オペレーティング システムが Windows であるかどうか\",\n\t\t\"プラットフォームが Web ブラウザーであるかどうか\",\n\t\t\"オペレーティング システムが非ブラウザー プラットフォーム上の macOS であるかどうか\",\n\t\t\"オペレーティング システムが iOS であるかどうか\",\n\t\t\"プラットフォームがモバイル Web ブラウザーであるかどうか\",\n\t\t\"VS Code の品質の種類\",\n\t\t\"キーボードのフォーカスが入力ボックス内にあるかどうか\",\n\t],\n\t\"vs/platform/contextkey/common/scanner\": [\n\t\t\"{0} を意図していましたか?\",\n\t\t\"{0} または {1} を意図していましたか?\",\n\t\t\"{0}、{1}、または {2} を意図していましたか?\",\n\t\t\"見積もりを開いたり閉じたりし忘れましたか?\",\n\t\t\"\\'/\\' (スラッシュ) 文字をエスケープし忘れましたか? エスケープする前に \\'\\\\\\\\/\\' などの 2 つの円記号を指定してください。\",\n\t],\n\t\"vs/platform/history/browser/contextScopedHistoryWidget\": [\n\t\t\"候補を表示するかどうか\",\n\t],\n\t\"vs/platform/keybinding/common/abstractKeybindingService\": [\n\t\t\"({0}) が渡されました。2 番目のキーを待っています...\",\n\t\t\"({0}) が渡されました。次のキーを待っています...\",\n\t\t\"キーの組み合わせ ({0}、{1}) はコマンドではありません。\",\n\t\t\"キーの組み合わせ ({0}、{1}) はコマンドではありません。\",\n\t],\n\t\"vs/platform/list/browser/listService\": [\n\t\t\"ワークベンチ\",\n\t\t\"Windows および Linux 上の `Control` キーと macOS 上の `Command` キーに割り当てます。\",\n\t\t\"Windows および Linux 上の `Alt` キーと macOS 上の `Option` キーに割り当てます。\",\n\t\t\"マウスを使用して項目を複数選択するときに使用する修飾キーです (たとえば、エクスプローラーでエディターと scm ビューを開くなど)。\\'横に並べて開く\\' マウス ジェスチャー (がサポートされている場合) は、複数選択の修飾キーと競合しないように調整されます。\",\n\t\t\"マウスを使用して、ツリーとリスト内の項目を開く方法を制御します (サポートされている場合)。適用できない場合、一部のツリーやリストではこの設定が無視されることがあります。\",\n\t\t\"リストとツリーがワークベンチで水平スクロールをサポートするかどうかを制御します。警告: この設定をオンにすると、パフォーマンスに影響があります。\",\n\t\t\"スクロールバーのクリックでページごとにスクロールするかどうかを制御します。\",\n\t\t\"ツリーのインデントをピクセル単位で制御します。\",\n\t\t\"ツリーでインデントのガイドを表示するかどうかを制御します。\",\n\t\t\"リストとツリーでスムーズ スクロールを使用するかどうかを制御します。\",\n\t\t\"マウス ホイール スクロール イベントの `deltaX` と `deltaY` で使用される乗数。\",\n\t\t\"`Alt` を押すと、スクロール速度が倍増します。\",\n\t\t\"検索時に要素を強調表示します。さらに上下のナビゲーションでは、強調表示された要素のみがスキャンされます。\",\n\t\t\"検索時に要素をフィルター処理します。\",\n\t\t\"ワークベンチのリストとツリーの既定の検索モードを制御します。\",\n\t\t\"簡単なキーボード ナビゲーションは、キーボード入力に一致する要素に焦点を当てます。一致処理はプレフィックスでのみ実行されます。\",\n\t\t\"キーボード ナビゲーションの強調表示を使用すると、キーボード入力に一致する要素が強調表示されます。上および下への移動は、強調表示されている要素のみを移動します。\",\n\t\t\"キーボード ナビゲーションのフィルターでは、キーボード入力に一致しないすべての要素がフィルター処理され、非表示になります。\",\n\t\t\"ワークベンチのリストおよびツリーのキーボード ナビゲーション スタイルを制御します。単純、強調表示、フィルターを指定できます。\",\n\t\t\"代わりに \\'workbench.list.defaultFindMode\\' と \\'workbench.list.typeNavigationMode\\' を使用してください。\",\n\t\t\"検索時にあいまい一致を使用します。\",\n\t\t\"検索時に連続一致を使用します。\",\n\t\t\"ワークベンチでリストとツリーを検索するときに使用される一致の種類を制御します。\",\n\t\t\"フォルダー名をクリックしたときにツリー フォルダーが展開される方法を制御します。適用できない場合、一部のツリーやリストではこの設定が無視されることがあります。\",\n\t\t\"ツリーで固定スクロールを有効にするかどうかを制御します。\",\n\t\t\"\\'#workbench.tree.enableStickyScroll#\\' が有効な場合に、ツリーに表示される固定要素の数を制御します。\",\n\t\t\"ワークベンチのリストとツリーで型ナビゲーションがどのように機能するかを制御します。`trigger` に設定すると、`list.triggerTypeNavigation` コマンドの実行後に型ナビゲーションが開始されます。\",\n\t],\n\t\"vs/platform/markers/common/markers\": [\n\t\t\"エラー\",\n\t\t\"警告\",\n\t\t\"情報\",\n\t],\n\t\"vs/platform/quickinput/browser/commandsQuickAccess\": [\n\t\t\"最近使用したもの\",\n\t\t\"同様のコマンド\",\n\t\t\"よく使用するもの\",\n\t\t\"その他のコマンド\",\n\t\t\"同様のコマンド\",\n\t\t\"{0}, {1}\",\n\t\t\"コマンド \\'{0}\\' でエラーが発生しました\",\n\t],\n\t\"vs/platform/quickinput/browser/helpQuickAccess\": [\n\t\t\"{0}, {1}\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInput\": [\n\t\t\"戻る\",\n\t\t\"\\'Enter\\' を押して入力を確認するか \\'Escape\\' を押して取り消します\",\n\t\t\"{0}/{1}\",\n\t\t\"入力すると結果が絞り込まれます。\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputController\": [\n\t\t\"すべてのチェック ボックスを切り替える\",\n\t\t\"{0} 件の結果\",\n\t\t\"{0} 個選択済み\",\n\t\t\"OK\",\n\t\t\"カスタム\",\n\t\t\"戻る ({0})\",\n\t\t\"戻る\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputList\": [\n\t\t\"クイック入力\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputUtils\": [\n\t\t\"クリックして \\'{0}\\' コマンドを実行\",\n\t],\n\t\"vs/platform/theme/common/colorRegistry\": [\n\t\t\"全体の前景色。この色は、コンポーネントによってオーバーライドされていない場合にのみ使用されます。\",\n\t\t\"無効な要素の全体的な前景。この色は、コンポーネントによってオーバーライドされない場合にのみ使用されます。\",\n\t\t\"エラー メッセージ全体の前景色。この色は、コンポーネントによって上書きされていない場合にのみ使用されます。\",\n\t\t\"追加情報を提供する説明文の前景色、例:ラベル。\",\n\t\t\"ワークベンチのアイコンの既定の色。\",\n\t\t\"フォーカスされた要素の境界線全体の色。この色はコンポーネントによって上書きされていない場合にのみ使用されます。\",\n\t\t\"コントラストを強めるために、他の要素と隔てる追加の境界線。\",\n\t\t\"コントラストを強めるために、アクティブな他要素と隔てる追加の境界線。\",\n\t\t\"ワークベンチ内のテキスト選択の背景色 (例: 入力フィールドやテキストエリア)。エディター内の選択には適用されないことに注意してください。\",\n\t\t\"テキストの区切り文字の色。\",\n\t\t\"テキスト内のリンクの前景色。\",\n\t\t\"クリックされたときとマウスをホバーしたときのテキスト内のリンクの前景色。\",\n\t\t\"フォーマット済みテキスト セグメントの前景色。\",\n\t\t\"書式設定されたテキスト セグメントの背景色。\",\n\t\t\"テキスト内のブロック引用の背景色。\",\n\t\t\"テキスト内のブロック引用の境界線色。\",\n\t\t\"テキスト内のコード ブロックの背景色。\",\n\t\t\"エディター内の検索/置換窓など、エディター ウィジェットの影の色。\",\n\t\t\"エディター内の検索/置換窓など、エディター ウィジェットの境界線の色。\",\n\t\t\"入力ボックスの背景。\",\n\t\t\"入力ボックスの前景。\",\n\t\t\"入力ボックスの境界線。\",\n\t\t\"入力フィールドのアクティブ オプションの境界線の色。\",\n\t\t\"入力フィールドでアクティブ化されたオプションの背景色。\",\n\t\t\"入力フィールドのオプションの背景のホバー色。\",\n\t\t\"入力フィールドでアクティブ化されたオプションの前景色。\",\n\t\t\"入力ボックスのプレースホルダー テキストの前景色。\",\n\t\t\"情報の重大度を示す入力検証の背景色。\",\n\t\t\"情報の重大度を示す入力検証の前景色。\",\n\t\t\"情報の重大度を示す入力検証の境界線色。\",\n\t\t\"警告の重大度を示す入力検証の背景色。\",\n\t\t\"警告の重大度を示す入力検証の前景色。\",\n\t\t\"警告の重大度を示す入力検証の境界線色。\",\n\t\t\"エラーの重大度を示す入力検証の背景色。\",\n\t\t\"エラーの重大度を示す入力検証の前景色。\",\n\t\t\"エラーの重大度を示す入力検証の境界線色。\",\n\t\t\"ドロップダウンの背景。\",\n\t\t\"ドロップダウン リストの背景色。\",\n\t\t\"ドロップダウンの前景。\",\n\t\t\"ドロップダウンの境界線。\",\n\t\t\"ボタンの前景色。\",\n\t\t\"ボタンの区切り記号の色。\",\n\t\t\"ボタンの背景色。\",\n\t\t\"ホバー時のボタン背景色。\",\n\t\t\"ボタンの境界線の色。\",\n\t\t\"ボタンの 2 次的な前景色。\",\n\t\t\"ボタンの 2 次的な背景色。\",\n\t\t\"ホバー時のボタンの 2 次的な背景色。\",\n\t\t\"バッジの背景色。バッジとは小さな情報ラベルのことです。例:検索結果の数\",\n\t\t\"バッジの前景色。バッジとは小さな情報ラベルのことです。例:検索結果の数\",\n\t\t\"ビューがスクロールされたことを示すスクロール バーの影。\",\n\t\t\"スクロール バーのスライダーの背景色。\",\n\t\t\"ホバー時のスクロール バー スライダー背景色。\",\n\t\t\"クリック時のスクロール バー スライダー背景色。\",\n\t\t\"時間のかかる操作で表示するプログレス バーの背景色。\",\n\t\t\"エディター内のエラー テキストの背景色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"エディターでエラーを示す波線の前景色。\",\n\t\t\"設定されている場合、エディター内のエラーの二重下線の色。\",\n\t\t\"エディター内の警告テキストの背景色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"エディターで警告を示す波線の前景色。\",\n\t\t\"設定されている場合、エディター内の警告の二重下線の色。\",\n\t\t\"エディター内の情報テキストの背景色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"エディターで情報を示す波線の前景色。\",\n\t\t\"設定されている場合、エディター内の情報の二重下線の色。\",\n\t\t\"エディターでヒントを示す波線の前景色。\",\n\t\t\"設定されている場合、エディター内のヒントの二重下線の色。\",\n\t\t\"アクティブな枠の境界線の色。\",\n\t\t\"エディターの背景色。\",\n\t\t\"エディターの既定の前景色。\",\n\t\t\"エディターの固定スクロールの背景色\",\n\t\t\"エディターの固定スクロールのホバー背景色\",\n\t\t\"検索/置換窓など、エディター ウィジェットの背景色。\",\n\t\t\"検索/置換などを行うエディター ウィジェットの前景色。\",\n\t\t\"エディター ウィジェットの境界線色。ウィジェットに境界線があり、ウィジェットによって配色を上書きされていない場合でのみこの配色は使用されます。\",\n\t\t\"エディター ウィジェットのサイズ変更バーの境界線色。ウィジェットにサイズ変更の境界線があり、ウィジェットによって配色を上書きされていない場合でのみこの配色は使用されます。\",\n\t\t\"クイック ピッカーの背景色。クイック ピッカー ウィジェットは、コマンド パレットのようなピッカーのコンテナーです。\",\n\t\t\"クイック ピッカーの前景色。クイック ピッカー ウィジェットは、コマンド パレットのようなピッカーのコンテナーです。\",\n\t\t\"クイック ピッカー のタイトルの背景色。クイック ピッカー ウィジェットは、コマンド パレットのようなピッカーのコンテナーです。\",\n\t\t\"ラベルをグループ化するためのクリック選択の色。\",\n\t\t\"境界線をグループ化するためのクイック選択の色。\",\n\t\t\"キー バインド ラベルの背景色です。キー バインド ラベルはキーボード ショートカットを表すために使用されます。\",\n\t\t\"キー バインド ラベルの前景色です。キー バインド ラベルはキーボード ショートカットを表すために使用されます。\",\n\t\t\"キー バインド ラベルの境界線の色です。キー バインド ラベルはキーボード ショートカットを表すために使用されます。\",\n\t\t\"キー バインド ラベルの下の境界線の色です。キー バインド ラベルはキーボード ショートカットを表すために使用されます。\",\n\t\t\"エディターの選択範囲の色。\",\n\t\t\"ハイ コントラストの選択済みテキストの色。\",\n\t\t\"非アクティブなエディターの選択範囲の色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"選択範囲の同じコンテンツの領域の色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"選択範囲と同じコンテンツの境界線の色。\",\n\t\t\"現在の検索一致項目の色。\",\n\t\t\"その他の検索条件に一致する項目の色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"検索を制限する範囲の色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"現在の検索一致項目の境界線の色。\",\n\t\t\"他の検索一致項目の境界線の色。\",\n\t\t\"検索を制限する範囲の境界線色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"検索エディターのクエリの色が一致します。\",\n\t\t\"検索エディター クエリの境界線の色が一致します。\",\n\t\t\"検索ビューレットの完了メッセージ内のテキストの色。\",\n\t\t\"ホバーが表示されている語の下を強調表示します。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"エディター ホバーの背景色。\",\n\t\t\"エディター ホバーの前景色。\",\n\t\t\"エディター ホバーの境界線の色。\",\n\t\t\"エディターのホバーのステータス バーの背景色。\",\n\t\t\"アクティブなリンクの色。\",\n\t\t\"インライン ヒントの前景色\",\n\t\t\"インライン ヒントの背景色\",\n\t\t\"種類のインライン ヒントの前景色\",\n\t\t\"種類のインライン ヒントの背景色\",\n\t\t\"パラメーターのインライン ヒントの前景色\",\n\t\t\"パラメーターのインライン ヒントの背景色\",\n\t\t\"電球アクション アイコンに使用する色。\",\n\t\t\"自動修正の電球アクション アイコンとして使用される色。\",\n\t\t\"電球 AI アイコンに使用する色。\",\n\t\t\"挿入されたテキストの背景色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"削除したテキストの背景色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"挿入された行の背景色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"削除した行の背景色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"挿入された行の余白の背景色。\",\n\t\t\"削除された行の余白の背景色。\",\n\t\t\"挿入されたコンテンツについて、差分概要ルーラーを前面に置きます。\",\n\t\t\"削除されたコンテンツについて、差分概要ルーラーを前面に置きます。\",\n\t\t\"挿入されたテキストの輪郭の色。\",\n\t\t\"削除されたテキストの輪郭の色。\",\n\t\t\"2 つのテキスト エディターの間の境界線の色。\",\n\t\t\"差分エディターの対角線の塗りつぶし色。対角線の塗りつぶしは、横に並べて比較するビューで使用されます。\",\n\t\t\"差分エディター内の変更されていないブロックの背景色。\",\n\t\t\"差分エディター内の変更されていないブロックの前景色。\",\n\t\t\"差分エディター内の変更されていないコードの背景色。\",\n\t\t\"ツリーリストがアクティブのとき、フォーカスされた項目のツリーリスト背景色。アクティブなツリーリストはキーボード フォーカスがあり、非アクティブではこれがありません。\",\n\t\t\"ツリーリストがアクティブのとき、フォーカスされた項目のツリーリスト前景色。アクティブなツリーリストはキーボード フォーカスがあり、非アクティブではこれがありません。\",\n\t\t\"リストやツリーがアクティブな場合の、フォーカスされた項目のリストやツリーのアウトライン色。アクティブなリストやツリーにはキーボード フォーカスがあり、非アクティブにはこれがありません。\",\n\t\t\"リスト/ツリーがアクティブで選択されている場合の、フォーカスされたアイテムのリスト/ツリー アウトラインの色。アクティブなリスト/ツリーにはキーボード フォーカスがあり、非アクティブな場合はありません。\",\n\t\t\"ツリーリストが非アクティブのとき、選択された項目のツリーリスト背景色。アクティブなツリーリストはキーボード フォーカスがあり、非アクティブではこれがありません。\",\n\t\t\"ツリーリストがアクティブのとき、選択された項目のツリーリスト前景色。アクティブなツリーリストはキーボード フォーカスがあり、非アクティブではこれがありません。\",\n\t\t\"ツリーリストがアクティブのとき、選択された項目のツリーリストのアイコン前景色。アクティブなツリーリストはキーボード フォーカスがあり、非アクティブではこれがありません。\",\n\t\t\"ツリーリストが非アクティブのとき、選択された項目のツリーリスト背景色。アクティブなツリーリストはキーボード フォーカスがあり、非アクティブではこれがありません。\",\n\t\t\"ツリーリストが非アクティブのとき、選択された項目のツリーリスト前景色。アクティブなツリーリストはキーボード フォーカスがあり、非アクティブではこれがありません。\",\n\t\t\"ツリーリストが非アクティブのとき、選択された項目のツリーリストのアイコン前景色。アクティブなツリーリストはキーボード フォーカスがあり、非アクティブではこれがありません。\",\n\t\t\"ツリーリストが非アクティブのとき、フォーカスされた項目のツリーリスト背景色。アクティブなツリーリストはキーボード フォーカスがあり、非アクティブではこれがありません。\",\n\t\t\"リストやツリーが非アクティブな場合の、フォーカスされた項目のリストやツリーのアウトライン色。アクティブなリストやツリーにはキーボード フォーカスがあり、非アクティブにはこれがありません。\",\n\t\t\"マウス操作で項目をホバーするときのツリーリスト背景。\",\n\t\t\"マウス操作で項目をホバーするときのツリーリスト前景。\",\n\t\t\"マウス操作で項目を移動するときのツリーリスト ドラッグ アンド ドロップの背景。\",\n\t\t\"ツリーリスト内を検索しているとき、一致した強調のツリーリスト前景色。\",\n\t\t\"ツリー/リスト内を検索しているとき、一致した強調のツリー/リストの前景色。\",\n\t\t\"無効な項目のツリーリストの前景色。たとえばエクスプローラーの未解決なルート。\",\n\t\t\"エラーを含むリスト項目の前景色。\",\n\t\t\"警告が含まれるリスト項目の前景色。\",\n\t\t\"リストおよびツリーの型フィルター ウェジェットの背景色。\",\n\t\t\"リストおよびツリーの型フィルター ウィジェットのアウトライン色。\",\n\t\t\"一致項目がない場合の、リストおよびツリーの型フィルター ウィジェットのアウトライン色。\",\n\t\t\"リストおよびツリーの型フィルター ウィジェットの影の色。\",\n\t\t\"フィルタリングされた一致の背景色。\",\n\t\t\"フィルタリングされた一致の境界線の色。\",\n\t\t\"インデント ガイドのツリー ストロークの色。\",\n\t\t\"アクティブでないインデント ガイドのツリー ストロークの色。\",\n\t\t\"列間の表の境界線の色。\",\n\t\t\"奇数テーブル行の背景色。\",\n\t\t\"強調表示されていない項目のリスト/ツリー前景色。 \",\n\t\t\"チェックボックス ウィジェットの背景色。\",\n\t\t\"要素が選択されている場合のチェックボックス ウィジェットの背景色。\",\n\t\t\"チェックボックス ウィジェットの前景色。\",\n\t\t\"チェックボックス ウィジェットの境界線の色。\",\n\t\t\"要素が選択されている場合のチェックボックス ウィジェットの境界線の色。\",\n\t\t\"代わりに quickInputList.focusBackground を使用してください\",\n\t\t\"フォーカスされた項目のクイック選択の前景色。\",\n\t\t\"フォーカスされた項目のクイック選択のアイコン前景色。\",\n\t\t\"フォーカスされた項目のクイック選択の背景色。\",\n\t\t\"メニューの境界線色。\",\n\t\t\"メニュー項目の前景色。\",\n\t\t\"メニュー項目の背景色。\",\n\t\t\"メニューで選択されたメニュー項目の前景色。\",\n\t\t\"メニューで選択されたメニュー項目の背景色。\",\n\t\t\"メニューで選択されたメニュー項目の境界線色。\",\n\t\t\"メニュー内のメニュー項目の境界線色。\",\n\t\t\"アクションの上にマウス ポインターを合わせたときのツール バーのアウトライン\",\n\t\t\"アクションの上にマウス ポインターを合わせたときのツール バーのアウトライン\",\n\t\t\"アクションの上にマウス ポインターを合わせるとツール バーの背景が表示される\",\n\t\t\"スニペット tabstop の背景色を強調表示します。\",\n\t\t\"スニペット tabstop の境界線の色を強調表示します。\",\n\t\t\"スニペットの最後の tabstop の背景色を強調表示します。\",\n\t\t\"スニペットの最後のタブストップで境界線の色を強調表示します。\",\n\t\t\"フォーカスされた階層リンクの項目の色。\",\n\t\t\"階層リンクの項目の背景色。\",\n\t\t\"フォーカスされた階層リンクの項目の色。\",\n\t\t\"選択された階層リンクの項目の色。\",\n\t\t\"階層項目ピッカーの背景色。\",\n\t\t\"インライン マージ競合の現在のヘッダーの背景。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"インライン マージ競合の現在のコンテンツ背景。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"インライン マージ競合の着信ヘッダーの背景。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"インライン マージ競合の着信コンテンツの背景。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"インライン マージ競合の共通の先祖のヘッダー背景。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"インライン マージ競合の共通の先祖のコンテンツ背景。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"行内マージ競合のヘッダーとスプリッターの境界線の色。\",\n\t\t\"行内マージ競合の現在の概要ルーラー前景色。\",\n\t\t\"行内マージ競合の入力側の概要ルーラー前景色。\",\n\t\t\"行内マージ競合の共通の祖先概要ルーラー前景色。\",\n\t\t\"検出された一致項目の概要ルーラー マーカーの色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"選択範囲を強調表示するための概要ルーラー マーカーの色。この色は、基本装飾が非表示にならないよう不透明にすることはできません。\",\n\t\t\"一致を検索するためのミニマップ マーカーの色。\",\n\t\t\"エディターを繰り返し選択する範囲のミニマップ マーカーの色。\",\n\t\t\"エディターの選択範囲のミニマップ マーカーの色。\",\n\t\t\"情報のミニマップ マーカーの色。\",\n\t\t\"警告のミニマップ マーカーの色。\",\n\t\t\"エラーのミニマップ マーカーの色。\",\n\t\t\"ミニマップの背景色。\",\n\t\t\"ミニマップにレンダリングされる前景要素の不透明度。たとえば、\\\"#000000c0\\\" では、75% の不透明度で要素をレンダリングします。\",\n\t\t\"ミニマップ スライダーの背景色。\",\n\t\t\"ホバーリング時のミニマップ スライダーの背景色。\",\n\t\t\"クリックしたときのミニマップ スライダーの背景色。\",\n\t\t\"問題のエラー アイコンに使用される色。\",\n\t\t\"問題の警告アイコンに使用される色。\",\n\t\t\"問題情報アイコンに使用される色。\",\n\t\t\"グラフで使用される前景色。\",\n\t\t\"グラフの水平線に使用される色。\",\n\t\t\"グラフの視覚化に使用される赤色。\",\n\t\t\"グラフの視覚化に使用される青色。\",\n\t\t\"グラフの視覚化に使用される黄色。\",\n\t\t\"グラフの視覚化に使用されるオレンジ色。\",\n\t\t\"グラフの視覚化に使用される緑色。\",\n\t\t\"グラフの視覚化に使用される紫色。\",\n\t],\n\t\"vs/platform/theme/common/iconRegistry\": [\n\t\t\"使用するフォントの ID。設定されていない場合は、最初に定義されているフォントが使用されます。\",\n\t\t\"アイコン定義に関連付けられたフォント文字。\",\n\t\t\"ウィジェットにある閉じるアクションのアイコン。\",\n\t\t\"前のエディターの場所に移動するためのアイコン。\",\n\t\t\"次のエディターの場所に移動するためのアイコン。\",\n\t],\n\t\"vs/platform/undoRedo/common/undoRedoService\": [\n\t\t\"次のファイルが閉じられ、ディスク上で変更されました: {0}。\",\n\t\t\"以下のファイルは互換性のない方法で変更されました: {0}。\",\n\t\t\"すべてのファイルで \\'{0}\\' を元に戻せませんでした。{1}\",\n\t\t\"すべてのファイルで \\'{0}\\' を元に戻せませんでした。{1}\",\n\t\t\"{1} に変更が加えられたため、すべてのファイルで \\'{0}\\' を元に戻せませんでした\",\n\t\t\"{1} で元に戻すまたはやり直し操作が既に実行されているため、すべてのファイルに対して \\'{0}\\' を元に戻すことはできませんでした\",\n\t\t\"元に戻すまたはやり直し操作がその期間に実行中であったため、すべてのファイルに対して \\'{0}\\' を元に戻すことはできませんでした\",\n\t\t\"すべてのファイルで \\'{0}\\' を元に戻しますか?\",\n\t\t\"{0} 個のファイルで元に戻す(&&U)\",\n\t\t\"このファイルを元に戻す\",\n\t\t\"元に戻すまたはやり直し操作が既に実行されているため、\\'{0}\\' を元に戻すことはできませんでした。\",\n\t\t\"\\'{0}\\' を元に戻しますか?\",\n\t\t\"はい(&&Y)\",\n\t\t\"いいえ\",\n\t\t\"すべてのファイルで \\'{0}\\' をやり直しできませんでした。{1}\",\n\t\t\"すべてのファイルで \\'{0}\\' をやり直しできませんでした。{1}\",\n\t\t\"{1} に変更が加えられたため、すべてのファイルで \\'{0}\\' を再実行できませんでした\",\n\t\t\"{1} で元に戻すまたはやり直し操作が既に実行されているため、すべてのファイルに対して \\'{0}\\' をやり直すことはできませんでした\",\n\t\t\"元に戻すまたはやり直し操作がその期間に実行中であったため、すべてのファイルに対して \\'{0}\\' をやり直すことはできませんでした\",\n\t\t\"元に戻すまたはやり直し操作が既に実行されているため、\\'{0}\\' をやり直すことはできませんでした。\",\n\t],\n\t\"vs/platform/workspace/common/workspace\": [\n\t\t\"コード ワークスペース\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA<PERSON>,OAAO,+BAAgC,CACtC,+CAAgD,CAC/C,WACD,EACA,yCAA0C,CACzC,cACD,EACA,gDAAiD,CAChD,2EACA,yDACA,wDACD,EACA,4CAA6C,CAC5C,eACA,0BACD,EACA,uCAAwC,CACvC,+KACA,yZACD,EACA,8CAA+C,CAC9C,mCACD,EACA,uCAAwC,CACvC,0BACA,oBACA,oBACA,4CACA,4BACA,kDACD,EACA,qDAAsD,CACrD,sCACD,EACA,+CAAgD,CAC/C,4CACD,EACA,qCAAsC,CACrC,yCACD,EACA,uCAAwC,CACvC,iCACA,uCACA,yDACA,uCACA,uCACA,qBACA,oEACD,EACA,yBAA0B,CACzB,UACD,EACA,8BAA+B,CAC9B,WACA,8FACA,iLACA,iLACA,4CACA,gLACD,EACA,kCAAmC,CAClC,OACA,QACA,MACA,UACA,OACA,QACA,MACA,QACA,UACA,QACA,iCACA,2BACA,UACA,QACA,MACA,UACA,UACA,QACA,MACA,OACD,EACA,0BAA2B,CAC1B,GACD,EACA,+CAAgD,CAC/C,iCACA,6IACA,gMACA,ukBACA,kbACD,EACA,iCAAkC,CACjC,yGACA,yGACA,yGACD,EACA,qCAAsC,CACrC,gCACA,2BACA,gCACA,2BACA,sCACA,sCACD,EACA,4CAA6C,CAC5C,qYACA,uFACD,EACA,2DAA4D,CAC3D,yKACA,yKACA,+KACA,qBACA,qKACA,2EACA,2DACA,6DACA,+GACA,eACA,iEACA,sEACA,iDACA,8BACD,EACA,6CAA8C,CAC7C,mJACA,uLACA,qKACD,EACA,kDAAmD,CAClD,+GACA,8GACD,EACA,8DAA+D,CAC9D,iIACA,sHACA,wMACA,4KACA,8FACA,6CACA,mDACA,6CACA,6IACA,uIACA,+GACA,6CACA,iIACA,4CACD,EACA,4DAA6D,CAC5D,qEACA,4CACD,EACA,wDAAyD,CACxD,sJACD,EACA,kEAAmE,CAClE,mGACA,uIACA,iFACA,uIACA,gCACA,gFACD,EACA,kEAAmE,CAClE,+DACA,+DACA,+DACA,+DACA,qEACA,qEACA,wDACD,EACA,uDAAwD,CACvD,wHACA,oIACA,oFACA,6EACD,EACA,wDAAyD,CACxD,2FACD,EACA,oDAAqD,CACpD,iCACA,sTACA,0dACA,sUACA,0RACA,qHACA,2NACA,mGACA,2KACA,+MACA,iLACA,yVACA,qNACA,qNACA,+PACA,oLACA,yTACA,2NACA,qLACA,qNACA,obACA,iIACA,yGACA,yGACA,uUACA,yGACA,yGACA,qQACA,8NACA,iOACA,wOACA,gQACA,+PACA,iOACA,mPACA,iJACA,+DACA,+GACA,mHACA,+GACA,+GACA,+MACA,uIACA,mMACA,+PACA,qNACA,0TACD,EACA,wCAAyC,CACxC,mPACA,kIACA,8IACA,4OACA,+JACA,2QACA,mPACA,iOACA,+JACA,+PACA,qKACA,+PACA,6IACA,oIACA,sPACA,kKACA,uRACA,4dACA,6SACA,oZACA,qYACA,8YACA,2VACA,iWACA,gbACA,gIACA,oNACA,+QACA,2FACA,kIACA,8OACA,4TACA,+NACA,qOACA,+NACA,+NACA,qOACA,yMACA,+MACA,yMACA,yMACA,yMACA,qHACA,2IACA,iOACA,qRACA,8LACA,weACA,ugBACA,oeACA,0JACA,8EACA,2RACA,uSACA,2RACA,iOACA,+GACA,kqBACA,kOACA,kIACA,0GACA,oNACA,0NACA,0GACA,sdACA,2QACA,8LACA;AAAA;AAAA;AAAA,mHACA,iIACA,+JACA,kQACA,4SACA,0RACA,mGACA,qHACA,sKACA,2KACA,0JACA,+JACA,iLACA,iLACA,8OACA,2PACA,uIACA,8IACA,mGACA,qHACA,2HACA,+JACA,s6BACA,2EACA,+GACA,+JACA,iGACA,iFACA,uLACA,yGACA,mYACA,+JACA,qHACA,iIACA,qHACA,+JACA,qHACA,iIACA,qHACA,2EACA,iFACA,qQACA,oPACA,6YACA,mPACA,mXACA,gMACA,0LACA,2HACA,0LACA,yMACA,4NACA,oPACA,sKACA,kLACA,qcACA,wVACA,0MACA,2GACA,oOACA,iHACA,4KACA,sNACA,gNACA,6HACA,0MACA,iOACA,wLACA,8IACA,kRACA,4KACA,6OACA,qKACA,+JACA,2ZACA,mPACA,qNACA,kUACA,sKACA,gKACA,qMACA,6KACA,+pBACA,0MACA,2KACA,8LACA,uLACA,6RACA,uOACA,kRACA,wJACA,4IACA,gLACA,kJACA,opBACA,8JACA,4IACA,kJACA,kJACA,gLACA,8JACA,8JACA,wJACA,kJACA,wJACA,sIACA,4IACA,kJACA,0IACA,8JACA,yJACA,sIACA,wJACA,4IACA,2IACA,8JACA,6IACA,8JACA,8JACA,wJACA,2HACA,8LACA,6JACA,uIACA,+HACA,+HACA,+GACA,uZACA,mbACA,uQACA,wTACA,2NACA,2cACA,0PACA,sTACA,idACA,iNACA,0XACA,opBACA,qEACA,8OACA,6LACA,yMACA,qQACA,6LACA,+MACA,qQACA,yPACA,yPACA,uOACA,+MACA,6LACA,yMACA,2QACA,6IACA,mJACA,mPACA,uWACA,ucACA,6XACA,6LACA,+GACA,+GACA,6RACA,6UACA,iJACA,6GACA,4QACA,yRACA,mOACA,6KACA,6KACA,+LACA,wOACA,uLACA,mMACA,iIACA,uKACA,+UACA,qJACA,yMACA,mGACA,wYACA,qNACA,wFACA,oIACA,wLACA,uQACA,0HACA,yJACA,qHACA,uOACA,2HACA,mMACA,uUACA,6IACA,qHACA,iOACA,+MACA,idACA,2QACA,8FACA,imBACA,2NACA,mVACA,iLACA,6GACA,mYACA,iOACA,mGACA,4LACA,+OACA,yJACA,uKACA,kKACA,kiBACA,0IACA,mGACA,iOACA,qNACA,uFACA,mJACA,iNACA,yMACA,uLACA,2HACA,uIACA,mRACA,8PACA,yJACA,uIACA,wJACA,+JACA,2KACA,mGACA,uLACA,2TACA,mJACA,uIACA,mGACA,+JACA,uIACA,6LACA,mMACA,+eACA,8KACA,2NACA,2HACA,6LACA,iOACA,yMACA,+JACA,qHACA,uIACA,uIACA,6IACA,mGACA,2QACA,mMACA,iWACA,mNACA,kPACA,6OACA,uFACA,4SACA,oRACA,mMACA,yKACA,2EACA,wRACA,2EACA,mJACA,2HACA,2KACA,+MACA,6IACA,6FACA,gWACA,kNACA,6RACA,+DACA,+GACA,yFACA,4JACA,6FACA,sNACA,qSACA,8PACD,EACA,4CAA6C,CAC5C,qHACA,6IACA,mXACA,+GACA,0aACA,iIACA,iFACA,6OACA,6FACA,2EACA,qGACA,iMACA,8IACA,6MACA,yGACA,yGACA,yGACA,yGACA,yGACA,yGACA,kJACA,kJACA,kJACA,kJACA,kJACA,kJACA,mGACA,uMACA,mGACA,qLACA,4EACA,wEACA,qEACA,6FACA,qEACA,yGACA,sNACA,oKACA,6pBACA,iIACA,+GACA,sHACA,gWACA,qHACA,+GACA,+GACA,oNACA,oNACA,oNACA,oNACA,oNACA,oNACA,6FACA,6RACA,6RACA,6RACA,6RACA,6RACA,6RACA,uRACA,uRACA,uRACA,uRACA,uRACA,uRACA,qJACA,wIACD,EACA,qCAAsC,CACrC,mNACA,sTACA,8PACA,mGACA,iIACA,yJACA,mJACA,uLACA,yGACA,8LACA,uLACA,kOACA,0GACA,uIACA,2HACA,qKACA,iIACA,mJACA,uIACA,qHACA,+IACA,mLACA,oLACA,qEACA,yJACA,uKACA,2JACA,iIACA,iIACA,iIACA,uIACA,wIACA,iLACA,mLACA,iIACA,6IACA,uKACA,uKACA,iLACA,6LACA,mMACA,8MACD,EACA,6BAA8B,CAC7B,eACA,2BACA,qBACA,eACA,mDACA,qBACA,6CACA,2BACA,iCACA,2BACA,eACA,mDACA,eACA,2BACA,iCACA,2BACA,OACA,eACA,uCACA,qBACA,iCACA,iCACA,qBACA,qBACA,6CACA,eACA,WACD,EACA,2CAA4C,CAC3C,kDACD,EACA,mCAAoC,CACnC,4CACD,EACA,qCAAsC,CACrC,iEACA,iDACA,mIACA,oDACA,+DACA,gDACA,oFACA,qEACA,mLACA,qGACA,uHACD,EACA,+CAAgD,CAC/C,mDACA,kBACD,EACA,sDAAuD,CACtD,uCACA,0FACA,yDACA,yDACA,iFACA,oEACD,EACA,4DAA6D,CAC5D,yJACA,mDACA,mDACA,uCACA,wDACA,kJACD,EACA,4DAA6D,CAC5D,iFACA,gFACD,EACA,sDAAuD,CACtD,4CACD,EACA,gDAAiD,CAChD,gCACA,2BACA,2BACA,2BACA,0BACA,qBACA,qBACA,qBACA,+DACA,+DACA,eACA,eACA,eACA,gCACA,2BACA,2BACA,2BACA,0EACD,EACA,kDAAmD,CAClD,+JACD,EACA,0DAA2D,CAC1D,oGACA,qKACA,oJACA,uTACA,oJACA,gKACA,6DACA,sHACA,gKACA,mJACA,kIACA,sHACA,0CACA,+JACA,mJACA,iIACA,qHACA,uDACA,gKACA,oJACA,kIACA,sHACA,mDACA,mJACA,iCACA,iIACA,8BACA,4FACD,EACA,+DAAgE,CAC/D,2OACA,oSACD,EACA,4DAA6D,CAC5D,+EACA,yDACA,gCACD,EACA,sDAAuD,CACtD,0CACA,uCACA,eACA,iCACA,6CACA,eACA,6CACA,sDACD,EACA,uDAAwD,CACvD,uLACA,4EACA,sEACA,8FACA,wFACA,iEACD,EACA,wDAAyD,CACxD,iHACA,4CACD,EACA,0DAA2D,CAC1D,mIACA,6FACD,EACA,qEAAsE,CACrE,2JACA,+JACA,wFACA,sHACD,EACA,4CAA6C,CAC5C,+DACA,oEACA,mDACA,mDACA,kFACA,sFACD,EACA,oDAAqD,CACpD,iCACA,mDACA,mDACA,eACA,iCACA,+DACA,iCACA,8CACA,eACA,qHACD,EACA,kDAAmD,CAClD,yDACA,wDACD,EACA,kEAAmE,CAClE,kEACA,iQACD,EACA,gEAAiE,CAChE,uIACA,8EACA,mMACA,yDACA,2GACD,EACA,6DAA8D,CAC7D,iCACA,qEACA,yBACA,yBACA,iCACA,iCACA,6CACA,4CACD,EACA,uEAAwE,CACvE,uOACD,EACA,qEAAsE,CACrE,wIACA,+EACA,mMACD,EACA,+DAAgE,CAC/D,qNACD,EACA,gDAAiD,CAChD,yMACA,eACA,oBACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA,yDACA,6CACA,2BACA,2BACA,uCACA,6IACA,oIACA,mFACA,mFACA,yDACA,yDACA,eACA,mBACD,EACA,4CAA6C,CAC5C,qLACA,+MACA,mMACA,uJACA,yKACA,mKACA,mKACA,4BACA,eACA,eACA,uCACA,uCACA,6CACA,qBACA,eACA,eACA,eACA,iCACA,6CACA,oQACA,mBACA,yDACA,uDACA,oEACA,+EACA,oEACA,0bACD,EACA,4CAA6C,CAC5C,eACA,mDACA,iCACA,+DACA,yDACA,gHACA,2EACA,yDACA,yGACA,uFACA,mDACA,iCACA,qEACA,yGACA,yGACA,+GACA,uFACA,6DACD,EACA,uDAAwD,CACvD,yPACA,yJACA,+JACA,2KACA,2KACA,gLACD,EACA,8CAA+C,CAC9C,iFACA,iFACA,oHACD,EACA,iDAAkD,CACjD,iFACA,oEACD,EACA,gDAAiD,CAChD,uGACA,qHACA,uGACA,qHACA,2IACA,4CACA,2IACA,2CACD,EACA,sDAAuD,CACtD,qBACA,eACA,eACA,qBACA,kBACA,gDACA,yBACA,yLACA,kNACA,mLACA,2MACA,mLACA,2MACA,sKACD,EACA,oDAAqD,CACpD,qBACA,eACA,2EACA,+DACA,iCACA,sCACA,6CACA,mDACA,eACA,2EACA,+DACA,iCACA,sCACA,2EACA,+DACA,mDACA,qBACA,iFACA,qEACA,uCACA,4CACA,uCACA,eACA,2EACA,+DACA,iCACA,kDACA,uCACA,2EACA,+DACA,iCACA,sCACA,eACA,mDACA,eACA,+DACA,eACA,qHACA,cACD,EACA,qEAAsE,CACrE,4GACD,EACA,iEAAkE,CACjE,uPACA,sDACA,WACD,EACA,2DAA4D,CAC3D,+BACA,+BACA,0BACD,EACA,6DAA8D,CAC7D,iFACA,yDACA,0BACD,EACA,uDAAwD,CACvD,yCACA,oDACA,4FACA,8FACA,2EACA,oGACA,sGACA,yIACD,EACA,wDAAyD,CACxD,2KACA,+DACA,kCACD,EACA,wCAAyC,CACxC,oFACA,iIACA,+MACA,qKACA,mGACA,kEACA,kEACA,kEACA,kEACA,4DACA,4DACA,gDACA,+CACD,EACA,2DAA4D,CAC3D,sDACA,0UACA,oTACD,EACA,yDAA0D,CACzD,iCACA,2HACA,4GACA,2HACA,4DACD,EACA,0DAA2D,CAC1D,uCACA,sCACD,EACA,oDAAqD,CACpD,iFACA,qEACA,qEACA,oDACA,oDACA,yGACA,+DACA,2EACA,qEACA,2EACA,mDACA,8DACD,EACA,uDAAwD,CACvD,iFACA,+EACA,iFACA,kFACA,+EACA,gJACA,uCACA,4CACD,EACA,uDAAwD,CACvD,uFACA,uFACA,uFACA,yGACA,mDACA,mGACA,uCACA,2EACA,2BACA,uFACA,2EACD,EACA,+DAAgE,CAC/D,2BACD,EACA,0EAA2E,CAC1E,mGACA,2HACA,iOACA,gIACD,EACA,0EAA2E,CAC1E,sIACD,EACA,2EAA4E,CAC3E,oJACA,oJACA,YACA,eACA,cACD,EACA,wDAAyD,CACxD,kDACD,EACA,4DAA6D,CAC5D,6CACA,kDACA,6CACA,kDACA,6CACA,kDACA,uCACA,4CACA,uCACA,4CACA,yDACA,yDACA,mDACA,qEACA,2BACA,6CACA,yDACA,uCACA,uCACA,mDACA,mDACA,uCACA,mGACA,uCACA,uCACA,iFACA,4EACA,4EACA,kDACD,EACA,wDAAyD,CACxD,qEACA,oKACD,EACA,wCAAyC,CACxC,sLACA,yMACA,6CACA,6CACA,iCACA,kCACA,oCACA,iCACA,kDACA,sCACD,EACA,sDAAuD,CACtD,uLACD,EACA,oDAAqD,CACpD,8DACA,8DACA,yDACA,8DACA,yDACA,8DACA,+DACA,oEACA,yDACA,yDACA,mGACA,8DACA,uFACA,8DACA,qHACA,2HACA,+GACA,0EACA,qEACA,2EACA,yGACA,uFACA,wGACD,EACA,0DAA2D,CAC1D,uFACD,EACA,gEAAiE,CAChE,oJACA,oJACA,8BACA,mJACD,EACA,8CAA+C,CAC9C,kLACA,qBACA,gHACA,mFACA,oGACA,8FACA,oGACA,+IACA,qJACA,0JACA,0JACA,qGACA,uHACA,2JACA,wIACA,yIACA,wIACD,EACA,4DAA6D,CAC5D,0JACA,8EACA,uDACA,wMACA,gJACD,EACA,8DAA+D,CAC9D,wOACA,0MACA,sHACA,8FACA,6CACA,uCACA,iCACA,uCACA,iCACA,qBACA,yDACA,qBACA,2BACA,2BACA,iCACA,2BACA,yDACA,iCACA,uCACA,mDACA,uCACA,uCACA,2BACA,mDACA,2BACA,iCACA,qBACA,qBACA,iCACA,6CACA,qBACA,uCACA,oBACD,EACA,yDAA0D,CACzD,+GACA,0HACD,EACA,0CAA2C,CAC1C,yDACA,iLACA,+FACA,oFACA,mJACA,qHACA,iIACA,yDACA,kMACD,EACA,oDAAqD,CACpD,6IACA,qNACA,4HACD,EACA,oDAAqD,CACpD,6CACA,kDACA,6CACA,iDACD,EACA,uDAAwD,CACvD,oJACA,gKACA,gKACA,mFACD,EACA,qDAAsD,CACrD,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,YACA,YACA,YACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,YACA,YACA,WACD,EACA,6DAA8D,CAC7D,2EACA,gFACA,6CACA,kDACA,uFACA,2FACA,iFACA,iFACA,qHACA,kDACD,EACA,4CAA6C,CAC5C,yGACA,6FACA,mGACA,qQACA,iIACA,iIACA,2HACA,kJACD,EACA,sDAAuD,CACtD,4FACA,6CACA,eACA,eACA,eACA,eACA,eACA,uCACA,iCACA,8GACD,EACA,kDAAmD,CAClD,uFACA,uFACA,iFACA,iIACA,yJACA,6IACA,6IACA,6OACA,mGACA,sDACA,yDACA,eACA,mBACA,UACA,gBACA,oDACD,EACA,yDAA0D,CACzD,qBACA,qDACD,EACA,0DAA2D,CAC1D,qHACA,gCACD,EACA,wDAAyD,CACxD,WACD,EACA,oDAAqD,CACpD,+SACA,2TACA,qTACA,ySACA,+SACA,mVACA,qTACA,6UACA,2TACA,iUACA,2TACA,iUACA,+SACA,mVACA,+SACA,iUACA,2TACA,iUACA,2TACA,wSACA,+SACA,uUACA,qTACA,iUACA,iUACA,+SACA,iUACA,qTACA,qTACA,2TACA,uUACA,+SACA,8SACD,EACA,kEAAmE,CAClE,+HACA,iMACA,kHACD,EACA,sDAAuD,CACtD,kFACD,EACA,kEAAmE,CAClE,6LACA,qLACA,oLACA,wKACA,iOACA,uNACA,6DACA,yGACA,iCACA,6FACA,+GACA,uFACA,yGACA,qHACA,qHACA,mGACA,yGACA,8FACA,sHACA,+DACA,gHACA,gFACA,+HACA,oGACD,EACA,0EAA2E,CAC1E,qEACA,qHACA;AAAA;AAAA,6QACA,4FACA,0BACD,EACA,iEAAkE,CACjE,2WACA,2WACA,qTACA,uLACA,6LACA,+GACA,iXACA,8XACA,qWACD,EACA,4DAA6D,CAC5D,wFACA,wFACA,uFACD,EACA,0DAA2D,CAC1D,gCACD,EACA,mDAAoD,CACnD,eACA,qBACA,qBACA,2BACA,2BACA,oBACD,EACA,8CAA+C,CAC9C,+FACA,2CACA,iEACA,qEACD,EACA,gDAAiD,CAChD,gKACA,oJACA,gHACA,+DACA,+DACA,yDACA,4FACD,EACA,sDAAuD,CACtD,YACA,YACA;AAAA,UACD,EACA,sCAAuC,CACtC,qBACA,wDACD,EACA,yCAA0C,CACzC,gCACD,EACA,gDAAiD,CAChD,iCACA,2BACA,+DACA,+DACA,yDACA,sEACA,iIACA,8FACA,+DACA,+DACA,oGACA,8CACA,8FACA,8FACA,qEACA,qEACA,mDACA,uFACA,iFACA,qEACA,qBACA,eACA,cACD,EACA,yDAA0D,CACzD,6FACA,6GACA,+JACA,qKACA,+JACA,qKACA,6FACA,2cACA,iLACA,wNACD,EACA,mDAAoD,CACnD,iIACD,EACA,2CAA4C,CAC3C,sEACA,gQACA,qEACA,2CACA,yDACA,oIACA,+DACA,iHACA;AAAA,sCACD,EACA,4CAA6C,CAC5C,mIACA,mIACA,qIACA,sIACA,oOACA,iIACA,8JACA,+CACA,8JACD,EACA,wCAAyC,CACxC,oEACA,2FACA,yGACA,4HACA,iTACD,EACA,yDAA0D,CACzD,oEACD,EACA,0DAA2D,CAC1D,sIACA,8HACA,iJACA,gJACD,EACA,uCAAwC,CACvC,uCACA,uKACA,kKACA,+rBACA,kfACA,yaACA,iOACA,6IACA,iLACA,0MACA,4LACA,2HACA,2TACA,+GACA,uLACA,wXACA,8dACA,4WACA,mXACA,oKACA,yGACA,6FACA,6OACA,wdACA,2KACA,6NACA,+dACD,EACA,qCAAsC,CACrC,qBACA,eACA,cACD,EACA,qDAAsD,CACrD,mDACA,6CACA,mDACA,mDACA,6CACA,WACA,mGACD,EACA,iDAAkD,CACjD,UACD,EACA,4CAA6C,CAC5C,eACA,yJACA,UACA,kGACD,EACA,sDAAuD,CACtD,gHACA,+BACA,qCACA,KACA,2BACA,qBACA,cACD,EACA,gDAAiD,CAChD,sCACD,EACA,iDAAkD,CACjD,uFACD,EACA,yCAA0C,CACzC,mSACA,2TACA,4TACA,wIACA,yGACA,6UACA,iLACA,+MACA,wYACA,iFACA,uFACA,2NACA,wIACA,kIACA,yGACA,+GACA,gHACA,+LACA,2MACA,+DACA,+DACA,qEACA,0JACA,qKACA,uIACA,qKACA,oJACA,+GACA,+GACA,qHACA,+GACA,+GACA,qHACA,qHACA,qHACA,2HACA,qEACA,8FACA,qEACA,2EACA,mDACA,2EACA,mDACA,2EACA,+DACA,wEACA,wEACA,sGACA,gNACA,gNACA,sKACA,gHACA,mIACA,yIACA,0JACA,wUACA,qHACA,2KACA,iUACA,+GACA,qKACA,iUACA,+GACA,qKACA,qHACA,2KACA,uFACA,+DACA,iFACA,yGACA,2HACA,qJACA,2JACA,waACA,4fACA,2UACA,2UACA,0WACA,6IACA,6IACA,0TACA,0TACA,sUACA,kVACA,iFACA,4HACA,6UACA,iUACA,qHACA,2EACA,iUACA,6RACA,mGACA,6FACA,+SACA,2HACA,8IACA,yJACA,+VACA,kFACA,kFACA,8FACA,wIACA,2EACA,4EACA,4EACA,8FACA,8FACA,sHACA,sHACA,gHACA,gKACA,qFACA,ySACA,mSACA,uRACA,iRACA,uFACA,uFACA,mMACA,mMACA,6FACA,6FACA,8HACA,+SACA,+JACA,+JACA,yJACA,0eACA,0eACA,siBACA,wkBACA,8dACA,wdACA,sfACA,8dACA,8dACA,4fACA,gfACA,4iBACA,+JACA,+JACA,oOACA,+MACA,uNACA,uOACA,mGACA,yGACA,sKACA,8LACA,gQACA,sKACA,yGACA,qHACA,6HACA,6KACA,qEACA,2EACA,+IACA,sHACA,oMACA,sHACA,kIACA,gNACA,iHACA,uIACA,+JACA,uIACA,+DACA,qEACA,qEACA,iIACA,iIACA,uIACA,+GACA,6NACA,6NACA,6NACA,wHACA,oIACA,gJACA,uLACA,qHACA,iFACA,qHACA,mGACA,iFACA,0VACA,0VACA,oVACA,0VACA,sWACA,4WACA,+JACA,iIACA,uIACA,6IACA,gWACA,wXACA,wIACA,kLACA,8IACA,8FACA,8FACA,oGACA,+DACA,2UACA,8FACA,8IACA,oJACA,gHACA,yGACA,mGACA,iFACA,6FACA,mGACA,mGACA,mGACA,qHACA,mGACA,kGACD,EACA,wCAAyC,CACxC,8QACA,iIACA,6IACA,6IACA,4IACD,EACA,8CAA+C,CAC9C,oKACA,8JACA,2IACA,2IACA,8MACA,wVACA,gWACA,iHACA,8EACA,qEACA,2QACA,0DACA,oBACA,qBACA,uJACA,uJACA,oNACA,wVACA,gWACA,0QACD,EACA,yCAA0C,CACzC,+DACD,CACD,CAAC", "names": [], "file": "editor.main.nls.ja.js"}