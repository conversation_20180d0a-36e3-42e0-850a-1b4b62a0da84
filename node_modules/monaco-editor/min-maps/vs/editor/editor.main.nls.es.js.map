{"version": 3, "sources": ["out-editor/vs/editor/editor.main.nls.es.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/editor/editor.main.nls.es\", {\n\t\"vs/base/browser/ui/actionbar/actionViewItems\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInput\": [\n\t\t\"entrada\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInputToggles\": [\n\t\t\"Coincidir mayúsculas y minúsculas\",\n\t\t\"Solo palabras completas\",\n\t\t\"Usar expresión regular\",\n\t],\n\t\"vs/base/browser/ui/findinput/replaceInput\": [\n\t\t\"entrada\",\n\t\t\"Conservar may/min\",\n\t],\n\t\"vs/base/browser/ui/hover/hoverWidget\": [\n\t\t\"Inspeccione esto en la vista accesible con {0}.\",\n\t\t\"Inspeccione esto en la vista accesible mediante el comando Abrir vista accesible, que actualmente no se puede desencadenar mediante el enlace de teclado.\",\n\t],\n\t\"vs/base/browser/ui/iconLabel/iconLabelHover\": [\n\t\t\"Cargando...\",\n\t],\n\t\"vs/base/browser/ui/inputbox/inputBox\": [\n\t\t\"Error: {0}\",\n\t\t\"Advertencia: {0}\",\n\t\t\"Información: {0}\",\n\t\t\" o {0} para el historial\",\n\t\t\" ({0} para el historial)\",\n\t\t\"Entrada borrada\",\n\t],\n\t\"vs/base/browser/ui/keybindingLabel/keybindingLabel\": [\n\t\t\"Sin enlazar\",\n\t],\n\t\"vs/base/browser/ui/selectBox/selectBoxCustom\": [\n\t\t\"Seleccionar cuadro\",\n\t],\n\t\"vs/base/browser/ui/toolbar/toolbar\": [\n\t\t\"Más Acciones...\",\n\t],\n\t\"vs/base/browser/ui/tree/abstractTree\": [\n\t\t\"Filtrar\",\n\t\t\"Coincidencia aproximada\",\n\t\t\"Escriba texto para filtrar\",\n\t\t\"Escriba texto para buscar\",\n\t\t\"Escriba texto para buscar\",\n\t\t\"Cerrar\",\n\t\t\"No se encontraron elementos.\",\n\t],\n\t\"vs/base/common/actions\": [\n\t\t\"(vacío)\",\n\t],\n\t\"vs/base/common/errorMessage\": [\n\t\t\"{0}: {1}\",\n\t\t\"Error del sistema ({0})\",\n\t\t\"Se ha producido un error desconocido. Consulte el registro para obtener más detalles.\",\n\t\t\"Se ha producido un error desconocido. Consulte el registro para obtener más detalles.\",\n\t\t\"{0} ({1} errores en total)\",\n\t\t\"Se ha producido un error desconocido. Consulte el registro para obtener más detalles.\",\n\t],\n\t\"vs/base/common/keybindingLabels\": [\n\t\t\"Ctrl\",\n\t\t\"Mayús\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Ctrl\",\n\t\t\"Mayús\",\n\t\t\"Alt\",\n\t\t\"Super\",\n\t\t\"Control\",\n\t\t\"Mayús\",\n\t\t\"Opción\",\n\t\t\"Comando\",\n\t\t\"Control\",\n\t\t\"Mayús\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Control\",\n\t\t\"Mayús\",\n\t\t\"Alt\",\n\t\t\"Super\",\n\t],\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/browser/controller/textAreaHandler\": [\n\t\t\"editor\",\n\t\t\"No se puede acceder al editor en este momento.\",\n\t\t\"{0} Para habilitar el modo optimizado para lectores de pantalla, use {1}\",\n\t\t\"{0} Para habilitar el modo optimizado para lector de pantalla, abra la selección rápida con {1} y ejecute el comando Alternar modo de accesibilidad del lector de pantalla, que actualmente no se puede desencadenar mediante el teclado.\",\n\t\t\"{0} Para asignar un enlace de teclado para el comando Alternar modo de accesibilidad del lector de pantalla, acceda al editor de enlaces de teclado con {1} y ejecútelo.\",\n\t],\n\t\"vs/editor/browser/coreCommands\": [\n\t\t\"Anclar al final incluso cuando se vayan a líneas más largas\",\n\t\t\"Anclar al final incluso cuando se vayan a líneas más largas\",\n\t\t\"Cursores secundarios quitados\",\n\t],\n\t\"vs/editor/browser/editorExtensions\": [\n\t\t\"&&Deshacer\",\n\t\t\"Deshacer\",\n\t\t\"&&Rehacer\",\n\t\t\"Rehacer\",\n\t\t\"&&Seleccionar todo\",\n\t\t\"Seleccionar todo\",\n\t],\n\t\"vs/editor/browser/widget/codeEditorWidget\": [\n\t\t\"El número de cursores se ha limitado a {0}. Considere la posibilidad de usar [buscar y reemplazar](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) para realizar cambios mayores o aumentar la configuración del límite de varios cursores del editor.\",\n\t\t\"Aumentar el límite de varios cursores\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/accessibleDiffViewer\": [\n\t\t\"Icono de \\\"Insertar\\\" en el visor de diferencias accesible.\",\n\t\t\"Icono de \\\"Quitar\\\" en el visor de diferencias accesible.\",\n\t\t\"Icono de \\\"Cerrar\\\" en el visor de diferencias accesible.\",\n\t\t\"Cerrar\",\n\t\t\"Visor de diferencias accesible. Utilice la flecha hacia arriba y hacia abajo para navegar.\",\n\t\t\"no se han cambiado líneas\",\n\t\t\"1 línea cambiada\",\n\t\t\"{0} líneas cambiadas\",\n\t\t\"Diferencia {0} de {1}: línea original {2}, {3}, línea modificada {4}, {5}\",\n\t\t\"vacío\",\n\t\t\"{0} línea sin cambios {1}\",\n\t\t\"{0} línea original {1} línea modificada {2}\",\n\t\t\"+ {0} línea modificada {1}\",\n\t\t\"- {0} línea original {1}\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/colors\": [\n\t\t\"Color del borde del texto que se movió en el editor de diferencias.\",\n\t\t\"Color del borde de texto activo que se movió en el editor de diferencias.\",\n\t\t\"Color de la sombra paralela en torno a los widgets de región sin cambios.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/decorations\": [\n\t\t\"Decoración de línea para las inserciones en el editor de diferencias.\",\n\t\t\"Decoración de línea para las eliminaciones en el editor de diferencias.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditor.contribution\": [\n\t\t\"Alternar contraer regiones sin cambios\",\n\t\t\"Alternar Mostrar bloques de código movidos\",\n\t\t\"Alternar el uso de la vista insertada cuando el espacio es limitado\",\n\t\t\"Uso de la vista insertada cuando el espacio es limitado\",\n\t\t\"Mostrar bloques de código movidos\",\n\t\t\"Editor de diferencias\",\n\t\t\"Lado del conmutador\",\n\t\t\"Salir de la comparación de movimientos\",\n\t\t\"Contraer todas las regiones sin cambios\",\n\t\t\"Mostrar todas las regiones sin cambios\",\n\t\t\"Visor de diferencias accesibles\",\n\t\t\"Ir a la siguiente diferencia\",\n\t\t\"Abrir visor de diferencias accesibles\",\n\t\t\"Ir a la diferencia anterior\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorDecorations\": [\n\t\t\"Revertir los cambios seleccionados\",\n\t\t\"Revertir el cambio\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorEditors\": [\n\t\t\" use {0} para abrir la ayuda de accesibilidad.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature\": [\n\t\t\"Plegar la región sin cambios\",\n\t\t\"Haga clic o arrastre para mostrar más arriba\",\n\t\t\"Mostrar región sin cambios\",\n\t\t\"Hacer clic o arrastrar para mostrar más abajo\",\n\t\t\"{0} líneas ocultas\",\n\t\t\"Doble clic para desplegar\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin\": [\n\t\t\"Copiar líneas eliminadas\",\n\t\t\"Copiar línea eliminada\",\n\t\t\"Copiar líneas cambiadas\",\n\t\t\"Copiar línea cambiada\",\n\t\t\"Copiar la línea eliminada ({0})\",\n\t\t\"Copiar línea cambiada ({0})\",\n\t\t\"Revertir este cambio\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/movedBlocksLines\": [\n\t\t\"Código movido con cambios en la línea {0}-{1}\",\n\t\t\"Código movido con cambios de la línea {0}-{1}\",\n\t\t\"Código movido a la línea {0}-{1}\",\n\t\t\"Código movido de la línea {0}-{1}\",\n\t],\n\t\"vs/editor/browser/widget/multiDiffEditorWidget/colors\": [\n\t\t\"Color de fondo del encabezado del editor de diferencias\",\n\t],\n\t\"vs/editor/common/config/editorConfigurationSchema\": [\n\t\t\"Editor\",\n\t\t\"El número de espacios a los que equivale una tabulación. Este valor se invalida en función del contenido del archivo cuando {0} está activado.\",\n\t\t\"Número de espacios usados para la sangría o \\\"tabSize\\\" para usar el valor de \\\"#editor.tabSize#\\\". Esta configuración se invalida en función del contenido del archivo cuando \\\"#editor.detectIndentation#\\\" está activado.\",\n\t\t\"Insertar espacios al presionar \\\"TAB\\\". Este valor se invalida en función del contenido del archivo cuando {0} está activado.\",\n\t\t\"Controla si {0} y {1} se detectan automáticamente al abrir un archivo en función del contenido de este.\",\n\t\t\"Quitar el espacio en blanco final autoinsertado.\",\n\t\t\"Manejo especial para archivos grandes para desactivar ciertas funciones de memoria intensiva.\",\n\t\t\"Desactivar sugerencias basadas en Word.\",\n\t\t\"Sugerir palabras solo del documento activo.\",\n\t\t\"Sugerir palabras de todos los documentos abiertos del mismo idioma.\",\n\t\t\"Sugerir palabras de todos los documentos abiertos.\",\n\t\t\"Controla si las finalizaciones se deben calcular en función de las palabras del documento y desde qué documentos se calculan.\",\n\t\t\"El resaltado semántico está habilitado para todos los temas de color.\",\n\t\t\"El resaltado semántico está deshabilitado para todos los temas de color.\",\n\t\t\"El resaltado semántico está configurado con el valor \\\"semanticHighlighting\\\" del tema de color actual.\",\n\t\t\"Controla si se muestra semanticHighlighting para los idiomas que lo admiten.\",\n\t\t\"Mantiene abiertos los editores interactivos, incluso al hacer doble clic en su contenido o presionar \\\"Escape\\\".\",\n\t\t\"Las lineas por encima de esta longitud no se tokenizarán por razones de rendimiento.\",\n\t\t\"Controla si la tokenización debe producirse de forma asincrónica en un rol de trabajo.\",\n\t\t\"Controla si se debe registrar la tokenización asincrónica. Solo para depuración.\",\n\t\t\"Controla si se debe comprobar la tokenización asincrónica con la tokenización en segundo plano heredada. Puede ralentizar la tokenización. Solo para depuración.\",\n\t\t\"Define los corchetes que aumentan o reducen la sangría.\",\n\t\t\"Secuencia de cadena o corchete de apertura.\",\n\t\t\"Secuencia de cadena o corchete de cierre.\",\n\t\t\"Define los pares de corchetes coloreados por su nivel de anidamiento si está habilitada la coloración de par de corchetes.\",\n\t\t\"Secuencia de cadena o corchete de apertura.\",\n\t\t\"Secuencia de cadena o corchete de cierre.\",\n\t\t\"Tiempo de espera en milisegundos después del cual se cancela el cálculo de diferencias. Utilice 0 para no usar tiempo de espera.\",\n\t\t\"Tamaño máximo de archivo en MB para el que calcular diferencias. Use 0 para no limitar.\",\n\t\t\"Controla si el editor de diferencias muestra las diferencias en paralelo o alineadas.\",\n\t\t\"Si el ancho del editor de diferencias es menor que este valor, se usa la vista insertada.\",\n\t\t\"Si está habilitada y el ancho del editor es demasiado pequeño, se usa la vista en línea.\",\n\t\t\"Cuando está habilitado, el editor de diferencias muestra flechas en su margen de glifo para revertir los cambios.\",\n\t\t\"Cuando está habilitado, el editor de diferencias omite los cambios en los espacios en blanco iniciales o finales.\",\n\t\t\"Controla si el editor de diferencias muestra los indicadores +/- para los cambios agregados o quitados.\",\n\t\t\"Controla si el editor muestra CodeLens.\",\n\t\t\"Las líneas no se ajustarán nunca.\",\n\t\t\"Las líneas se ajustarán en el ancho de la ventanilla.\",\n\t\t\"Las líneas se ajustarán en función de la configuración de {0}.\",\n\t\t\"Usa el algoritmo de diferenciación heredado.\",\n\t\t\"Usa el algoritmo de diferenciación avanzada.\",\n\t\t\"Controla si el editor de diferencias muestra las regiones sin cambios.\",\n\t\t\"Controla cuántas líneas se usan para las regiones sin cambios.\",\n\t\t\"Controla cuántas líneas se usan como mínimo para las regiones sin cambios.\",\n\t\t\"Controla cuántas líneas se usan como contexto al comparar regiones sin cambios.\",\n\t\t\"Controlar si el editor de diferencias debe mostrar los movimientos de código detectados.\",\n\t\t\"Controla si el editor de diferencias muestra decoraciones vacías para ver dónde se insertan o eliminan los caracteres.\",\n\t],\n\t\"vs/editor/common/config/editorOptions\": [\n\t\t\"Usar las API de la plataforma para detectar cuándo se conecta un lector de pantalla.\",\n\t\t\"Optimizar para usar con un lector de pantalla.\",\n\t\t\"Supongamos que no hay un lector de pantalla conectado.\",\n\t\t\"Controla si la interfaz de usuario debe ejecutarse en un modo en el que esté optimizada para lectores de pantalla.\",\n\t\t\"Controla si se inserta un carácter de espacio al comentar.\",\n\t\t\"Controla si las líneas vacías deben ignorarse con la opción de alternar, agregar o quitar acciones para los comentarios de línea.\",\n\t\t\"Controla si al copiar sin selección se copia la línea actual.\",\n\t\t\"Controla si el cursor debe saltar para buscar coincidencias mientras se escribe.\",\n\t\t\"Nunca inicializar la cadena de búsqueda desde la selección del editor.\",\n\t\t\"Siempre inicializar la cadena de búsqueda desde la selección del editor, incluida la palabra en la posición del cursor.\",\n\t\t\"Solo inicializar la cadena de búsqueda desde la selección del editor.\",\n\t\t\"Controla si la cadena de búsqueda del widget de búsqueda se inicializa desde la selección del editor.\",\n\t\t\"No activar nunca Buscar en selección automáticamente (predeterminado).\",\n\t\t\"Activar siempre Buscar en selección automáticamente.\",\n\t\t\"Activar Buscar en la selección automáticamente cuando se seleccionen varias líneas de contenido.\",\n\t\t\"Controla la condición para activar la búsqueda en la selección de forma automática.\",\n\t\t\"Controla si el widget de búsqueda debe leer o modificar el Portapapeles de búsqueda compartido en macOS.\",\n\t\t\"Controla si Encontrar widget debe agregar más líneas en la parte superior del editor. Si es true, puede desplazarse más allá de la primera línea cuando Encontrar widget está visible.\",\n\t\t\"Controla si la búsqueda se reinicia automáticamente desde el principio (o el final) cuando no se encuentran más coincidencias.\",\n\t\t\"Habilita o deshabilita las ligaduras tipográficas (características de fuente \\\"calt\\\" y \\\"liga\\\"). Cámbielo a una cadena para el control específico de la propiedad de CSS \\\"font-feature-settings\\\".\",\n\t\t\"Propiedad de CSS \\\"font-feature-settings\\\" explícita. En su lugar, puede pasarse un valor booleano si solo es necesario activar o desactivar las ligaduras.\",\n\t\t\"Configura las ligaduras tipográficas o las características de fuente. Puede ser un valor booleano para habilitar o deshabilitar las ligaduras o bien una cadena para el valor de la propiedad \\\"font-feature-settings\\\" de CSS.\",\n\t\t\"Habilita o deshabilita la traducción del grosor de font-weight a font-variation-settings. Cambie esto a una cadena para el control específico de la propiedad CSS \\'font-variation-settings\\'.\",\n\t\t\"Propiedad CSS explícita \\'font-variation-settings\\'. En su lugar, se puede pasar un valor booleano si solo es necesario traducir font-weight a font-variation-settings.\",\n\t\t\"Configura variaciones de fuente. Puede ser un booleano para habilitar o deshabilitar la traducción de font-weight a font-variation-settings o una cadena para el valor de la propiedad CSS \\'font-variation-settings\\'.\",\n\t\t\"Controla el tamaño de fuente en píxeles.\",\n\t\t\"Solo se permiten las palabras clave \\\"normal\\\" y \\\"negrita\\\" o los números entre 1 y 1000.\",\n\t\t\"Controla el grosor de la fuente. Acepta las palabras clave \\\"normal\\\" y \\\"negrita\\\" o los números entre 1 y 1000.\",\n\t\t\"Mostrar vista de inspección de los resultados (predeterminado)\",\n\t\t\"Ir al resultado principal y mostrar una vista de inspección\",\n\t\t\"Vaya al resultado principal y habilite la navegación sin peek para otros\",\n\t\t\"Esta configuración está en desuso. Use configuraciones separadas como \\\"editor.editor.gotoLocation.multipleDefinitions\\\" o \\\"editor.editor.gotoLocation.multipleImplementations\\\" en su lugar.\",\n\t\t\"Controla el comportamiento del comando \\\"Ir a definición\\\" cuando existen varias ubicaciones de destino.\",\n\t\t\"Controla el comportamiento del comando \\\"Ir a definición de tipo\\\" cuando existen varias ubicaciones de destino.\",\n\t\t\"Controla el comportamiento del comando \\\"Ir a declaración\\\" cuando existen varias ubicaciones de destino.\",\n\t\t\"Controla el comportamiento del comando \\\"Ir a implementaciones\\\" cuando existen varias ubicaciones de destino.\",\n\t\t\"Controla el comportamiento del comando \\\"Ir a referencias\\\" cuando existen varias ubicaciones de destino.\",\n\t\t\"Identificador de comando alternativo que se ejecuta cuando el resultado de \\\"Ir a definición\\\" es la ubicación actual.\",\n\t\t\"Id. de comando alternativo que se está ejecutando cuando el resultado de \\\"Ir a definición de tipo\\\" es la ubicación actual.\",\n\t\t\"Id. de comando alternativo que se está ejecutando cuando el resultado de \\\"Ir a declaración\\\" es la ubicación actual.\",\n\t\t\"Id. de comando alternativo que se está ejecutando cuando el resultado de \\\"Ir a implementación\\\" es la ubicación actual.\",\n\t\t\"Identificador de comando alternativo que se ejecuta cuando el resultado de \\\"Ir a referencia\\\" es la ubicación actual.\",\n\t\t\"Controla si se muestra la información al mantener el puntero sobre un elemento.\",\n\t\t\"Controla el retardo en milisegundos después del cual se muestra la información al mantener el puntero sobre un elemento.\",\n\t\t\"Controla si la información que aparece al mantener el puntero sobre un elemento permanece visible al mover el mouse sobre este.\",\n\t\t\"Controla el retraso en milisegundos después del cual se oculta el desplazamiento. Requiere que se habilite `editor.hover.sticky`.\",\n\t\t\"Preferir mostrar los desplazamientos por encima de la línea, si hay espacio.\",\n\t\t\"Se supone que todos los caracteres son del mismo ancho. Este es un algoritmo rápido que funciona correctamente para fuentes monoespaciales y ciertos scripts (como caracteres latinos) donde los glifos tienen el mismo ancho.\",\n\t\t\"Delega el cálculo de puntos de ajuste en el explorador. Es un algoritmo lento, que podría causar bloqueos para archivos grandes, pero funciona correctamente en todos los casos.\",\n\t\t\"Controla el algoritmo que calcula los puntos de ajuste. Tenga en cuenta que, en el modo de accesibilidad, se usará el modo avanzado para obtener la mejor experiencia.\",\n\t\t\"Habilita la bombilla de acción de código en el editor.\",\n\t\t\"No mostrar el icono de IA.\",\n\t\t\"Muestra un icono de IA cuando el menú de acción de código contiene una acción de IA, pero solo en código.\",\n\t\t\"Muestra un icono de IA cuando el menú de acción de código contiene una acción de IA, en código y líneas vacías.\",\n\t\t\"Muestra un icono de IA junto con la bombilla cuando el menú de acción de código contiene una acción de IA.\",\n\t\t\"Muestra los ámbitos actuales anidados durante el desplazamiento en la parte superior del editor.\",\n\t\t\"Define el número máximo de líneas rápidas que se mostrarán.\",\n\t\t\"Define el modelo que se va a usar para determinar qué líneas se van a pegar. Si el modelo de esquema no existe, recurrirá al modelo del proveedor de plegado que recurre al modelo de sangría. Este orden se respeta en los tres casos.\",\n\t\t\"Habilite el desplazamiento de desplazamiento rápido con la barra de desplazamiento horizontal del editor.\",\n\t\t\"Habilita las sugerencias de incrustación en el editor.\",\n\t\t\"Las sugerencias de incrustación están habilitadas\",\n\t\t\"Las sugerencias de incrustación se muestran de forma predeterminada y se ocultan cuando se mantiene presionado {0}\",\n\t\t\"Las sugerencias de incrustación están ocultas de forma predeterminada y se muestran al mantener presionado {0}\",\n\t\t\"Las sugerencias de incrustación están deshabilitadas\",\n\t\t\"Controla el tamaño de fuente de las sugerencias de incrustación en el editor. Como valor predeterminado, se usa {0} cuando el valor configurado es menor que {1} o mayor que el tamaño de fuente del editor.\",\n\t\t\"Controla la familia de fuentes de sugerencias de incrustación en el editor. Cuando se establece en vacío, se usa el {0}.\",\n\t\t\"Habilita el relleno alrededor de las sugerencias de incrustación en el editor.\",\n\t\t\"Controla el alto de línea. \\r\\n - Use 0 para calcular automáticamente el alto de línea a partir del tamaño de la fuente.\\r\\n - Los valores entre 0 y 8 se usarán como multiplicador con el tamaño de fuente.\\r\\n - Los valores mayores o igual que 8 se usarán como valores efectivos.\",\n\t\t\"Controla si se muestra el minimapa.\",\n\t\t\"Controla si el minimapa se oculta automáticamente.\",\n\t\t\"El minimapa tiene el mismo tamaño que el contenido del editor (y podría desplazarse).\",\n\t\t\"El minimapa se estirará o reducirá según sea necesario para ocupar la altura del editor (sin desplazamiento).\",\n\t\t\"El minimapa se reducirá según sea necesario para no ser nunca más grande que el editor (sin desplazamiento).\",\n\t\t\"Controla el tamaño del minimapa.\",\n\t\t\"Controla en qué lado se muestra el minimapa.\",\n\t\t\"Controla cuándo se muestra el control deslizante del minimapa.\",\n\t\t\"Escala del contenido dibujado en el minimapa: 1, 2 o 3.\",\n\t\t\"Represente los caracteres reales en una línea, por oposición a los bloques de color.\",\n\t\t\"Limite el ancho del minimapa para representar como mucho un número de columnas determinado.\",\n\t\t\"Controla la cantidad de espacio entre el borde superior del editor y la primera línea.\",\n\t\t\"Controla el espacio entre el borde inferior del editor y la última línea.\",\n\t\t\"Habilita un elemento emergente que muestra documentación de los parámetros e información de los tipos mientras escribe.\",\n\t\t\"Controla si el menú de sugerencias de parámetros se cicla o se cierra al llegar al final de la lista.\",\n\t\t\"Las sugerencias rápidas se muestran dentro del widget de sugerencias\",\n\t\t\"Las sugerencias rápidas se muestran como texto fantasma\",\n\t\t\"Las sugerencias rápidas están deshabilitadas\",\n\t\t\"Habilita sugerencias rápidas en las cadenas.\",\n\t\t\"Habilita sugerencias rápidas en los comentarios.\",\n\t\t\"Habilita sugerencias rápidas fuera de las cadenas y los comentarios.\",\n\t\t\"Controla si las sugerencias deben mostrarse automáticamente al escribir. Puede controlarse para la escritura en comentarios, cadenas y otro código. Las sugerencias rápidas pueden configurarse para mostrarse como texto fantasma o con el widget de sugerencias. Tenga también en cuenta la configuración \\'{0}\\' que controla si las sugerencias son desencadenadas por caracteres especiales.\",\n\t\t\"Los números de línea no se muestran.\",\n\t\t\"Los números de línea se muestran como un número absoluto.\",\n\t\t\"Los números de línea se muestran como distancia en líneas a la posición del cursor.\",\n\t\t\"Los números de línea se muestran cada 10 líneas.\",\n\t\t\"Controla la visualización de los números de línea.\",\n\t\t\"Número de caracteres monoespaciales en los que se representará esta regla del editor.\",\n\t\t\"Color de esta regla del editor.\",\n\t\t\"Muestra reglas verticales después de un cierto número de caracteres monoespaciados. Usa múltiples valores para mostrar múltiples reglas. Si la matriz está vacía, no se muestran reglas.\",\n\t\t\"La barra de desplazamiento vertical estará visible solo cuando sea necesario.\",\n\t\t\"La barra de desplazamiento vertical estará siempre visible.\",\n\t\t\"La barra de desplazamiento vertical estará siempre oculta.\",\n\t\t\"Controla la visibilidad de la barra de desplazamiento vertical.\",\n\t\t\"La barra de desplazamiento horizontal estará visible solo cuando sea necesario.\",\n\t\t\"La barra de desplazamiento horizontal estará siempre visible.\",\n\t\t\"La barra de desplazamiento horizontal estará siempre oculta.\",\n\t\t\"Controla la visibilidad de la barra de desplazamiento horizontal.\",\n\t\t\"Ancho de la barra de desplazamiento vertical.\",\n\t\t\"Altura de la barra de desplazamiento horizontal.\",\n\t\t\"Controla si al hacer clic se desplaza por página o salta a la posición donde se hace clic.\",\n\t\t\"Cuando se establece, la barra de desplazamiento horizontal no aumentará el tamaño del contenido del editor.\",\n\t\t\"Controla si se resaltan todos los caracteres ASCII no básicos. Solo los caracteres entre U+0020 y U+007E, tabulación, avance de línea y retorno de carro se consideran ASCII básicos.\",\n\t\t\"Controla si se resaltan los caracteres que solo reservan espacio o que no tienen ancho.\",\n\t\t\"Controla si se resaltan caracteres que se pueden confundir con caracteres ASCII básicos, excepto los que son comunes en la configuración regional del usuario actual.\",\n\t\t\"Controla si los caracteres de los comentarios también deben estar sujetos al resaltado Unicode.\",\n\t\t\"Controla si los caracteres de las cadenas también deben estar sujetos al resaltado Unicode.\",\n\t\t\"Define los caracteres permitidos que no se resaltan.\",\n\t\t\"Los caracteres Unicode que son comunes en las configuraciones regionales permitidas no se resaltan.\",\n\t\t\"Controla si se deben mostrar automáticamente las sugerencias alineadas en el editor.\",\n\t\t\"Muestra la barra de herramientas de sugerencias insertadas cada vez que se muestra una sugerencia insertada.\",\n\t\t\"Muestra la barra de herramientas de sugerencias insertadas al mantener el puntero sobre una sugerencia insertada.\",\n\t\t\"No mostrar nunca la barra de herramientas de sugerencias insertadas.\",\n\t\t\"Controla cuándo mostrar la barra de herramientas de sugerencias insertadas.\",\n\t\t\"Controla cómo interactúan las sugerencias insertadas con el widget de sugerencias. Si se habilita, el widget de sugerencias no se muestra automáticamente cuando hay sugerencias insertadas disponibles.\",\n\t\t\"Controla si está habilitada o no la coloración de pares de corchetes. Use {0} para invalidar los colores de resaltado de corchete.\",\n\t\t\"Controla si cada tipo de corchete tiene su propio grupo de colores independiente.\",\n\t\t\"Habilita guías de par de corchetes.\",\n\t\t\"Habilita guías de par de corchetes solo para el par de corchetes activo.\",\n\t\t\"Deshabilita las guías de par de corchetes.\",\n\t\t\"Controla si están habilitadas las guías de pares de corchetes.\",\n\t\t\"Habilita guías horizontales como adición a guías de par de corchetes verticales.\",\n\t\t\"Habilita guías horizontales solo para el par de corchetes activo.\",\n\t\t\"Deshabilita las guías de par de corchetes horizontales.\",\n\t\t\"Controla si están habilitadas las guías de pares de corchetes horizontales.\",\n\t\t\"Controla si el editor debe resaltar el par de corchetes activo.\",\n\t\t\"Controla si el editor debe representar guías de sangría.\",\n\t\t\"Resalta la guía de sangría activa.\",\n\t\t\"Resalta la guía de sangría activa incluso si se resaltan las guías de corchetes.\",\n\t\t\"No resalta la guía de sangría activa.\",\n\t\t\"Controla si el editor debe resaltar la guía de sangría activa.\",\n\t\t\"Inserte la sugerencia sin sobrescribir el texto a la derecha del cursor.\",\n\t\t\"Inserte la sugerencia y sobrescriba el texto a la derecha del cursor.\",\n\t\t\"Controla si las palabras se sobrescriben al aceptar la finalización. Tenga en cuenta que esto depende de las extensiones que participan en esta característica.\",\n\t\t\"Controla si el filtrado y la ordenación de sugerencias se tienen en cuenta para los errores ortográficos pequeños.\",\n\t\t\"Controla si la ordenación mejora las palabras que aparecen cerca del cursor.\",\n\t\t\"Controla si las selecciones de sugerencias recordadas se comparten entre múltiples áreas de trabajo y ventanas (necesita \\\"#editor.suggestSelection#\\\").\",\n\t\t\"Seleccione siempre una sugerencia cuando se desencadene IntelliSense automáticamente.\",\n\t\t\"Nunca seleccione una sugerencia cuando desencadene IntelliSense automáticamente.\",\n\t\t\"Seleccione una sugerencia solo cuando desencadene IntelliSense desde un carácter de desencadenador.\",\n\t\t\"Seleccione una sugerencia solo cuando desencadene IntelliSense mientras escribe.\",\n\t\t\"Controla si se selecciona una sugerencia cuando se muestra el widget. Tenga en cuenta que esto solo se aplica a las sugerencias desencadenadas automáticamente (`#editor.quickSuggestions#` y `#editor.suggestOnTriggerCharacters#`) y que siempre se selecciona una sugerencia cuando se invoca explícitamente, por ejemplo, a través de \\'Ctrl+Espacio\\'.\",\n\t\t\"Controla si un fragmento de código activo impide sugerencias rápidas.\",\n\t\t\"Controla si mostrar u ocultar iconos en sugerencias.\",\n\t\t\"Controla la visibilidad de la barra de estado en la parte inferior del widget de sugerencias.\",\n\t\t\"Controla si se puede obtener una vista previa del resultado de la sugerencia en el editor.\",\n\t\t\"Controla si los detalles de sugerencia se muestran incorporados con la etiqueta o solo en el widget de detalles.\",\n\t\t\"La configuración está en desuso. Ahora puede cambiarse el tamaño del widget de sugerencias.\",\n\t\t\"Esta configuración está en desuso. Use configuraciones separadas como \\\"editor.suggest.showKeyword\\\" o \\\"editor.suggest.showSnippets\\\" en su lugar.\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"method\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de \\\"función\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"constructor\\\".\",\n\t\t\"Cuando se activa IntelliSense muestra sugerencias \\\"obsoletas\\\".\",\n\t\t\"Cuando se activa el filtro IntelliSense se requiere que el primer carácter coincida con el inicio de una palabra. Por ejemplo, \\\"c\\\" en \\\"Consola\\\" o \\\"WebContext\\\" but _not_ on \\\"descripción\\\". Si se desactiva, IntelliSense mostrará más resultados, pero los ordenará según la calidad de la coincidencia.\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"field\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"variable\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"class\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"struct\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"interface\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"module\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"property\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"event\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"operator\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"unit\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de \\\"value\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"constant\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"enum\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"enumMember\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"keyword\\\".\",\n\t\t\"Si está habilitado, IntelliSense muestra sugerencias de tipo \\\"text\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de \\\"color\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"file\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"reference\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"customcolor\\\".\",\n\t\t\"Si está habilitado, IntelliSense muestra sugerencias de tipo \\\"folder\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"typeParameter\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"snippet\\\".\",\n\t\t\"Cuando está habilitado, IntelliSense muestra sugerencias del usuario.\",\n\t\t\"Cuando está habilitado IntelliSense muestra sugerencias para problemas.\",\n\t\t\"Indica si los espacios en blanco iniciales y finales deben seleccionarse siempre.\",\n\t\t\"Indica si se deben seleccionar las subpalabras (como \\\"foo\\\" en \\\"fooBar\\\" o \\\"foo_bar\\\").\",\n\t\t\"No hay sangría. Las líneas ajustadas comienzan en la columna 1.\",\n\t\t\"A las líneas ajustadas se les aplica la misma sangría que al elemento primario.\",\n\t\t\"A las líneas ajustadas se les aplica una sangría de +1 respecto al elemento primario.\",\n\t\t\"A las líneas ajustadas se les aplica una sangría de +2 respecto al elemento primario.\",\n\t\t\"Controla la sangría de las líneas ajustadas.\",\n\t\t\"Controla si puede arrastrar y colocar un archivo en un editor de texto manteniendo presionada la tecla \\\"Mayús\\\" (en lugar de abrir el archivo en un editor).\",\n\t\t\"Controla si se muestra un widget al colocar archivos en el editor. Este widget le permite controlar cómo se coloca el archivo.\",\n\t\t\"Muestra el widget del selector de colocación después de colocar un archivo en el editor.\",\n\t\t\"No mostrar nunca el widget del selector de colocación. En su lugar, siempre se usa el proveedor de colocación predeterminado.\",\n\t\t\"Controla si se puede pegar contenido de distintas formas.\",\n\t\t\"Controla si se muestra un widget al pegar contenido en el editor. Este widget le permite controlar cómo se pega el archivo.\",\n\t\t\"Muestra el widget del selector de pegado después de pegar contenido en el editor.\",\n\t\t\"No mostrar nunca el widget del selector de pegado. En su lugar, siempre se usa el comportamiento de pegado predeterminado.\",\n\t\t\"Controla si se deben aceptar sugerencias en los caracteres de confirmación. Por ejemplo, en Javascript, el punto y coma (\\\";\\\") puede ser un carácter de confirmación que acepta una sugerencia y escribe ese carácter.\",\n\t\t\"Aceptar solo una sugerencia con \\\"Entrar\\\" cuando realiza un cambio textual.\",\n\t\t\"Controla si las sugerencias deben aceptarse con \\\"Entrar\\\", además de \\\"TAB\\\". Ayuda a evitar la ambigüedad entre insertar nuevas líneas o aceptar sugerencias.\",\n\t\t\"Controla el número de líneas del editor que pueden ser leídas por un lector de pantalla a la vez. Cuando detectamos un lector de pantalla, fijamos automáticamente el valor por defecto en 500. Advertencia: esto tiene una implicación de rendimiento para números mayores que el predeterminado.\",\n\t\t\"Contenido del editor\",\n\t\t\"Controlar si un lector de pantalla anuncia sugerencias insertadas.\",\n\t\t\"Utilizar las configuraciones del lenguaje para determinar cuándo cerrar los corchetes automáticamente.\",\n\t\t\"Cerrar automáticamente los corchetes cuando el cursor esté a la izquierda de un espacio en blanco.\",\n\t\t\"Controla si el editor debe cerrar automáticamente los corchetes después de que el usuario agregue un corchete de apertura.\",\n\t\t\"Utilice las configuraciones de idioma para determinar cuándo cerrar los comentarios automáticamente.\",\n\t\t\"Cerrar automáticamente los comentarios solo cuando el cursor esté a la izquierda de un espacio en blanco.\",\n\t\t\"Controla si el editor debe cerrar automáticamente los comentarios después de que el usuario agregue un comentario de apertura.\",\n\t\t\"Quite los corchetes o las comillas de cierre adyacentes solo si se insertaron automáticamente.\",\n\t\t\"Controla si el editor debe quitar los corchetes o las comillas de cierre adyacentes al eliminar.\",\n\t\t\"Escriba en las comillas o los corchetes solo si se insertaron automáticamente.\",\n\t\t\"Controla si el editor debe escribir entre comillas o corchetes.\",\n\t\t\"Utilizar las configuraciones del lenguaje para determinar cuándo cerrar las comillas automáticamente. \",\n\t\t\"Cerrar automáticamente las comillas cuando el cursor esté a la izquierda de un espacio en blanco. \",\n\t\t\"Controla si el editor debe cerrar automáticamente las comillas después de que el usuario agrega uma comilla de apertura.\",\n\t\t\"El editor no insertará la sangría automáticamente.\",\n\t\t\"El editor mantendrá la sangría de la línea actual.\",\n\t\t\"El editor respetará la sangría de la línea actual y los corchetes definidos por el idioma.\",\n\t\t\"El editor mantendrá la sangría de la línea actual, respetará los corchetes definidos por el idioma e invocará onEnterRules especiales definidos por idiomas.\",\n\t\t\"El editor respetará la sangría de la línea actual, los corchetes definidos por idiomas y las reglas indentationRules definidas por idiomas, además de invocar reglas onEnterRules especiales.\",\n\t\t\"Controla si el editor debe ajustar automáticamente la sangría mientras los usuarios escriben, pegan, mueven o sangran líneas.\",\n\t\t\"Use las configuraciones de idioma para determinar cuándo delimitar las selecciones automáticamente.\",\n\t\t\"Envolver con comillas, pero no con corchetes.\",\n\t\t\"Envolver con corchetes, pero no con comillas.\",\n\t\t\"Controla si el editor debe rodear automáticamente las selecciones al escribir comillas o corchetes.\",\n\t\t\"Emula el comportamiento de selección de los caracteres de tabulación al usar espacios para la sangría. La selección se aplicará a las tabulaciones.\",\n\t\t\"Controla si el editor muestra CodeLens.\",\n\t\t\"Controla la familia de fuentes para CodeLens.\",\n\t\t\"Controla el tamaño de fuente de CodeLens en píxeles. Cuando se establece en 0, se usa el 90 % de \\\"#editor.fontSize#\\\".\",\n\t\t\"Controla si el editor debe representar el Selector de colores y los elementos Decorator de color en línea.\",\n\t\t\"Hacer que el selector de colores aparezca tanto al hacer clic como al mantener el puntero sobre el decorador de color\",\n\t\t\"Hacer que el selector de colores aparezca al pasar el puntero sobre el decorador de color\",\n\t\t\"Hacer que el selector de colores aparezca al hacer clic en el decorador de color\",\n\t\t\"Controla la condición para que un selector de colores aparezca de un decorador de color\",\n\t\t\"Controla el número máximo de decoradores de color que se pueden representar en un editor a la vez.\",\n\t\t\"Habilite que la selección con el mouse y las teclas esté realizando la selección de columnas.\",\n\t\t\"Controla si el resaltado de sintaxis debe ser copiado al portapapeles.\",\n\t\t\"Controla el estilo de animación del cursor.\",\n\t\t\"La animación del símbolo de intercalación suave está deshabilitada.\",\n\t\t\"La animación de símbolo de intercalación suave solo se habilita cuando el usuario mueve el cursor con un gesto explícito.\",\n\t\t\"La animación de símbolo de intercalación suave siempre está habilitada.\",\n\t\t\"Controla si la animación suave del cursor debe estar habilitada.\",\n\t\t\"Controla el estilo del cursor.\",\n\t\t\"Controla el número mínimo de líneas iniciales visibles (mínimo 0) y líneas finales (mínimo 1) que rodean el cursor. Se conoce como \\\"scrollOff\\\" o \\\"scrollOffset\\\" en otros editores.\",\n\t\t\"Solo se aplica \\\"cursorSurroundingLines\\\" cuando se desencadena mediante el teclado o la API.\",\n\t\t\"\\\"cursorSurroundingLines\\\" se aplica siempre.\",\n\t\t\"Controla cuando se debe aplicar \\\"#cursorSurroundingLines#\\\".\",\n\t\t\"Controla el ancho del cursor cuando \\\"#editor.cursorStyle#\\\" se establece en \\\"line\\\".\",\n\t\t\"Controla si el editor debe permitir mover las selecciones mediante arrastrar y colocar.\",\n\t\t\"Use un nuevo método de representación con svgs.\",\n\t\t\"Use un nuevo método de representación con caracteres de fuente.\",\n\t\t\"Use el método de representación estable.\",\n\t\t\"Controla si los espacios en blanco se representan con un nuevo método experimental.\",\n\t\t\"Multiplicador de la velocidad de desplazamiento al presionar \\\"Alt\\\".\",\n\t\t\"Controla si el editor tiene el plegado de código habilitado.\",\n\t\t\"Utilice una estrategia de plegado específica del idioma, si está disponible, de lo contrario la basada en sangría.\",\n\t\t\"Utilice la estrategia de plegado basada en sangría.\",\n\t\t\"Controla la estrategia para calcular rangos de plegado.\",\n\t\t\"Controla si el editor debe destacar los rangos plegados.\",\n\t\t\"Permite controlar si el editor contrae automáticamente los rangos de importación.\",\n\t\t\"Número máximo de regiones plegables. Si aumenta este valor, es posible que el editor tenga menos capacidad de respuesta cuando el origen actual tiene un gran número de regiones plegables.\",\n\t\t\"Controla si al hacer clic en el contenido vacío después de una línea plegada se desplegará la línea.\",\n\t\t\"Controla la familia de fuentes.\",\n\t\t\"Controla si el editor debe dar formato automáticamente al contenido pegado. Debe haber disponible un formateador capaz de aplicar formato a un rango dentro de un documento. \",\n\t\t\"Controla si el editor debe dar formato a la línea automáticamente después de escribirla.\",\n\t\t\"Controla si el editor debe representar el margen de glifo vertical. El margen de glifo se usa, principalmente, para depuración.\",\n\t\t\"Controla si el cursor debe ocultarse en la regla de información general.\",\n\t\t\"Controla el espacio entre letras en píxeles.\",\n\t\t\"Controla si el editor tiene habilitada la edición vinculada. Dependiendo del lenguaje, los símbolos relacionados (por ejemplo, las etiquetas HTML) se actualizan durante la edición.\",\n\t\t\"Controla si el editor debe detectar vínculos y hacerlos interactivos.\",\n\t\t\"Resaltar paréntesis coincidentes.\",\n\t\t\"Se usará un multiplicador en los eventos de desplazamiento de la rueda del mouse \\\"deltaX\\\" y \\\"deltaY\\\". \",\n\t\t\"Ampliar la fuente del editor cuando se use la rueda del mouse mientras se presiona \\\"Ctrl\\\".\",\n\t\t\"Combinar varios cursores cuando se solapan.\",\n\t\t\"Se asigna a \\\"Control\\\" en Windows y Linux y a \\\"Comando\\\" en macOS.\",\n\t\t\"Se asigna a \\\"Alt\\\" en Windows y Linux y a \\\"Opción\\\" en macOS.\",\n\t\t\"El modificador que se usará para agregar varios cursores con el mouse. Los gestos del mouse Ir a definición y Abrir vínculo se adaptarán de modo que no entren en conflicto con el [modificador multicursor](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).\",\n\t\t\"Cada cursor pega una única línea del texto.\",\n\t\t\"Cada cursor pega el texto completo.\",\n\t\t\"Controla el pegado cuando el recuento de líneas del texto pegado coincide con el recuento de cursores.\",\n\t\t\"Controla el número máximo de cursores que puede haber en un editor activo a la vez.\",\n\t\t\"No resalta las repeticiones.\",\n\t\t\"Resalta las repeticiones solo en el archivo actual.\",\n\t\t\"Experimental: Resalta las repeticiones en todos los archivos abiertos válidos.\",\n\t\t\"Controla si las repeticiones deben resaltarse en los archivos abiertos.\",\n\t\t\"Controla si debe dibujarse un borde alrededor de la regla de información general.\",\n\t\t\"Enfocar el árbol al abrir la inspección\",\n\t\t\"Enfocar el editor al abrir la inspección\",\n\t\t\"Controla si se debe enfocar el editor en línea o el árbol en el widget de vista.\",\n\t\t\"Controla si el gesto del mouse Ir a definición siempre abre el widget interactivo.\",\n\t\t\"Controla el retraso, en milisegundos, tras el cual aparecerán sugerencias rápidas.\",\n\t\t\"Controla si el editor cambia el nombre automáticamente en el tipo.\",\n\t\t\"En desuso. Utilice \\\"editor.linkedEditing\\\" en su lugar.\",\n\t\t\"Controla si el editor debe representar caracteres de control.\",\n\t\t\"Representar el número de la última línea cuando el archivo termina con un salto de línea.\",\n\t\t\"Resalta el medianil y la línea actual.\",\n\t\t\"Controla cómo debe representar el editor el resaltado de línea actual.\",\n\t\t\"Controla si el editor debe representar el resaltado de la línea actual solo cuando el editor está enfocado.\",\n\t\t\"Representa caracteres de espacio en blanco, excepto los espacios individuales entre palabras.\",\n\t\t\"Represente los caracteres de espacio en blanco solo en el texto seleccionado.\",\n\t\t\"Representa solo los caracteres de espacio en blanco al final.\",\n\t\t\"Controla la forma en que el editor debe representar los caracteres de espacio en blanco.\",\n\t\t\"Controla si las selecciones deberían tener las esquinas redondeadas.\",\n\t\t\"Controla el número de caracteres adicionales a partir del cual el editor se desplazará horizontalmente.\",\n\t\t\"Controla si el editor seguirá haciendo scroll después de la última línea.\",\n\t\t\"Desplácese solo a lo largo del eje predominante cuando se desplace vertical y horizontalmente al mismo tiempo. Evita la deriva horizontal cuando se desplaza verticalmente en un trackpad.\",\n\t\t\"Controla si el portapapeles principal de Linux debe admitirse.\",\n\t\t\"Controla si el editor debe destacar las coincidencias similares a la selección.\",\n\t\t\"Mostrar siempre los controles de plegado.\",\n\t\t\"No mostrar nunca los controles de plegado y reducir el tamaño del medianil.\",\n\t\t\"Mostrar solo los controles de plegado cuando el mouse está sobre el medianil.\",\n\t\t\"Controla cuándo se muestran los controles de plegado en el medianil.\",\n\t\t\"Controla el fundido de salida del código no usado.\",\n\t\t\"Controla las variables en desuso tachadas.\",\n\t\t\"Mostrar sugerencias de fragmentos de código por encima de otras sugerencias.\",\n\t\t\"Mostrar sugerencias de fragmentos de código por debajo de otras sugerencias.\",\n\t\t\"Mostrar sugerencias de fragmentos de código con otras sugerencias.\",\n\t\t\"No mostrar sugerencias de fragmentos de código.\",\n\t\t\"Controla si se muestran los fragmentos de código con otras sugerencias y cómo se ordenan.\",\n\t\t\"Controla si el editor se desplazará con una animación.\",\n\t\t\"Controla si se debe proporcionar la sugerencia de accesibilidad a los usuarios del lector de pantalla cuando se muestra una finalización insertada.\",\n\t\t\"Tamaño de fuente del widget de sugerencias. Cuando se establece en {0}, se usa el valor de {1}.\",\n\t\t\"Alto de línea para el widget de sugerencias. Cuando se establece en {0}, se usa el valor de {1}. El valor mínimo es 8.\",\n\t\t\"Controla si deben aparecer sugerencias de forma automática al escribir caracteres desencadenadores.\",\n\t\t\"Seleccionar siempre la primera sugerencia.\",\n\t\t\"Seleccione sugerencias recientes a menos que al escribir más se seleccione una, por ejemplo, \\\"console.| -> console.log\\\" porque \\\"log\\\" se ha completado recientemente.\",\n\t\t\"Seleccione sugerencias basadas en prefijos anteriores que han completado esas sugerencias, por ejemplo, \\\"co -> console\\\" y \\\"con -> const\\\".\",\n\t\t\"Controla cómo se preseleccionan las sugerencias cuando se muestra la lista,\",\n\t\t\"La pestaña se completará insertando la mejor sugerencia de coincidencia encontrada al presionar la pestaña\",\n\t\t\"Deshabilitar los complementos para pestañas.\",\n\t\t\"La pestaña se completa con fragmentos de código cuando su prefijo coincide. Funciona mejor cuando las \\'quickSuggestions\\' no están habilitadas.\",\n\t\t\"Habilita completar pestañas.\",\n\t\t\"Los terminadores de línea no habituales se quitan automáticamente.\",\n\t\t\"Los terminadores de línea no habituales se omiten.\",\n\t\t\"Advertencia de terminadores de línea inusuales que se quitarán.\",\n\t\t\"Quite los terminadores de línea inusuales que podrían provocar problemas.\",\n\t\t\"La inserción y eliminación del espacio en blanco sigue a las tabulaciones.\",\n\t\t\"Use la regla de salto de línea predeterminada.\",\n\t\t\"Los saltos de palabra no deben usarse para texto chino, japonés o coreano (CJK). El comportamiento del texto distinto a CJK es el mismo que el normal.\",\n\t\t\"Controla las reglas de salto de palabra usadas para texto chino, japonés o coreano (CJK).\",\n\t\t\"Caracteres que se usarán como separadores de palabras al realizar operaciones o navegaciones relacionadas con palabras.\",\n\t\t\"Las líneas no se ajustarán nunca.\",\n\t\t\"Las líneas se ajustarán en el ancho de la ventanilla.\",\n\t\t\"Las líneas se ajustarán al valor de \\\"#editor.wordWrapColumn#\\\". \",\n\t\t\"Las líneas se ajustarán al valor que sea inferior: el tamaño de la ventanilla o el valor de \\\"#editor.wordWrapColumn#\\\".\",\n\t\t\"Controla cómo deben ajustarse las líneas.\",\n\t\t\"Controla la columna de ajuste del editor cuando \\\"#editor.wordWrap#\\\" es \\\"wordWrapColumn\\\" o \\\"bounded\\\".\",\n\t\t\"Controla si las decoraciones de color en línea deben mostrarse con el proveedor de colores del documento predeterminado.\",\n\t\t\"Controla si el editor recibe las pestañas o las aplaza al área de trabajo para la navegación.\",\n\t],\n\t\"vs/editor/common/core/editorColorRegistry\": [\n\t\t\"Color de fondo para la línea resaltada en la posición del cursor.\",\n\t\t\"Color de fondo del borde alrededor de la línea en la posición del cursor.\",\n\t\t\"Color de fondo de rangos resaltados, como en abrir rápido y encontrar características. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\n\t\t\"Color de fondo del borde alrededor de los intervalos resaltados.\",\n\t\t\"Color de fondo del símbolo destacado, como Ir a definición o Ir al siguiente/anterior símbolo. El color no debe ser opaco para no ocultar la decoración subyacente.\",\n\t\t\"Color de fondo del borde alrededor de los símbolos resaltados.\",\n\t\t\"Color del cursor del editor.\",\n\t\t\"Color de fondo del cursor de edición. Permite personalizar el color del caracter solapado por el bloque del cursor.\",\n\t\t\"Color de los caracteres de espacio en blanco del editor.\",\n\t\t\"Color de números de línea del editor.\",\n\t\t\"Color de las guías de sangría del editor.\",\n\t\t\"\\\"editorIndentGuide.background\\\" está en desuso. Use \\\"editorIndentGuide.background1\\\" en su lugar.\",\n\t\t\"Color de las guías de sangría activas del editor.\",\n\t\t\"\\\"editorIndentGuide.activeBackground\\\" está en desuso. Use \\\"editorIndentGuide.activeBackground1\\\" en su lugar.\",\n\t\t\"Color de las guías de sangría del editor (1).\",\n\t\t\"Color de las guías de sangría del editor (2).\",\n\t\t\"Color de las guías de sangría del editor (3).\",\n\t\t\"Color de las guías de sangría del editor (4).\",\n\t\t\"Color de las guías de sangría del editor (5).\",\n\t\t\"Color de las guías de sangría del editor (6).\",\n\t\t\"Color de las guías de sangría del editor activo (1).\",\n\t\t\"Color de las guías de sangría del editor activo (2).\",\n\t\t\"Color de las guías de sangría del editor activo (3).\",\n\t\t\"Color de las guías de sangría del editor activo (4).\",\n\t\t\"Color de las guías de sangría del editor activo (5).\",\n\t\t\"Color de las guías de sangría del editor activo (6).\",\n\t\t\"Color del número de línea activa en el editor\",\n\t\t\"ID es obsoleto. Usar en lugar \\'editorLineNumber.activeForeground\\'. \",\n\t\t\"Color del número de línea activa en el editor\",\n\t\t\"Color de la línea final del editor cuando editor.renderFinalNewline se establece en atenuado.\",\n\t\t\"Color de las reglas del editor\",\n\t\t\"Color principal de lentes de código en el editor\",\n\t\t\"Color de fondo tras corchetes coincidentes\",\n\t\t\"Color de bloques con corchetes coincidentes\",\n\t\t\"Color del borde de la regla de visión general.\",\n\t\t\"Color de fondo de la regla de información general del editor.\",\n\t\t\"Color de fondo del margen del editor. Este espacio contiene los márgenes de glifos y los números de línea.\",\n\t\t\"Color del borde de código fuente innecesario (sin usar) en el editor.\",\n\t\t\"Opacidad de código fuente innecesario (sin usar) en el editor. Por ejemplo, \\\"#000000c0\\\" representará el código con un 75 % de opacidad. Para temas de alto contraste, utilice el color del tema \\'editorUnnecessaryCode.border\\' para resaltar el código innecesario en vez de atenuarlo.\",\n\t\t\"Color del borde del texto fantasma en el editor.\",\n\t\t\"Color de primer plano del texto fantasma en el editor.\",\n\t\t\"Color de fondo del texto fantasma en el editor.\",\n\t\t\"Color de marcador de regla general para los destacados de rango. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de marcador de regla de información general para errores. \",\n\t\t\"Color de marcador de regla de información general para advertencias.\",\n\t\t\"Color de marcador de regla de información general para mensajes informativos. \",\n\t\t\"Color de primer plano de los corchetes (1). Requiere que se habilite la coloración del par de corchetes.\",\n\t\t\"Color de primer plano de los corchetes (2). Requiere que se habilite la coloración del par de corchetes.\",\n\t\t\"Color de primer plano de los corchetes (3). Requiere que se habilite la coloración del par de corchetes.\",\n\t\t\"Color de primer plano de los corchetes (4). Requiere que se habilite la coloración del par de corchetes.\",\n\t\t\"Color de primer plano de los corchetes (5). Requiere que se habilite la coloración del par de corchetes.\",\n\t\t\"Color de primer plano de los corchetes (6). Requiere que se habilite la coloración del par de corchetes.\",\n\t\t\"Color de primer plano de corchetes inesperados.\",\n\t\t\"Color de fondo de las guías de par de corchetes inactivos (1). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de par de corchetes inactivos (2). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de par de corchetes inactivos (3). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de par de corchetes inactivos (4). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de par de corchetes inactivos (5). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de par de corchetes inactivos (6). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de pares de corchetes activos (1). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de par de corchetes activos (2). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de pares de corchetes activos (3). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de par de corchetes activos (4). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de par de corchetes activos (5). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de fondo de las guías de par de corchetes activos (6). Requiere habilitar guías de par de corchetes.\",\n\t\t\"Color de borde usado para resaltar caracteres Unicode.\",\n\t\t\"Color de borde usado para resaltar caracteres unicode.\",\n\t],\n\t\"vs/editor/common/editorContextKeys\": [\n\t\t\"Si el texto del editor tiene el foco (el cursor parpadea)\",\n\t\t\"Si el editor o un widget del editor tiene el foco (por ejemplo, el foco está en el widget de búsqueda)\",\n\t\t\"Si un editor o una entrada de texto enriquecido tienen el foco (el cursor parpadea)\",\n\t\t\"Si el editor es de solo lectura\",\n\t\t\"Si el contexto es un editor de diferencias\",\n\t\t\"Si el contexto es un editor de diferencias incrustado\",\n\t\t\"Si el contexto es un editor de diferencias múltiples\",\n\t\t\"Si todos los archivos del editor de diferencias múltiples están contraídos\",\n\t\t\"Si el editor de diferencias tiene cambios\",\n\t\t\"Indica si se selecciona un bloque de código movido para la comparación\",\n\t\t\"Si el visor de diferencias accesible está visible\",\n\t\t\"Indica si se alcanza el punto de interrupción insertado en paralelo del editor de diferencias\",\n\t\t\"Si \\\"editor.columnSelection\\\" se ha habilitado\",\n\t\t\"Si el editor tiene texto seleccionado\",\n\t\t\"Si el editor tiene varias selecciones\",\n\t\t\"Si \\\"Tabulación\\\" moverá el foco fuera del editor\",\n\t\t\"Si el mantenimiento del puntero del editor es visible\",\n\t\t\"Si se centra el desplazamiento del editor\",\n\t\t\"Si el desplazamiento permanente está centrado\",\n\t\t\"Si el desplazamiento permanente está visible\",\n\t\t\"Si el selector de colores independiente está visible\",\n\t\t\"Si el selector de colores independiente está centrado\",\n\t\t\"Si el editor forma parte de otro más grande (por ejemplo, blocs de notas)\",\n\t\t\"Identificador de idioma del editor\",\n\t\t\"Si el editor tiene un proveedor de elementos de finalización\",\n\t\t\"Si el editor tiene un proveedor de acciones de código\",\n\t\t\"Si el editor tiene un proveedor de CodeLens\",\n\t\t\"Si el editor tiene un proveedor de definiciones\",\n\t\t\"Si el editor tiene un proveedor de declaraciones\",\n\t\t\"Si el editor tiene un proveedor de implementación\",\n\t\t\"Si el editor tiene un proveedor de definiciones de tipo\",\n\t\t\"Si el editor tiene un proveedor de contenido con mantenimiento del puntero\",\n\t\t\"Si el editor tiene un proveedor de resaltado de documentos\",\n\t\t\"Si el editor tiene un proveedor de símbolos de documentos\",\n\t\t\"Si el editor tiene un proveedor de referencia\",\n\t\t\"Si el editor tiene un proveedor de cambio de nombre\",\n\t\t\"Si el editor tiene un proveedor de ayuda de signatura\",\n\t\t\"Si el editor tiene un proveedor de sugerencias insertadas\",\n\t\t\"Si el editor tiene un proveedor de formatos de documento\",\n\t\t\"Si el editor tiene un proveedor de formatos de selección de documentos\",\n\t\t\"Si el editor tiene varios proveedores de formatos del documento\",\n\t\t\"Si el editor tiene varios proveedores de formato de la selección de documentos\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"matriz\",\n\t\t\"booleano\",\n\t\t\"clase\",\n\t\t\"constante\",\n\t\t\"constructor\",\n\t\t\"enumeración\",\n\t\t\"miembro de la enumeración\",\n\t\t\"evento\",\n\t\t\"campo\",\n\t\t\"archivo\",\n\t\t\"función\",\n\t\t\"interfaz\",\n\t\t\"clave\",\n\t\t\"método\",\n\t\t\"módulo\",\n\t\t\"espacio de nombres\",\n\t\t\"NULL\",\n\t\t\"número\",\n\t\t\"objeto\",\n\t\t\"operador\",\n\t\t\"paquete\",\n\t\t\"propiedad\",\n\t\t\"cadena\",\n\t\t\"estructura\",\n\t\t\"parámetro de tipo\",\n\t\t\"variable\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/common/languages/modesRegistry\": [\n\t\t\"Texto sin formato\",\n\t],\n\t\"vs/editor/common/model/editStack\": [\n\t\t\"Escribiendo\",\n\t],\n\t\"vs/editor/common/standaloneStrings\": [\n\t\t\"Desarrollador: inspeccionar tokens\",\n\t\t\"Vaya a Línea/Columna...\",\n\t\t\"Mostrar todos los proveedores de acceso rápido\",\n\t\t\"Paleta de comandos\",\n\t\t\"Mostrar y ejecutar comandos\",\n\t\t\"Ir a símbolo...\",\n\t\t\"Ir a símbolo por categoría...\",\n\t\t\"Contenido del editor\",\n\t\t\"Presione Alt+F1 para ver las opciones de accesibilidad.\",\n\t\t\"Alternar tema de contraste alto\",\n\t\t\"{0} ediciones realizadas en {1} archivos\",\n\t],\n\t\"vs/editor/common/viewLayout/viewLineRenderer\": [\n\t\t\"Mostrar más ({0})\",\n\t\t\"{0} caracteres\",\n\t],\n\t\"vs/editor/contrib/anchorSelect/browser/anchorSelect\": [\n\t\t\"Delimitador de la selección\",\n\t\t\"Delimitador establecido en {0}:{1}\",\n\t\t\"Establecer el delimitador de la selección\",\n\t\t\"Ir al delimitador de la selección\",\n\t\t\"Seleccionar desde el delimitador hasta el cursor\",\n\t\t\"Cancelar el delimitador de la selección\",\n\t],\n\t\"vs/editor/contrib/bracketMatching/browser/bracketMatching\": [\n\t\t\"Resumen color de marcador de regla para corchetes.\",\n\t\t\"Ir al corchete\",\n\t\t\"Seleccionar para corchete\",\n\t\t\"Quitar corchetes\",\n\t\t\"Ir al &&corchete\",\n\t\t\"Se selecciona el texto que está dentro, incluyendo los corchetes o las llaves\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/caretOperations\": [\n\t\t\"Mover el texto seleccionado a la izquierda\",\n\t\t\"Mover el texto seleccionado a la derecha\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/transpose\": [\n\t\t\"Transponer letras\",\n\t],\n\t\"vs/editor/contrib/clipboard/browser/clipboard\": [\n\t\t\"Cor&&tar\",\n\t\t\"Cortar\",\n\t\t\"Cortar\",\n\t\t\"Cortar\",\n\t\t\"&&Copiar\",\n\t\t\"Copiar\",\n\t\t\"Copiar\",\n\t\t\"Copiar\",\n\t\t\"Copiar como\",\n\t\t\"Copiar como\",\n\t\t\"Compartir\",\n\t\t\"Compartir\",\n\t\t\"Compartir\",\n\t\t\"&&Pegar\",\n\t\t\"Pegar\",\n\t\t\"Pegar\",\n\t\t\"Pegar\",\n\t\t\"Copiar con resaltado de sintaxis\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeAction\": [\n\t\t\"Se ha producido un error desconocido al aplicar la acción de código\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionCommands\": [\n\t\t\"Tipo de la acción de código que se va a ejecutar.\",\n\t\t\"Controla cuándo se aplican las acciones devueltas.\",\n\t\t\"Aplicar siempre la primera acción de código devuelto.\",\n\t\t\"Aplicar la primera acción de código devuelta si solo hay una.\",\n\t\t\"No aplique las acciones de código devuelto.\",\n\t\t\"Controla si solo se deben devolver las acciones de código preferidas.\",\n\t\t\"Corrección Rápida\",\n\t\t\"No hay acciones de código disponibles\",\n\t\t\"No hay acciones de código preferidas para \\\"{0}\\\" disponibles\",\n\t\t\"No hay ninguna acción de código para \\\"{0}\\\" disponible.\",\n\t\t\"No hay acciones de código preferidas disponibles\",\n\t\t\"No hay acciones de código disponibles\",\n\t\t\"Refactorizar...\",\n\t\t\"No hay refactorizaciones preferidas de \\\"{0}\\\" disponibles\",\n\t\t\"No hay refactorizaciones de \\\"{0}\\\" disponibles\",\n\t\t\"No hay ninguna refactorización favorita disponible.\",\n\t\t\"No hay refactorizaciones disponibles\",\n\t\t\"Acción de código fuente...\",\n\t\t\"No hay acciones de origen preferidas para \\\"{0}\\\" disponibles\",\n\t\t\"No hay ninguna acción de código fuente para \\\"{0}\\\" disponible.\",\n\t\t\"No hay ninguna acción de código fuente favorita disponible.\",\n\t\t\"No hay acciones de origen disponibles\",\n\t\t\"Organizar Importaciones\",\n\t\t\"No hay acciones de importación disponibles\",\n\t\t\"Corregir todo\",\n\t\t\"No está disponible la acción de corregir todo\",\n\t\t\"Corregir automáticamente...\",\n\t\t\"No hay autocorrecciones disponibles\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionContributions\": [\n\t\t\"Activar/desactivar la visualización de los encabezados de los grupos en el menú de Acción de código.\",\n\t\t\"Habilita o deshabilita la visualización de la corrección rápida más cercana dentro de una línea cuando no está actualmente en un diagnóstico.\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionController\": [\n\t\t\"Contexto: {0} en la línea {1} y columna {2}.\",\n\t\t\"Ocultar deshabilitado\",\n\t\t\"Mostrar elementos deshabilitados\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionMenu\": [\n\t\t\"Más Acciones...\",\n\t\t\"Corrección rápida\",\n\t\t\"Extraer\",\n\t\t\"Insertado\",\n\t\t\"Reescribir\",\n\t\t\"Mover\",\n\t\t\"Delimitar con\",\n\t\t\"Acción de origen\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/lightBulbWidget\": [\n\t\t\"Mostrar acciones de código. Corrección rápida preferida disponible ({0})\",\n\t\t\"Mostrar acciones de código ({0})\",\n\t\t\"Mostrar acciones de código\",\n\t\t\"Iniciar chat en línea ({0})\",\n\t\t\"Iniciar chat en línea\",\n\t\t\"Desencadenar acción de IA\",\n\t],\n\t\"vs/editor/contrib/codelens/browser/codelensController\": [\n\t\t\"Mostrar comandos de lente de código para la línea actual\",\n\t\t\"Seleccionar un comando\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/colorPickerWidget\": [\n\t\t\"Haga clic para alternar las opciones de color (rgb/hsl/hex)\",\n\t\t\"Icono para cerrar el selector de colores\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions\": [\n\t\t\"Mostrar o centrar Selector de colores independientes\",\n\t\t\"&Mostrar o centrar Selector de colores independientes\",\n\t\t\"Ocultar la Selector de colores\",\n\t\t\"Insertar color con Selector de colores independiente\",\n\t],\n\t\"vs/editor/contrib/comment/browser/comment\": [\n\t\t\"Alternar comentario de línea\",\n\t\t\"&&Alternar comentario de línea\",\n\t\t\"Agregar comentario de línea\",\n\t\t\"Quitar comentario de línea\",\n\t\t\"Alternar comentario de bloque\",\n\t\t\"Alternar &&bloque de comentario\",\n\t],\n\t\"vs/editor/contrib/contextmenu/browser/contextmenu\": [\n\t\t\"Minimapa\",\n\t\t\"Representar caracteres\",\n\t\t\"Tamaño vertical\",\n\t\t\"Proporcional\",\n\t\t\"Relleno\",\n\t\t\"Ajustar\",\n\t\t\"Control deslizante\",\n\t\t\"Pasar el mouse\",\n\t\t\"Siempre\",\n\t\t\"Mostrar menú contextual del editor\",\n\t],\n\t\"vs/editor/contrib/cursorUndo/browser/cursorUndo\": [\n\t\t\"Cursor Deshacer\",\n\t\t\"Cursor Rehacer\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution\": [\n\t\t\"Pegar como...\",\n\t\t\"Id. de la edición pegada que se intenta aplicar. Si no se proporciona, el editor mostrará un selector.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController\": [\n\t\t\"Si se muestra el widget de pegado\",\n\t\t\"Mostrar opciones de pegado...\",\n\t\t\"Ejecutando controladores de pegado. Haga clic para cancelar.\",\n\t\t\"Seleccionar acción pegar\",\n\t\t\"Ejecutando controladores de pegado\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders\": [\n\t\t\"Integrado\",\n\t\t\"Insertar texto sin formato\",\n\t\t\"Insertar URIs\",\n\t\t\"Insertar URI\",\n\t\t\"Insertar rutas de acceso\",\n\t\t\"Insertar ruta de acceso\",\n\t\t\"Insertar rutas de acceso relativas\",\n\t\t\"Insertar ruta de acceso relativa\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution\": [\n\t\t\"Configura el proveedor de colocación predeterminado que se usará para el contenido de un tipo MIME determinado.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController\": [\n\t\t\"Si se muestra el widget de colocación\",\n\t\t\"Mostrar opciones de colocación...\",\n\t\t\"Ejecutando controladores de colocación. Haga clic para cancelar.\",\n\t],\n\t\"vs/editor/contrib/editorState/browser/keybindingCancellation\": [\n\t\t\"Indica si el editor ejecuta una operación que se puede cancelar como, por ejemplo, \\\"Inspeccionar referencias\\\"\",\n\t],\n\t\"vs/editor/contrib/find/browser/findController\": [\n\t\t\"El archivo es demasiado grande para realizar una operación de reemplazar todo.\",\n\t\t\"Buscar\",\n\t\t\"&&Buscar\",\n\t\t\"Invalida la marca \\\"Usar expresión regular\\\".\\r\\nLa marca no se guardará para el futuro.\\r\\n0: No hacer nada\\r\\n1: True\\r\\n2: False\",\n\t\t\"Invalida la marca \\\"Hacer coincidir palabra completa”.\\r\\nLa marca no se guardará para el futuro.\\r\\n0: No hacer nada\\r\\n1: True\\r\\n2: False\",\n\t\t\"Invalida la marca \\\"Caso matemático\\\".\\r\\nLa marca no se guardará para el futuro.\\r\\n0: No hacer nada\\r\\n1: True\\r\\n2: False\",\n\t\t\"Invalida la marca \\\"Conservar mayúsculas y minúsculas.\\r\\nLa marca no se guardará para el futuro.\\r\\n0: No hacer nada\\r\\n1: True\\r\\n2: False\",\n\t\t\"Búsqueda con argumentos\",\n\t\t\"Buscar con selección\",\n\t\t\"Buscar siguiente\",\n\t\t\"Buscar anterior\",\n\t\t\"Ir a Coincidencia...\",\n\t\t\"No hay coincidencias. Intente buscar otra cosa.\",\n\t\t\"Escriba un número para ir a una coincidencia específica (entre 1 y {0})\",\n\t\t\"Escriba un número entre 1 y {0}\",\n\t\t\"Escriba un número entre 1 y {0}\",\n\t\t\"Buscar selección siguiente\",\n\t\t\"Buscar selección anterior\",\n\t\t\"Reemplazar\",\n\t\t\"&&Reemplazar\",\n\t],\n\t\"vs/editor/contrib/find/browser/findWidget\": [\n\t\t\"Icono para \\\"Buscar en selección\\\" en el widget de búsqueda del editor.\",\n\t\t\"Icono para indicar que el widget de búsqueda del editor está contraído.\",\n\t\t\"Icono para indicar que el widget de búsqueda del editor está expandido.\",\n\t\t\"Icono para \\\"Reemplazar\\\" en el widget de búsqueda del editor.\",\n\t\t\"Icono para \\\"Reemplazar todo\\\" en el widget de búsqueda del editor.\",\n\t\t\"Icono para \\\"Buscar anterior\\\" en el widget de búsqueda del editor.\",\n\t\t\"Icono para \\\"Buscar siguiente\\\" en el widget de búsqueda del editor.\",\n\t\t\"Buscar y reemplazar\",\n\t\t\"Buscar\",\n\t\t\"Buscar\",\n\t\t\"Coincidencia anterior\",\n\t\t\"Coincidencia siguiente\",\n\t\t\"Buscar en selección\",\n\t\t\"Cerrar\",\n\t\t\"Reemplazar\",\n\t\t\"Reemplazar\",\n\t\t\"Reemplazar\",\n\t\t\"Reemplazar todo\",\n\t\t\"Alternar reemplazar\",\n\t\t\"Sólo los primeros {0} resultados son resaltados, pero todas las operaciones de búsqueda trabajan en todo el texto.\",\n\t\t\"{0} de {1}\",\n\t\t\"No hay resultados\",\n\t\t\"Encontrados: {0}\",\n\t\t\"{0} encontrado para \\\"{1}\\\"\",\n\t\t\"{0} encontrado para \\\"{1}\\\", en {2}\",\n\t\t\"{0} encontrado para \\\"{1}\\\"\",\n\t\t\"Ctrl+Entrar ahora inserta un salto de línea en lugar de reemplazar todo. Puede modificar el enlace de claves para editor.action.replaceAll para invalidar este comportamiento.\",\n\t],\n\t\"vs/editor/contrib/folding/browser/folding\": [\n\t\t\"Desplegar\",\n\t\t\"Desplegar de forma recursiva\",\n\t\t\"Plegar\",\n\t\t\"Alternar plegado\",\n\t\t\"Plegar de forma recursiva\",\n\t\t\"Cerrar todos los comentarios de bloque\",\n\t\t\"Plegar todas las regiones\",\n\t\t\"Desplegar Todas las Regiones\",\n\t\t\"Plegar todas excepto las seleccionadas\",\n\t\t\"Desplegar todas excepto las seleccionadas\",\n\t\t\"Plegar todo\",\n\t\t\"Desplegar todo\",\n\t\t\"Ir al plegado primario\",\n\t\t\"Ir al rango de plegado anterior\",\n\t\t\"Ir al rango de plegado siguiente\",\n\t\t\"Crear rango de plegado a partir de la selección\",\n\t\t\"Quitar rangos de plegado manuales\",\n\t\t\"Nivel de plegamiento {0}\",\n\t],\n\t\"vs/editor/contrib/folding/browser/foldingDecorations\": [\n\t\t\"Color de fondo detrás de los rangos plegados. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color del control plegable en el medianil del editor.\",\n\t\t\"Icono de rangos expandidos en el margen de glifo del editor.\",\n\t\t\"Icono de rangos contraídos en el margen de glifo del editor.\",\n\t\t\"Icono de intervalos contraídos manualmente en el margen del glifo del editor.\",\n\t\t\"Icono de intervalos expandidos manualmente en el margen del glifo del editor.\",\n\t],\n\t\"vs/editor/contrib/fontZoom/browser/fontZoom\": [\n\t\t\"Acercarse a la tipografía del editor\",\n\t\t\"Alejarse de la tipografía del editor\",\n\t\t\"Restablecer alejamiento de la tipografía del editor\",\n\t],\n\t\"vs/editor/contrib/format/browser/formatActions\": [\n\t\t\"Dar formato al documento\",\n\t\t\"Dar formato a la selección\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoError\": [\n\t\t\"Ir al siguiente problema (Error, Advertencia, Información)\",\n\t\t\"Icono para ir al marcador siguiente.\",\n\t\t\"Ir al problema anterior (Error, Advertencia, Información)\",\n\t\t\"Icono para ir al marcador anterior.\",\n\t\t\"Ir al siguiente problema en Archivos (Error, Advertencia, Información)\",\n\t\t\"Siguiente &&problema\",\n\t\t\"Ir al problema anterior en Archivos (Error, Advertencia, Información)\",\n\t\t\"Anterior &&problema\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoErrorWidget\": [\n\t\t\"Error\",\n\t\t\"Advertencia\",\n\t\t\"Información\",\n\t\t\"Sugerencia\",\n\t\t\"{0} en {1}. \",\n\t\t\"{0} de {1} problemas\",\n\t\t\"{0} de {1} problema\",\n\t\t\"Color de los errores del widget de navegación de marcadores del editor.\",\n\t\t\"Fondo del encabezado del error del widget de navegación del marcador de editor.\",\n\t\t\"Color de las advertencias del widget de navegación de marcadores del editor.\",\n\t\t\"Fondo del encabezado de la advertencia del widget de navegación del marcador de editor.\",\n\t\t\"Color del widget informativo marcador de navegación en el editor.\",\n\t\t\"Fondo del encabezado de información del widget de navegación del marcador de editor.\",\n\t\t\"Fondo del widget de navegación de marcadores del editor.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/goToCommands\": [\n\t\t\"Ver\",\n\t\t\"Definiciones\",\n\t\t\"No se encontró ninguna definición para \\\"{0}\\\"\",\n\t\t\"No se encontró ninguna definición\",\n\t\t\"Ir a definición\",\n\t\t\"Ir a &&definición\",\n\t\t\"Abrir definición en el lateral\",\n\t\t\"Ver la definición sin salir\",\n\t\t\"Declaraciones\",\n\t\t\"No se encontró ninguna definición para \\'{0}\\'\",\n\t\t\"No se encontró ninguna declaración\",\n\t\t\"Ir a Definición\",\n\t\t\"Ir a &&declaración\",\n\t\t\"No se encontró ninguna definición para \\'{0}\\'\",\n\t\t\"No se encontró ninguna declaración\",\n\t\t\"Inspeccionar Definición\",\n\t\t\"Definiciones de tipo\",\n\t\t\"No se encontró ninguna definición de tipo para \\\"{0}\\\"\",\n\t\t\"No se encontró ninguna definición de tipo\",\n\t\t\"Ir a la definición de tipo\",\n\t\t\"Ir a la definición de &&tipo\",\n\t\t\"Inspeccionar definición de tipo\",\n\t\t\"Implementaciones\",\n\t\t\"No se encontró ninguna implementación para \\\"{0}\\\"\",\n\t\t\"No se encontró ninguna implementación\",\n\t\t\"Ir a Implementaciones\",\n\t\t\"Ir a &&implementaciones\",\n\t\t\"Inspeccionar implementaciones\",\n\t\t\"No se ha encontrado ninguna referencia para \\\"{0}\\\".\",\n\t\t\"No se encontraron referencias\",\n\t\t\"Ir a Referencias\",\n\t\t\"Ir a &&referencias\",\n\t\t\"Referencias\",\n\t\t\"Inspeccionar Referencias\",\n\t\t\"Referencias\",\n\t\t\"Ir a cualquier símbolo\",\n\t\t\"Ubicaciones\",\n\t\t\"No hay resultados para \\\"{0}\\\"\",\n\t\t\"Referencias\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition\": [\n\t\t\"Haga clic para mostrar {0} definiciones.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesController\": [\n\t\t\"Indica si está visible la inspección de referencias, como \\\"Inspección de referencias\\\" o \\\"Ver la definición sin salir\\\".\",\n\t\t\"Cargando...\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree\": [\n\t\t\"{0} referencias\",\n\t\t\"{0} referencia\",\n\t\t\"Referencias\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget\": [\n\t\t\"vista previa no disponible\",\n\t\t\"No hay resultados\",\n\t\t\"Referencias\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/referencesModel\": [\n\t\t\"en {0} en la línea {1} en la columna {2}\",\n\t\t\"{0} en {1} en la línea {2} en la columna {3}\",\n\t\t\"1 símbolo en {0}, ruta de acceso completa {1}\",\n\t\t\"{0} símbolos en {1}, ruta de acceso completa {2}\",\n\t\t\"No se encontraron resultados\",\n\t\t\"Encontró 1 símbolo en {0}\",\n\t\t\"Encontró {0} símbolos en {1}\",\n\t\t\"Encontró {0} símbolos en {1} archivos\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/symbolNavigation\": [\n\t\t\"Indica si hay ubicaciones de símbolos a las que se pueda navegar solo con el teclado.\",\n\t\t\"Símbolo {0} de {1}, {2} para el siguiente\",\n\t\t\"Símbolo {0} de {1}\",\n\t],\n\t\"vs/editor/contrib/hover/browser/hover\": [\n\t\t\"Mostrar o centrarse al mantener el puntero\",\n\t\t\"El cuadro del elemento sobre el que se ha pasado el ratón se enfocará automáticamente.\",\n\t\t\"El cuadro del elemento sobre el que se ha pasado el ratón se enfocará solo si ya está visible.\",\n\t\t\"Se enfocará el cuadro que aparece cuando se pasa el ratón por encima de un elemento.\",\n\t\t\"Mostrar vista previa de la definición que aparece al mover el puntero\",\n\t\t\"Desplazar hacia arriba al mantener el puntero\",\n\t\t\"Desplazar hacia abajo al mantener el puntero\",\n\t\t\"Desplazar al mantener el puntero a la izquierda\",\n\t\t\"Desplazar al mantener el puntero a la derecha\",\n\t\t\"Desplazamiento de página hacia arriba\",\n\t\t\"Desplazamiento de página hacia abajo\",\n\t\t\"Ir al puntero superior\",\n\t\t\"Ir a la parte inferior al mantener el puntero\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markdownHoverParticipant\": [\n\t\t\"Cargando...\",\n\t\t\"Representación en pausa durante una línea larga por motivos de rendimiento. Esto se puede configurar mediante \\\"editor.stopRenderingLineAfter\\\".\",\n\t\t\"Por motivos de rendimiento, la tokenización se omite con filas largas. Esta opción se puede configurar con \\\"editor.maxTokenizationLineLength\\\".\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markerHoverParticipant\": [\n\t\t\"Ver el problema\",\n\t\t\"No hay correcciones rápidas disponibles\",\n\t\t\"Buscando correcciones rápidas...\",\n\t\t\"No hay correcciones rápidas disponibles\",\n\t\t\"Corrección Rápida\",\n\t],\n\t\"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace\": [\n\t\t\"Reemplazar con el valor anterior\",\n\t\t\"Reemplazar con el valor siguiente\",\n\t],\n\t\"vs/editor/contrib/indentation/browser/indentation\": [\n\t\t\"Convertir sangría en espacios\",\n\t\t\"Convertir sangría en tabulaciones\",\n\t\t\"Tamaño de tabulación configurado\",\n\t\t\"Tamaño de tabulación predeterminado\",\n\t\t\"Tamaño de tabulación actual\",\n\t\t\"Seleccionar tamaño de tabulación para el archivo actual\",\n\t\t\"Aplicar sangría con tabulaciones\",\n\t\t\"Aplicar sangría con espacios\",\n\t\t\"Cambiar tamaño de visualización de tabulación\",\n\t\t\"Detectar sangría del contenido\",\n\t\t\"Volver a aplicar sangría a líneas\",\n\t\t\"Volver a aplicar sangría a líneas seleccionadas\",\n\t],\n\t\"vs/editor/contrib/inlayHints/browser/inlayHintsHover\": [\n\t\t\"Haga doble clic para insertar\",\n\t\t\"cmd + clic\",\n\t\t\"ctrl + clic\",\n\t\t\"opción + clic\",\n\t\t\"alt + clic\",\n\t\t\"Ir a Definición ({0}), haga clic con el botón derecho para obtener más información\",\n\t\t\"Ir a Definición ({0})\",\n\t\t\"Ejecutar comando\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/commands\": [\n\t\t\"Mostrar sugerencia alineada siguiente\",\n\t\t\"Mostrar sugerencia alineada anterior\",\n\t\t\"Desencadenar sugerencia alineada\",\n\t\t\"Aceptar la siguiente palabra de sugerencia insertada\",\n\t\t\"Aceptar palabra\",\n\t\t\"Aceptar la siguiente línea de sugerencia insertada\",\n\t\t\"Aceptar línea\",\n\t\t\"Aceptar la sugerencia insertada\",\n\t\t\"Aceptar\",\n\t\t\"Ocultar la sugerencia insertada\",\n\t\t\"Mostrar siempre la barra de herramientas\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/hoverParticipant\": [\n\t\t\"Sugerencia:\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys\": [\n\t\t\"Si una sugerencia alineada está visible\",\n\t\t\"Si la sugerencia alineada comienza con un espacio en blanco\",\n\t\t\"Si la sugerencia insertada comienza con un espacio en blanco menor que lo que se insertaría mediante tabulación\",\n\t\t\"Si las sugerencias deben suprimirse para la sugerencia actual\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController\": [\n\t\t\"Inspeccionar esto en la vista accesible ({0})\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget\": [\n\t\t\"Icono para mostrar la sugerencia de parámetro siguiente.\",\n\t\t\"Icono para mostrar la sugerencia de parámetro anterior.\",\n\t\t\"{0} ({1})\",\n\t\t\"Anterior\",\n\t\t\"Siguiente\",\n\t],\n\t\"vs/editor/contrib/lineSelection/browser/lineSelection\": [\n\t\t\"Expandir selección de línea\",\n\t],\n\t\"vs/editor/contrib/linesOperations/browser/linesOperations\": [\n\t\t\"Copiar línea arriba\",\n\t\t\"&&Copiar línea arriba\",\n\t\t\"Copiar línea abajo\",\n\t\t\"Co&&piar línea abajo\",\n\t\t\"Selección duplicada\",\n\t\t\"&&Duplicar selección\",\n\t\t\"Mover línea hacia arriba\",\n\t\t\"Mo&&ver línea arriba\",\n\t\t\"Mover línea hacia abajo\",\n\t\t\"Mover &&línea abajo\",\n\t\t\"Ordenar líneas en orden ascendente\",\n\t\t\"Ordenar líneas en orden descendente\",\n\t\t\"Eliminar líneas duplicadas\",\n\t\t\"Recortar espacio final\",\n\t\t\"Eliminar línea\",\n\t\t\"Sangría de línea\",\n\t\t\"Anular sangría de línea\",\n\t\t\"Insertar línea arriba\",\n\t\t\"Insertar línea debajo\",\n\t\t\"Eliminar todo a la izquierda\",\n\t\t\"Eliminar todo lo que está a la derecha\",\n\t\t\"Unir líneas\",\n\t\t\"Transponer caracteres alrededor del cursor\",\n\t\t\"Transformar a mayúsculas\",\n\t\t\"Transformar a minúsculas\",\n\t\t\"Transformar en Title Case\",\n\t\t\"Transformar en Snake Case\",\n\t\t\"Transformar a mayúsculas y minúsculas Camel\",\n\t\t\"Transformar en caso Kebab\",\n\t],\n\t\"vs/editor/contrib/linkedEditing/browser/linkedEditing\": [\n\t\t\"Iniciar edición vinculada\",\n\t\t\"Color de fondo cuando el editor cambia el nombre automáticamente al escribir.\",\n\t],\n\t\"vs/editor/contrib/links/browser/links\": [\n\t\t\"No se pudo abrir este vínculo porque no tiene un formato correcto: {0}\",\n\t\t\"No se pudo abrir este vínculo porque falta el destino.\",\n\t\t\"Ejecutar comando\",\n\t\t\"Seguir vínculo\",\n\t\t\"cmd + clic\",\n\t\t\"ctrl + clic\",\n\t\t\"opción + clic\",\n\t\t\"alt + clic\",\n\t\t\"Ejecutar el comando {0}\",\n\t\t\"Abrir vínculo\",\n\t],\n\t\"vs/editor/contrib/message/browser/messageController\": [\n\t\t\"Indica si el editor muestra actualmente un mensaje insertado\",\n\t],\n\t\"vs/editor/contrib/multicursor/browser/multicursor\": [\n\t\t\"Cursor agregado: {0}\",\n\t\t\"Cursores agregados: {0}\",\n\t\t\"Agregar cursor arriba\",\n\t\t\"&&Agregar cursor arriba\",\n\t\t\"Agregar cursor debajo\",\n\t\t\"A&&gregar cursor abajo\",\n\t\t\"Añadir cursores a finales de línea\",\n\t\t\"Agregar c&&ursores a extremos de línea\",\n\t\t\"Añadir cursores a la parte inferior\",\n\t\t\"Añadir cursores a la parte superior\",\n\t\t\"Agregar selección hasta la siguiente coincidencia de búsqueda\",\n\t\t\"Agregar &&siguiente repetición\",\n\t\t\"Agregar selección hasta la anterior coincidencia de búsqueda\",\n\t\t\"Agregar r&&epetición anterior\",\n\t\t\"Mover última selección hasta la siguiente coincidencia de búsqueda\",\n\t\t\"Mover última selección hasta la anterior coincidencia de búsqueda\",\n\t\t\"Seleccionar todas las repeticiones de coincidencia de búsqueda\",\n\t\t\"Seleccionar todas las &&repeticiones\",\n\t\t\"Cambiar todas las ocurrencias\",\n\t\t\"Enfocar el siguiente cursor\",\n\t\t\"Centra el cursor siguiente\",\n\t\t\"Enfocar cursor anterior\",\n\t\t\"Centra el cursor anterior\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHints\": [\n\t\t\"Sugerencias para parámetros Trigger\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHintsWidget\": [\n\t\t\"Icono para mostrar la sugerencia de parámetro siguiente.\",\n\t\t\"Icono para mostrar la sugerencia de parámetro anterior.\",\n\t\t\"{0}, sugerencia\",\n\t\t\"Color de primer plano del elemento activo en la sugerencia de parámetro.\",\n\t],\n\t\"vs/editor/contrib/peekView/browser/peekView\": [\n\t\t\"Indica si el editor de código actual está incrustado en la inspección.\",\n\t\t\"Cerrar\",\n\t\t\"Color de fondo del área de título de la vista de inspección.\",\n\t\t\"Color del título de la vista de inpección.\",\n\t\t\"Color de la información del título de la vista de inspección.\",\n\t\t\"Color de los bordes y la flecha de la vista de inspección.\",\n\t\t\"Color de fondo de la lista de resultados de vista de inspección.\",\n\t\t\"Color de primer plano de los nodos de inspección en la lista de resultados.\",\n\t\t\"Color de primer plano de los archivos de inspección en la lista de resultados.\",\n\t\t\"Color de fondo de la entrada seleccionada en la lista de resultados de vista de inspección.\",\n\t\t\"Color de primer plano de la entrada seleccionada en la lista de resultados de vista de inspección.\",\n\t\t\"Color de fondo del editor de vista de inspección.\",\n\t\t\"Color de fondo del margen en el editor de vista de inspección.\",\n\t\t\"Color de fondo del desplazamiento permanente en el editor de vista de inspección.\",\n\t\t\"Buscar coincidencia con el color de resaltado de la lista de resultados de vista de inspección.\",\n\t\t\"Buscar coincidencia del color de resultado del editor de vista de inspección.\",\n\t\t\"Hacer coincidir el borde resaltado en el editor de vista previa.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess\": [\n\t\t\"Abra primero un editor de texto para ir a una línea.\",\n\t\t\"Vaya a la línea {0} y al carácter {1}.\",\n\t\t\"Ir a la línea {0}.\",\n\t\t\"Línea actual: {0}, Carácter: {1}. Escriba un número de línea entre 1 y {2} a los que navegar.\",\n\t\t\"Línea actual: {0}, Carácter: {1}. Escriba un número de línea al que navegar.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess\": [\n\t\t\"Para ir a un símbolo, primero abra un editor de texto con información de símbolo.\",\n\t\t\"El editor de texto activo no proporciona información de símbolos.\",\n\t\t\"No hay ningún símbolo del editor coincidente.\",\n\t\t\"No hay símbolos del editor.\",\n\t\t\"Abrir en el lateral\",\n\t\t\"Abrir en la parte inferior\",\n\t\t\"símbolos ({0})\",\n\t\t\"propiedades ({0})\",\n\t\t\"métodos ({0})\",\n\t\t\"funciones ({0})\",\n\t\t\"constructores ({0})\",\n\t\t\"variables ({0})\",\n\t\t\"clases ({0})\",\n\t\t\"estructuras ({0})\",\n\t\t\"eventos ({0})\",\n\t\t\"operadores ({0})\",\n\t\t\"interfaces ({0})\",\n\t\t\"espacios de nombres ({0})\",\n\t\t\"paquetes ({0})\",\n\t\t\"parámetros de tipo ({0})\",\n\t\t\"módulos ({0})\",\n\t\t\"propiedades ({0})\",\n\t\t\"enumeraciones ({0})\",\n\t\t\"miembros de enumeración ({0})\",\n\t\t\"cadenas ({0})\",\n\t\t\"archivos ({0})\",\n\t\t\"matrices ({0})\",\n\t\t\"números ({0})\",\n\t\t\"booleanos ({0})\",\n\t\t\"objetos ({0})\",\n\t\t\"claves ({0})\",\n\t\t\"campos ({0})\",\n\t\t\"constantes ({0})\",\n\t],\n\t\"vs/editor/contrib/readOnlyMessage/browser/contribution\": [\n\t\t\"No se puede editar en la entrada de solo lectura\",\n\t\t\"No se puede editar en un editor de sólo lectura\",\n\t],\n\t\"vs/editor/contrib/rename/browser/rename\": [\n\t\t\"No hay ningún resultado.\",\n\t\t\"Error desconocido al resolver el cambio de nombre de la ubicación\",\n\t\t\"Cambiando el nombre de \\'{0}\\' a \\'{1}\\'\",\n\t\t\"Cambiar el nombre de {0} a {1}\",\n\t\t\"Nombre cambiado correctamente de \\'{0}\\' a \\'{1}\\'. Resumen: {2}\",\n\t\t\"No se pudo cambiar el nombre a las ediciones de aplicación\",\n\t\t\"No se pudo cambiar el nombre de las ediciones de cálculo\",\n\t\t\"Cambiar el nombre del símbolo\",\n\t\t\"Activar/desactivar la capacidad de previsualizar los cambios antes de cambiar el nombre\",\n\t],\n\t\"vs/editor/contrib/rename/browser/renameInputField\": [\n\t\t\"Indica si el widget de cambio de nombre de entrada está visible.\",\n\t\t\"Cambie el nombre de la entrada. Escriba el nuevo nombre y presione Entrar para confirmar.\",\n\t\t\"{0} para cambiar de nombre, {1} para obtener una vista previa\",\n\t],\n\t\"vs/editor/contrib/smartSelect/browser/smartSelect\": [\n\t\t\"Expandir selección\",\n\t\t\"&&Expandir selección\",\n\t\t\"Reducir la selección\",\n\t\t\"&&Reducir selección\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetController2\": [\n\t\t\"Indica si el editor actual está en modo de fragmentos de código.\",\n\t\t\"Indica si hay una tabulación siguiente cuando se está en modo de fragmentos de código.\",\n\t\t\"Si hay una tabulación anterior cuando se está en modo de fragmentos de código.\",\n\t\t\"Ir al marcador de posición siguiente...\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetVariables\": [\n\t\t\"Domingo\",\n\t\t\"Lunes\",\n\t\t\"Martes\",\n\t\t\"Miércoles\",\n\t\t\"Jueves\",\n\t\t\"Viernes\",\n\t\t\"Sábado\",\n\t\t\"Dom\",\n\t\t\"Lun\",\n\t\t\"Mar\",\n\t\t\"Mié\",\n\t\t\"Jue\",\n\t\t\"Vie\",\n\t\t\"Sáb\",\n\t\t\"Enero\",\n\t\t\"Febrero\",\n\t\t\"Marzo\",\n\t\t\"Abril\",\n\t\t\"May\",\n\t\t\"Junio\",\n\t\t\"Julio\",\n\t\t\"Agosto\",\n\t\t\"Septiembre\",\n\t\t\"Octubre\",\n\t\t\"Noviembre\",\n\t\t\"Diciembre\",\n\t\t\"Ene\",\n\t\t\"Feb\",\n\t\t\"Mar\",\n\t\t\"Abr\",\n\t\t\"May\",\n\t\t\"Jun\",\n\t\t\"Jul\",\n\t\t\"Ago\",\n\t\t\"Sep\",\n\t\t\"Oct\",\n\t\t\"Nov\",\n\t\t\"Dic\",\n\t],\n\t\"vs/editor/contrib/stickyScroll/browser/stickyScrollActions\": [\n\t\t\"Alternar desplazamiento permanente\",\n\t\t\"&&Alternar desplazamiento permanente\",\n\t\t\"Desplazamiento permanente\",\n\t\t\"&&Desplazamiento permanente\",\n\t\t\"Desplazamiento permanente de foco\",\n\t\t\"&&Desplazamiento permanente de foco\",\n\t\t\"Seleccionar la siguiente línea de desplazamiento rápida\",\n\t\t\"Seleccionar la línea de desplazamiento rápida anterior\",\n\t\t\"Ir a la línea de desplazamiento rápida con foco\",\n\t\t\"Seleccionar el Editor\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggest\": [\n\t\t\"Si alguna sugerencia tiene el foco\",\n\t\t\"Indica si los detalles de las sugerencias están visibles.\",\n\t\t\"Indica si hay varias sugerencias para elegir.\",\n\t\t\"Indica si la inserción de la sugerencia actual genera un cambio o si ya se ha escrito todo.\",\n\t\t\"Indica si se insertan sugerencias al presionar Entrar.\",\n\t\t\"Indica si la sugerencia actual tiene el comportamiento de inserción y reemplazo.\",\n\t\t\"Indica si el comportamiento predeterminado es insertar o reemplazar.\",\n\t\t\"Indica si la sugerencia actual admite la resolución de más detalles.\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestController\": [\n\t\t\"Aceptando \\\"{0}\\\" ediciones adicionales de {1} realizadas\",\n\t\t\"Sugerencias para Trigger\",\n\t\t\"Insertar\",\n\t\t\"Insertar\",\n\t\t\"Reemplazar\",\n\t\t\"Reemplazar\",\n\t\t\"Insertar\",\n\t\t\"mostrar menos\",\n\t\t\"mostrar más\",\n\t\t\"Restablecer tamaño del widget de sugerencias\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidget\": [\n\t\t\"Color de fondo del widget sugerido.\",\n\t\t\"Color de borde del widget sugerido.\",\n\t\t\"Color de primer plano del widget sugerido.\",\n\t\t\"Color de primer plano de le entrada seleccionada del widget de sugerencias.\",\n\t\t\"Color de primer plano del icono de la entrada seleccionada en el widget de sugerencias.\",\n\t\t\"Color de fondo de la entrada seleccionada del widget sugerido.\",\n\t\t\"Color del resaltado coincidido en el widget sugerido.\",\n\t\t\"Color de los resaltados de coincidencia en el widget de sugerencias cuando se enfoca un elemento.\",\n\t\t\"Color de primer plano del estado del widget sugerido.\",\n\t\t\"Cargando...\",\n\t\t\"No hay sugerencias.\",\n\t\t\"Sugerir\",\n\t\t\"{0} {1}, {2}\",\n\t\t\"{0} {1}\",\n\t\t\"{0}, {1}\",\n\t\t\"{0}, documentos: {1}\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetDetails\": [\n\t\t\"Cerrar\",\n\t\t\"Cargando...\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetRenderer\": [\n\t\t\"Icono para obtener más información en el widget de sugerencias.\",\n\t\t\"Leer más\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetStatus\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/symbolIcons/browser/symbolIcons\": [\n\t\t\"Color de primer plano de los símbolos de matriz. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos booleanos. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de clase. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de color. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos constantes. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de constructor. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de enumerador. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de miembro del enumerador. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de evento. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de campo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de archivo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de carpeta. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de función. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de interfaz. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de claves. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de palabra clave. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de método. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de módulo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de espacio de nombres. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos nulos. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano para los símbolos numéricos. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de objeto. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano para los símbolos del operador. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de paquete. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de propiedad. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de referencia. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de fragmento de código. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de cadena. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de estructura. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de texto. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano para los símbolos de parámetro de tipo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos de unidad. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t\t\"Color de primer plano de los símbolos variables. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\n\t],\n\t\"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode\": [\n\t\t\"Alternar tecla de tabulación para mover el punto de atención\",\n\t\t\"Presionando la pestaña ahora moverá el foco al siguiente elemento enfocable.\",\n\t\t\"Presionando la pestaña ahora insertará el carácter de tabulación\",\n\t],\n\t\"vs/editor/contrib/tokenization/browser/tokenization\": [\n\t\t\"Desarrollador: forzar nueva aplicación de token\",\n\t],\n\t\"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter\": [\n\t\t\"Icono que se muestra con un mensaje de advertencia en el editor de extensiones.\",\n\t\t\"Este documento contiene muchos caracteres Unicode ASCII no básicos\",\n\t\t\"Este documento contiene muchos caracteres Unicode ambiguos\",\n\t\t\"Este documento contiene muchos caracteres Unicode invisibles\",\n\t\t\"El carácter {0} podría confundirse con el carácter ASCII {1}, que es más común en el código fuente.\",\n\t\t\"El carácter {0} podría confundirse con el carácter {1}, que es más común en el código fuente.\",\n\t\t\"El carácter {0} es invisible.\",\n\t\t\"El carácter {0} no es un carácter ASCII básico.\",\n\t\t\"Ajustar la configuración\",\n\t\t\"Deshabilitar resaltado en comentarios\",\n\t\t\"Deshabilitar resaltado de caracteres en comentarios\",\n\t\t\"Deshabilitar resaltado en cadenas\",\n\t\t\"Deshabilitar resaltado de caracteres en cadenas\",\n\t\t\"Deshabilitar resaltado ambiguo\",\n\t\t\"Deshabilitar el resaltado de caracteres ambiguos\",\n\t\t\"Deshabilitar resaltado invisible\",\n\t\t\"Deshabilitar el resaltado de caracteres invisibles\",\n\t\t\"Deshabilitar resaltado que no es ASCII\",\n\t\t\"Deshabilitar el resaltado de caracteres ASCII no básicos\",\n\t\t\"Mostrar opciones de exclusión\",\n\t\t\"Excluir {0} (carácter invisible) de que se resalte\",\n\t\t\"Excluir {0} de ser resaltado\",\n\t\t\"Permite caracteres Unicode más comunes en el idioma \\\"{0}\\\".\",\n\t\t\"Configurar opciones de resaltado Unicode\",\n\t],\n\t\"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators\": [\n\t\t\"Terminadores de línea inusuales\",\n\t\t\"Se han detectado terminadores de línea inusuales\",\n\t\t\"Este archivo \\\"{0}\\\" contiene uno o más caracteres de terminación de línea inusuales, como el separador de línea (LS) o el separador de párrafo (PS).\\r\\n\\r\\nSe recomienda eliminarlos del archivo. Esto puede configurarse mediante \\\"editor.unusualLineTerminators\\\".\",\n\t\t\"&&Quitar terminadores de línea inusuales\",\n\t\t\"Omitir\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/highlightDecorations\": [\n\t\t\"Color de fondo de un símbolo durante el acceso de lectura, como la lectura de una variable. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\n\t\t\"Color de fondo de un símbolo durante el acceso de escritura, como escribir en una variable. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de fondo de la presencia textual para un símbolo. Para evitar ocultar cualquier decoración subyacente, el color no debe ser opaco.\",\n\t\t\"Color de fondo de un símbolo durante el acceso de lectura; por ejemplo, cuando se lee una variable.\",\n\t\t\"Color de fondo de un símbolo durante el acceso de escritura; por ejemplo, cuando se escribe una variable.\",\n\t\t\"Color de borde de una repetición textual de un símbolo.\",\n\t\t\"Color del marcador de regla general para destacados de símbolos. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\n\t\t\"Color de marcador de regla general para destacados de símbolos de acceso de escritura. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color del marcador de regla de información general de una repetición textual de un símbolo. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/wordHighlighter\": [\n\t\t\"Ir al siguiente símbolo destacado\",\n\t\t\"Ir al símbolo destacado anterior\",\n\t\t\"Desencadenar los símbolos destacados\",\n\t],\n\t\"vs/editor/contrib/wordOperations/browser/wordOperations\": [\n\t\t\"Eliminar palabra\",\n\t],\n\t\"vs/platform/action/common/actionCommonCategories\": [\n\t\t\"Ver\",\n\t\t\"Ayuda\",\n\t\t\"Probar\",\n\t\t\"archivo\",\n\t\t\"Preferencias\",\n\t\t\"Desarrollador\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionList\": [\n\t\t\"{0} para aplicar, {1} para previsualizar\",\n\t\t\"{0} para aplicar\",\n\t\t\"{0}, Motivo de deshabilitación: {1}\",\n\t\t\"Widget de acción\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionWidget\": [\n\t\t\"Color de fondo de los elementos de acción alternados en la barra de acciones.\",\n\t\t\"Si la lista de widgets de acción es visible\",\n\t\t\"Ocultar el widget de acción\",\n\t\t\"Seleccione la acción anterior\",\n\t\t\"Seleccione la siguiente acción\",\n\t\t\"Aceptar la acción seleccionada\",\n\t\t\"Vista previa de la acción seleccionada\",\n\t],\n\t\"vs/platform/actions/browser/menuEntryActionViewItem\": [\n\t\t\"{0} ({1})\",\n\t\t\"{0} ({1})\",\n\t\t\"{0}\\r\\n[{1}] {2}\",\n\t],\n\t\"vs/platform/actions/browser/toolbar\": [\n\t\t\"Ocultar\",\n\t\t\"Menú Restablecer\",\n\t],\n\t\"vs/platform/actions/common/menuService\": [\n\t\t\"Ocultar \\\"{0}\\\"\",\n\t],\n\t\"vs/platform/audioCues/browser/audioCueService\": [\n\t\t\"Error en la línea\",\n\t\t\"Advertencia en la línea\",\n\t\t\"Área doblada en la línea\",\n\t\t\"Punto de interrupción en la línea\",\n\t\t\"Sugerencia insertada en la línea\",\n\t\t\"Corrección rápida del terminal\",\n\t\t\"Depurador detenido en el punto de interrupción\",\n\t\t\"No hay sugerencias de incrustación en la línea\",\n\t\t\"Tarea completada.\",\n\t\t\"Error en la tarea\",\n\t\t\"Error del comando de terminal\",\n\t\t\"Campana de terminal\",\n\t\t\"Celda del bloc de notas completada\",\n\t\t\"Error en la celda del bloc de notas\",\n\t\t\"Línea de diferencia insertada\",\n\t\t\"Línea de diferencia eliminada\",\n\t\t\"Línea de diferencia modificada\",\n\t\t\"Se envió una solicitud de chat\",\n\t\t\"Respuesta de chat recibida\",\n\t\t\"Respuesta de chat pendiente\",\n\t\t\"Borrar\",\n\t\t\"Guardar\",\n\t\t\"Formato\",\n\t],\n\t\"vs/platform/configuration/common/configurationRegistry\": [\n\t\t\"La configuración del lenguaje predeterminada se reemplaza\",\n\t\t\"Configure los valores que se invalidarán para el idioma {0}.\",\n\t\t\"Establecer los valores de configuración que se reemplazarán para un lenguaje.\",\n\t\t\"Esta configuración no admite la configuración por idioma.\",\n\t\t\"Establecer los valores de configuración que se reemplazarán para un lenguaje.\",\n\t\t\"Esta configuración no admite la configuración por idioma.\",\n\t\t\"No se puede registrar una propiedad vacía.\",\n\t\t\"No se puede registrar \\\"{0}\\\". Coincide con el patrón de propiedad \\'\\\\\\\\[.*\\\\\\\\]$\\' para describir la configuración del editor específica del lenguaje. Utilice la contribución \\\"configurationDefaults\\\".\",\n\t\t\"No se puede registrar \\\"{0}\\\". Esta propiedad ya está registrada.\",\n\t\t\"No se puede registrar \\\"{0}\\\". La directiva asociada {1} ya está registrada con {2}.\",\n\t],\n\t\"vs/platform/contextkey/browser/contextKeyService\": [\n\t\t\"Comando que devuelve información sobre las claves de contexto\",\n\t],\n\t\"vs/platform/contextkey/common/contextkey\": [\n\t\t\"Expresión de clave de contexto vacía\",\n\t\t\"¿Ha olvidado escribir una expresión? también puede poner \\\"false\\\" o \\\"true\\\" para evaluar siempre como false o true, respectivamente.\",\n\t\t\"\\'in\\' después de \\'not\\'.\",\n\t\t\"paréntesis de cierre \\')\\'\",\n\t\t\"Token inesperado\",\n\t\t\"¿Ha olvidado poner && o || antes del token?\",\n\t\t\"Final de expresión inesperado\",\n\t\t\"¿Ha olvidado poner una clave de contexto?\",\n\t\t\"Esperado: {0}\\r\\nrecibido: \\'{1}\\'.\",\n\t],\n\t\"vs/platform/contextkey/common/contextkeys\": [\n\t\t\"Si el sistema operativo es macOS\",\n\t\t\"Si el sistema operativo es Linux\",\n\t\t\"Si el sistema operativo es Windows\",\n\t\t\"Si la plataforma es un explorador web\",\n\t\t\"Si el sistema operativo es macOS en una plataforma que no es de explorador\",\n\t\t\"Si el sistema operativo es IOS\",\n\t\t\"Si la plataforma es un explorador web móvil\",\n\t\t\"Tipo de calidad de VS Code\",\n\t\t\"Si el foco del teclado está dentro de un cuadro de entrada\",\n\t],\n\t\"vs/platform/contextkey/common/scanner\": [\n\t\t\"¿Quiso decir {0}?\",\n\t\t\"¿Quiso decir {0} o {1}?\",\n\t\t\"¿Quiso decir {0}, {1} o {2}?\",\n\t\t\"¿Ha olvidado abrir o cerrar la cita?\",\n\t\t\"¿Ha olvidado escapar el carácter \\\"/\\\" (barra diagonal)?Coloque dos barras diagonales inversas antes de que escape, por ejemplo, \\'\\\\\\\\/\\'.\",\n\t],\n\t\"vs/platform/history/browser/contextScopedHistoryWidget\": [\n\t\t\"Indica si las sugerencias están visibles.\",\n\t],\n\t\"vs/platform/keybinding/common/abstractKeybindingService\": [\n\t\t\"Se presionó ({0}). Esperando la siguiente tecla...\",\n\t\t\"Se ha presionado ({0}). Esperando la siguiente tecla...\",\n\t\t\"La combinación de claves ({0}, {1}) no es un comando.\",\n\t\t\"La combinación de claves ({0}, {1}) no es un comando.\",\n\t],\n\t\"vs/platform/list/browser/listService\": [\n\t\t\"Área de trabajo\",\n\t\t\"Se asigna a \\\"Control\\\" en Windows y Linux y a \\\"Comando\\\" en macOS.\",\n\t\t\"Se asigna a \\\"Alt\\\" en Windows y Linux y a \\\"Opción\\\" en macOS.\",\n\t\t\"El modificador que se utilizará para agregar un elemento en los árboles y listas para una selección múltiple con el ratón (por ejemplo en el explorador, abiertos editores y vista de scm). Los gestos de ratón \\'Abrir hacia\\' - si están soportados - se adaptarán de forma tal que no tenga conflicto con el modificador múltiple.\",\n\t\t\"Controla cómo abrir elementos en los árboles y las listas mediante el mouse (si se admite). Tenga en cuenta que algunos árboles y listas pueden optar por ignorar esta configuración si no es aplicable.\",\n\t\t\"Controla si las listas y los árboles admiten el desplazamiento horizontal en el área de trabajo. Advertencia: La activación de esta configuración repercute en el rendimiento.\",\n\t\t\"Controla si los clics en la barra de desplazamiento se desplazan página por página.\",\n\t\t\"Controla la sangría de árbol en píxeles.\",\n\t\t\"Controla si el árbol debe representar guías de sangría.\",\n\t\t\"Controla si las listas y los árboles tienen un desplazamiento suave.\",\n\t\t\"Se usará un multiplicador en los eventos de desplazamiento de la rueda del mouse \\\"deltaX\\\" y \\\"deltaY\\\". \",\n\t\t\"Multiplicador de la velocidad de desplazamiento al presionar \\\"Alt\\\".\",\n\t\t\"Resalta elementos al buscar. Navegar más arriba o abajo pasará solo por los elementos resaltados.\",\n\t\t\"Filtre elementos al buscar.\",\n\t\t\"Controla el modo de búsqueda predeterminado para listas y árboles en el área de trabajo.\",\n\t\t\"La navegación simple del teclado se centra en elementos que coinciden con la entrada del teclado. El emparejamiento se hace solo en prefijos.\",\n\t\t\"Destacar la navegación del teclado resalta los elementos que coinciden con la entrada del teclado. Más arriba y abajo la navegación atravesará solo los elementos destacados.\",\n\t\t\"La navegación mediante el teclado de filtro filtrará y ocultará todos los elementos que no coincidan con la entrada del teclado.\",\n\t\t\"Controla el estilo de navegación del teclado para listas y árboles en el área de trabajo. Puede ser simple, resaltar y filtrar.\",\n\t\t\"Use \\\"workbench.list.defaultFindMode\\\" y \\\"workbench.list.typeNavigationMode\\\" en su lugar.\",\n\t\t\"Usar coincidencias aproximadas al buscar.\",\n\t\t\"Use coincidencias contiguas al buscar.\",\n\t\t\"Controla el tipo de coincidencia que se usa al buscar listas y árboles en el área de trabajo.\",\n\t\t\"Controla cómo se expanden las carpetas de árbol al hacer clic en sus nombres. Tenga en cuenta que algunos árboles y listas pueden optar por omitir esta configuración si no es aplicable.\",\n\t\t\"Controla si el desplazamiento permanente está habilitado en los árboles.\",\n\t\t\"Controla el número de elementos permanentes que se muestran en el árbol cuando \\'#workbench.tree.enableStickyScroll#\\' está habilitado.\",\n\t\t\"Controla el funcionamiento de la navegación por tipos en listas y árboles del área de trabajo. Cuando se establece en \\\"trigger\\\", la navegación por tipos comienza una vez que se ejecuta el comando \\\"list.triggerTypeNavigation\\\".\",\n\t],\n\t\"vs/platform/markers/common/markers\": [\n\t\t\"Error\",\n\t\t\"Advertencia\",\n\t\t\"Información\",\n\t],\n\t\"vs/platform/quickinput/browser/commandsQuickAccess\": [\n\t\t\"usado recientemente\",\n\t\t\"comandos similares\",\n\t\t\"usados habitualmente\",\n\t\t\"otros comandos\",\n\t\t\"comandos similares\",\n\t\t\"{0}, {1}\",\n\t\t\"El comando \\\"{0}\\\" ha dado lugar a un error\",\n\t],\n\t\"vs/platform/quickinput/browser/helpQuickAccess\": [\n\t\t\"{0}, {1}\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInput\": [\n\t\t\"Atrás\",\n\t\t\"Presione \\\"Entrar\\\" para confirmar su entrada o \\\"Esc\\\" para cancelar\",\n\t\t\"{0}/{1}\",\n\t\t\"Escriba para restringir los resultados.\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputController\": [\n\t\t\"Activar o desactivar todas las casillas\",\n\t\t\"{0} resultados\",\n\t\t\"{0} seleccionados\",\n\t\t\"Aceptar\",\n\t\t\"Personalizado\",\n\t\t\"Atrás ({0})\",\n\t\t\"Atrás\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputList\": [\n\t\t\"Entrada rápida\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputUtils\": [\n\t\t\"Haga clic en para ejecutar el comando \\\"{0}\\\"\",\n\t],\n\t\"vs/platform/theme/common/colorRegistry\": [\n\t\t\"Color de primer plano general. Este color solo se usa si un componente no lo invalida.\",\n\t\t\"Primer plano general de los elementos deshabilitados. Este color solo se usa si un componente no lo reemplaza.\",\n\t\t\"Color de primer plano general para los mensajes de erroe. Este color solo se usa si un componente no lo invalida.\",\n\t\t\"Color de primer plano para el texto descriptivo que proporciona información adicional, por ejemplo para una etiqueta.\",\n\t\t\"El color predeterminado para los iconos en el área de trabajo.\",\n\t\t\"Color de borde de los elementos con foco. Este color solo se usa si un componente no lo invalida.\",\n\t\t\"Un borde adicional alrededor de los elementos para separarlos unos de otros y así mejorar el contraste.\",\n\t\t\"Un borde adicional alrededor de los elementos activos para separarlos unos de otros y así mejorar el contraste.\",\n\t\t\"El color de fondo del texto seleccionado en el área de trabajo (por ejemplo, campos de entrada o áreas de texto). Esto no se aplica a las selecciones dentro del editor.\",\n\t\t\"Color para los separadores de texto.\",\n\t\t\"Color de primer plano para los vínculos en el texto.\",\n\t\t\"Color de primer plano para los enlaces de texto, al hacer clic o pasar el mouse sobre ellos.\",\n\t\t\"Color de primer plano para los segmentos de texto con formato previo.\",\n\t\t\"Color de fondo para segmentos de texto con formato previo.\",\n\t\t\"Color de fondo para los bloques en texto.\",\n\t\t\"Color de borde para los bloques en texto.\",\n\t\t\"Color de fondo para los bloques de código en el texto.\",\n\t\t\"Color de sombra de los widgets dentro del editor, como buscar/reemplazar\",\n\t\t\"Color de borde de los widgets dentro del editor, como buscar/reemplazar\",\n\t\t\"Fondo de cuadro de entrada.\",\n\t\t\"Primer plano de cuadro de entrada.\",\n\t\t\"Borde de cuadro de entrada.\",\n\t\t\"Color de borde de opciones activadas en campos de entrada.\",\n\t\t\"Color de fondo de las opciones activadas en los campos de entrada.\",\n\t\t\"Color de fondo al pasar por encima de las opciones en los campos de entrada.\",\n\t\t\"Color de primer plano de las opciones activadas en los campos de entrada.\",\n\t\t\"Color de primer plano para el marcador de posición de texto\",\n\t\t\"Color de fondo de validación de entrada para gravedad de información.\",\n\t\t\"Color de primer plano de validación de entrada para información de gravedad.\",\n\t\t\"Color de borde de validación de entrada para gravedad de información.\",\n\t\t\"Color de fondo de validación de entrada para gravedad de advertencia.\",\n\t\t\"Color de primer plano de validación de entrada para información de advertencia.\",\n\t\t\"Color de borde de validación de entrada para gravedad de advertencia.\",\n\t\t\"Color de fondo de validación de entrada para gravedad de error.\",\n\t\t\"Color de primer plano de validación de entrada para información de error.\",\n\t\t\"Color de borde de valdación de entrada para gravedad de error.\",\n\t\t\"Fondo de lista desplegable.\",\n\t\t\"Fondo de la lista desplegable.\",\n\t\t\"Primer plano de lista desplegable.\",\n\t\t\"Borde de lista desplegable.\",\n\t\t\"Color de primer plano del botón.\",\n\t\t\"Color del separador de botones.\",\n\t\t\"Color de fondo del botón.\",\n\t\t\"Color de fondo del botón al mantener el puntero.\",\n\t\t\"Color del borde del botón\",\n\t\t\"Color de primer plano del botón secundario.\",\n\t\t\"Color de fondo del botón secundario.\",\n\t\t\"Color de fondo del botón secundario al mantener el mouse.\",\n\t\t\"Color de fondo de la insignia. Las insignias son pequeñas etiquetas de información, por ejemplo los resultados de un número de resultados.\",\n\t\t\"Color de primer plano de la insignia. Las insignias son pequeñas etiquetas de información, por ejemplo los resultados de un número de resultados.\",\n\t\t\"Sombra de la barra de desplazamiento indica que la vista se ha despazado.\",\n\t\t\"Color de fondo de control deslizante de barra de desplazamiento.\",\n\t\t\"Color de fondo de barra de desplazamiento cursor cuando se pasar sobre el control.\",\n\t\t\"Color de fondo de la barra de desplazamiento al hacer clic.\",\n\t\t\"Color de fondo para la barra de progreso que se puede mostrar para las operaciones de larga duración.\",\n\t\t\"Color de fondo del texto de error del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de primer plano de squigglies de error en el editor.\",\n\t\t\"Si se establece, color de subrayados dobles para errores en el editor.\",\n\t\t\"Color de fondo del texto de advertencia del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de primer plano de squigglies de advertencia en el editor.\",\n\t\t\"Si se establece, color de subrayados dobles para advertencias en el editor.\",\n\t\t\"Color de fondo del texto de información del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de primer plano de los subrayados ondulados informativos en el editor.\",\n\t\t\"Si se establece, color de subrayados dobles para informaciones en el editor.\",\n\t\t\"Color de primer plano de pista squigglies en el editor.\",\n\t\t\"Si se establece, color de subrayados dobles para sugerencias en el editor.\",\n\t\t\"Color de borde de los marcos activos.\",\n\t\t\"Color de fondo del editor.\",\n\t\t\"Color de primer plano predeterminado del editor.\",\n\t\t\"Color de fondo de desplazamiento permanente para el editor\",\n\t\t\"Desplazamiento permanente al mantener el mouse sobre el color de fondo del editor\",\n\t\t\"Color de fondo del editor de widgets como buscar/reemplazar\",\n\t\t\"Color de primer plano de los widgets del editor, como buscar y reemplazar.\",\n\t\t\"Color de borde de los widgets del editor. El color solo se usa si el widget elige tener un borde y no invalida el color.\",\n\t\t\"Color del borde de la barra de cambio de tamaño de los widgets del editor. El color se utiliza solo si el widget elige tener un borde de cambio de tamaño y si un widget no invalida el color.\",\n\t\t\"Color de fondo del selector rápido. El widget del selector rápido es el contenedor para selectores como la paleta de comandos.\",\n\t\t\"Color de primer plano del selector rápido. El widget del selector rápido es el contenedor para selectores como la paleta de comandos.\",\n\t\t\"Color de fondo del título del selector rápido. El widget del selector rápido es el contenedor para selectores como la paleta de comandos.\",\n\t\t\"Selector de color rápido para la agrupación de etiquetas.\",\n\t\t\"Selector de color rápido para la agrupación de bordes.\",\n\t\t\"Color de fondo de etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un método abreviado de teclado.\",\n\t\t\"Color de primer plano de etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un método abreviado de teclado.\",\n\t\t\"Color del borde de la etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un método abreviado de teclado.\",\n\t\t\"Color del borde inferior de la etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un método abreviado de teclado.\",\n\t\t\"Color de la selección del editor.\",\n\t\t\"Color del texto seleccionado para alto contraste.\",\n\t\t\"Color de la selección en un editor inactivo. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\n\t\t\"Color en las regiones con el mismo contenido que la selección. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\n\t\t\"Color de borde de las regiones con el mismo contenido que la selección.\",\n\t\t\"Color de la coincidencia de búsqueda actual.\",\n\t\t\"Color de los otros resultados de la búsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de la gama que limita la búsqueda. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\n\t\t\"Color de borde de la coincidencia de búsqueda actual.\",\n\t\t\"Color de borde de otra búsqueda que coincide.\",\n\t\t\"Color del borde de la gama que limita la búsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de las consultas coincidentes del Editor de búsqueda.\",\n\t\t\"Color de borde de las consultas coincidentes del Editor de búsqueda.\",\n\t\t\"Color del texto en el mensaje de finalización del viewlet de búsqueda.\",\n\t\t\"Destacar debajo de la palabra para la que se muestra un mensaje al mantener el mouse. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\n\t\t\"Color de fondo al mantener el puntero en el editor.\",\n\t\t\"Color de primer plano al mantener el puntero en el editor.\",\n\t\t\"Color del borde al mantener el puntero en el editor.\",\n\t\t\"Color de fondo de la barra de estado al mantener el puntero en el editor.\",\n\t\t\"Color de los vínculos activos.\",\n\t\t\"Color de primer plano de las sugerencias insertadas\",\n\t\t\"Color de fondo de las sugerencias insertadas\",\n\t\t\"Color de primer plano de las sugerencias insertadas para los tipos de letra\",\n\t\t\"Color de fondo de las sugerencias insertadas para los tipos de letra\",\n\t\t\"Color de primer plano de las sugerencias insertadas para los parámetros\",\n\t\t\"Color de fondo de las sugerencias insertadas para los parámetros\",\n\t\t\"El color utilizado para el icono de bombilla de acciones.\",\n\t\t\"El color utilizado para el icono de la bombilla de acciones de corrección automática.\",\n\t\t\"El color utilizado para el icono de bombilla de inteligencia artificial.\",\n\t\t\"Color de fondo para el texto que se insertó. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de fondo para el texto que se eliminó. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\n\t\t\"Color de fondo de las líneas insertadas. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de fondo de las líneas que se quitaron. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de fondo del margen donde se insertaron las líneas.\",\n\t\t\"Color de fondo del margen donde se quitaron las líneas.\",\n\t\t\"Primer plano de la regla de información general de diferencias para el contenido insertado.\",\n\t\t\"Primer plano de la regla de información general de diferencias para el contenido quitado.\",\n\t\t\"Color de contorno para el texto insertado.\",\n\t\t\"Color de contorno para el texto quitado.\",\n\t\t\"Color del borde entre ambos editores de texto.\",\n\t\t\"Color de relleno diagonal del editor de diferencias. El relleno diagonal se usa en las vistas de diferencias en paralelo.\",\n\t\t\"Color de fondo de los bloques sin modificar en el editor de diferencias.\",\n\t\t\"Color de primer plano de los bloques sin modificar en el editor de diferencias.\",\n\t\t\"Color de fondo del código sin modificar en el editor de diferencias.\",\n\t\t\"Color de fondo de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\n\t\t\"Color de primer plano de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\n\t\t\"Color de contorno de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.\",\n\t\t\"Color de contorno de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos y seleccionados. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.\",\n\t\t\"Color de fondo de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\n\t\t\"Color de primer plano de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\n\t\t\"Color de primer plano del icono de lista o árbol del elemento seleccionado cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\n\t\t\"Color de fondo de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\n\t\t\"Color de primer plano de la lista o el árbol del elemento con el foco cuando la lista o el árbol esta inactiva. Una lista o un árbol tiene el foco del teclado cuando está activo, cuando esta inactiva no.\",\n\t\t\"Color de primer plano del icono de lista o árbol del elemento seleccionado cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\n\t\t\"Color de fondo de la lista o el árbol del elemento con el foco cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.\",\n\t\t\"Color de contorno de la lista o el árbol del elemento con el foco cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.\",\n\t\t\"Fondo de la lista o el árbol al mantener el mouse sobre los elementos.\",\n\t\t\"Color de primer plano de la lista o el árbol al pasar por encima de los elementos con el ratón.\",\n\t\t\"Fondo de arrastrar y colocar la lista o el árbol al mover los elementos con el mouse.\",\n\t\t\"Color de primer plano de la lista o el árbol de las coincidencias resaltadas al buscar dentro de la lista o el ábol.\",\n\t\t\"Color de primer plano de la lista o árbol de los elementos coincidentes en los elementos enfocados activamente cuando se busca dentro de la lista o árbol.\",\n\t\t\"Color de primer plano de una lista o árbol para los elementos inválidos, por ejemplo una raiz sin resolver en el explorador.\",\n\t\t\"Color del primer plano de elementos de lista que contienen errores.\",\n\t\t\"Color del primer plano de elementos de lista que contienen advertencias.\",\n\t\t\"Color de fondo del widget de filtro de tipo en listas y árboles.\",\n\t\t\"Color de contorno del widget de filtro de tipo en listas y árboles.\",\n\t\t\"Color de contorno del widget de filtro de tipo en listas y árboles, cuando no hay coincidencias.\",\n\t\t\"Color de sombra del widget de filtrado de escritura en listas y árboles.\",\n\t\t\"Color de fondo de la coincidencia filtrada.\",\n\t\t\"Color de borde de la coincidencia filtrada.\",\n\t\t\"Color de trazo de árbol para las guías de sangría.\",\n\t\t\"Color de trazo de árbol para las guías de sangría que no están activas.\",\n\t\t\"Color de borde de la tabla entre columnas.\",\n\t\t\"Color de fondo para las filas de tabla impares.\",\n\t\t\"Color de primer plano de lista/árbol para los elementos no enfatizados.\",\n\t\t\"Color de fondo de la casilla de verificación del widget.\",\n\t\t\"Color de fondo del widget de la casilla cuando se selecciona el elemento en el que se encuentra.\",\n\t\t\"Color de primer plano del widget de la casilla de verificación.\",\n\t\t\"Color del borde del widget de la casilla de verificación.\",\n\t\t\"Color de borde del widget de la casilla cuando se selecciona el elemento en el que se encuentra.\",\n\t\t\"Use quickInputList.focusBackground en su lugar.\",\n\t\t\"Selector rápido del color de primer plano para el elemento con el foco.\",\n\t\t\"Color de primer plano del icono del selector rápido para el elemento con el foco.\",\n\t\t\"Color de fondo del selector rápido para el elemento con el foco.\",\n\t\t\"Color del borde de los menús.\",\n\t\t\"Color de primer plano de los elementos de menú.\",\n\t\t\"Color de fondo de los elementos de menú.\",\n\t\t\"Color de primer plano del menu para el elemento del menú seleccionado.\",\n\t\t\"Color de fondo del menu para el elemento del menú seleccionado.\",\n\t\t\"Color del borde del elemento seleccionado en los menús.\",\n\t\t\"Color del separador del menu para un elemento del menú.\",\n\t\t\"El fondo de la barra de herramientas se perfila al pasar por encima de las acciones con el mouse.\",\n\t\t\"La barra de herramientas se perfila al pasar por encima de las acciones con el mouse.\",\n\t\t\"Fondo de la barra de herramientas al mantener el mouse sobre las acciones\",\n\t\t\"Resaltado del color de fondo para una ficha de un fragmento de código.\",\n\t\t\"Resaltado del color del borde para una ficha de un fragmento de código.\",\n\t\t\"Resaltado del color de fondo para la última ficha de un fragmento de código.\",\n\t\t\"Resaltado del color del borde para la última tabulación de un fragmento de código.\",\n\t\t\"Color de los elementos de ruta de navegación que reciben el foco.\",\n\t\t\"Color de fondo de los elementos de ruta de navegación\",\n\t\t\"Color de los elementos de ruta de navegación que reciben el foco.\",\n\t\t\"Color de los elementos de ruta de navegación seleccionados.\",\n\t\t\"Color de fondo del selector de elementos de ruta de navegación.\",\n\t\t\"Fondo del encabezado actual en los conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Fondo de contenido actual en los conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Fondo de encabezado entrante en los conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Fondo de contenido entrante en los conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Fondo de cabecera de elemento antecesor común en conflictos de fusión en línea. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\n\t\t\"Fondo de contenido antecesor común en conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color del borde en los encabezados y el divisor en conflictos de combinación alineados.\",\n\t\t\"Primer plano de la regla de visión general actual para conflictos de combinación alineados.\",\n\t\t\"Primer plano de regla de visión general de entrada para conflictos de combinación alineados.\",\n\t\t\"Primer plano de la regla de visión general de ancestros comunes para conflictos de combinación alineados.\",\n\t\t\"Color del marcador de regla general para buscar actualizaciones. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color del marcador de la regla general para los destacados de la selección. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\n\t\t\"Color de marcador de minimapa para coincidencias de búsqueda.\",\n\t\t\"Color de marcador de minimapa para las selecciones del editor que se repiten.\",\n\t\t\"Color del marcador de minimapa para la selección del editor.\",\n\t\t\"Color del marcador de minimapa para información.\",\n\t\t\"Color del marcador de minimapa para advertencias.\",\n\t\t\"Color del marcador de minimapa para errores.\",\n\t\t\"Color de fondo del minimapa.\",\n\t\t\"Opacidad de los elementos de primer plano representados en el minimapa. Por ejemplo, \\\"#000000c0\\\" representará los elementos con 75% de opacidad.\",\n\t\t\"Color de fondo del deslizador del minimapa.\",\n\t\t\"Color de fondo del deslizador del minimapa al pasar el puntero.\",\n\t\t\"Color de fondo del deslizador de minimapa al hacer clic en él.\",\n\t\t\"Color utilizado para el icono de error de problemas.\",\n\t\t\"Color utilizado para el icono de advertencia de problemas.\",\n\t\t\"Color utilizado para el icono de información de problemas.\",\n\t\t\"Color de primer plano que se usa en los gráficos.\",\n\t\t\"Color que se usa para las líneas horizontales en los gráficos.\",\n\t\t\"Color rojo que se usa en las visualizaciones de gráficos.\",\n\t\t\"Color azul que se usa en las visualizaciones de gráficos.\",\n\t\t\"Color amarillo que se usa en las visualizaciones de gráficos.\",\n\t\t\"Color naranja que se usa en las visualizaciones de gráficos.\",\n\t\t\"Color verde que se usa en las visualizaciones de gráficos.\",\n\t\t\"Color púrpura que se usa en las visualizaciones de gráficos.\",\n\t],\n\t\"vs/platform/theme/common/iconRegistry\": [\n\t\t\"Identificador de la fuente que se va a usar. Si no se establece, se usa la fuente definida en primer lugar.\",\n\t\t\"Carácter de fuente asociado a la definición del icono.\",\n\t\t\"Icono de la acción de cierre en los widgets.\",\n\t\t\"Icono para ir a la ubicación del editor anterior.\",\n\t\t\"Icono para ir a la ubicación del editor siguiente.\",\n\t],\n\t\"vs/platform/undoRedo/common/undoRedoService\": [\n\t\t\"Se han cerrado los siguientes archivos y se han modificado en el disco: {0}.\",\n\t\t\"Los siguientes archivos se han modificado de forma incompatible: {0}.\",\n\t\t\"No se pudo deshacer \\\"{0}\\\" en todos los archivos. {1}\",\n\t\t\"No se pudo deshacer \\\"{0}\\\" en todos los archivos. {1}\",\n\t\t\"No se pudo deshacer \\\"{0}\\\" en todos los archivos porque se realizaron cambios en {1}\",\n\t\t\"No se pudo deshacer \\\"{0}\\\" en todos los archivos porque ya hay una operación de deshacer o rehacer en ejecución en {1}\",\n\t\t\"No se pudo deshacer \\\"{0}\\\" en todos los archivos porque se produjo una operación de deshacer o rehacer mientras tanto\",\n\t\t\"¿Desea deshacer \\\"{0}\\\" en todos los archivos?\",\n\t\t\"&&Deshacer en {0} archivos\",\n\t\t\"Deshacer este &&archivo\",\n\t\t\"No se pudo deshacer \\\"{0}\\\" porque ya hay una operación de deshacer o rehacer en ejecución.\",\n\t\t\"¿Quiere deshacer \\\"{0}\\\"?\",\n\t\t\"&&Sí\",\n\t\t\"No\",\n\t\t\"No se pudo rehacer \\\"{0}\\\" en todos los archivos. {1}\",\n\t\t\"No se pudo rehacer \\\"{0}\\\" en todos los archivos. {1}\",\n\t\t\"No se pudo volver a hacer \\\"{0}\\\" en todos los archivos porque se realizaron cambios en {1}\",\n\t\t\"No se pudo rehacer \\\"{0}\\\" en todos los archivos porque ya hay una operación de deshacer o rehacer en ejecución en {1}\",\n\t\t\"No se pudo rehacer \\\"{0}\\\" en todos los archivos porque se produjo una operación de deshacer o rehacer mientras tanto\",\n\t\t\"No se pudo rehacer \\\"{0}\\\" porque ya hay una operación de deshacer o rehacer en ejecución.\",\n\t],\n\t\"vs/platform/workspace/common/workspace\": [\n\t\t\"Área de trabajo de código\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA<PERSON>,OAAO,+BAAgC,CACtC,+CAAgD,CAC/C,WACD,EACA,yCAA0C,CACzC,SACD,EACA,gDAAiD,CAChD,0CACA,0BACA,2BACD,EACA,4CAA6C,CAC5C,UACA,mBACD,EACA,uCAAwC,CACvC,kDACA,2JACD,EACA,8CAA+C,CAC9C,aACD,EACA,uCAAwC,CACvC,aACA,mBACA,sBACA,2BACA,2BACA,iBACD,EACA,qDAAsD,CACrD,aACD,EACA,+CAAgD,CAC/C,oBACD,EACA,qCAAsC,CACrC,oBACD,EACA,uCAAwC,CACvC,UACA,0BACA,6BACA,4BACA,4BACA,SACA,8BACD,EACA,yBAA0B,CACzB,YACD,EACA,8BAA+B,CAC9B,WACA,0BACA,2FACA,2FACA,6BACA,0FACD,EACA,kCAAmC,CAClC,OACA,WACA,MACA,UACA,OACA,WACA,MACA,QACA,UACA,WACA,YACA,UACA,UACA,WACA,MACA,UACA,UACA,WACA,MACA,OACD,EACA,0BAA2B,CAC1B,GACD,EACA,+CAAgD,CAC/C,SACA,iDACA,2EACA,kPACA,6KACD,EACA,iCAAkC,CACjC,oEACA,oEACA,+BACD,EACA,qCAAsC,CACrC,aACA,WACA,YACA,UACA,qBACA,kBACD,EACA,4CAA6C,CAC5C,0RACA,0CACD,EACA,2DAA4D,CAC3D,4DACA,0DACA,0DACA,SACA,6FACA,+BACA,sBACA,0BACA,kFACA,WACA,+BACA,oDACA,gCACA,6BACD,EACA,6CAA8C,CAC7C,yEACA,+EACA,8EACD,EACA,kDAAmD,CAClD,8EACA,+EACD,EACA,8DAA+D,CAC9D,yCACA,gDACA,sEACA,0DACA,uCACA,wBACA,sBACA,4CACA,0CACA,yCACA,kCACA,+BACA,wCACA,6BACD,EACA,4DAA6D,CAC5D,qCACA,oBACD,EACA,wDAAyD,CACxD,gDACD,EACA,kEAAmE,CAClE,kCACA,kDACA,gCACA,mDACA,wBACA,2BACD,EACA,kEAAmE,CAClE,8BACA,4BACA,6BACA,2BACA,qCACA,iCACA,sBACD,EACA,uDAAwD,CACvD,sDACA,sDACA,yCACA,yCACD,EACA,wDAAyD,CACxD,yDACD,EACA,oDAAqD,CACpD,SACA,6JACA,wOACA,oIACA,gHACA,mDACA,gGACA,0CACA,8CACA,sEACA,qDACA,sIACA,8EACA,iFACA,8GACA,+EACA,iHACA,0FACA,+FACA,4FACA,kLACA,6DACA,8CACA,4CACA,mIACA,8CACA,4CACA,yIACA,gGACA,wFACA,4FACA,oGACA,uHACA,uHACA,0GACA,0CACA,0CACA,8DACA,6EACA,kDACA,kDACA,yEACA,uEACA,sFACA,wFACA,8FACA,8HACD,EACA,wCAAyC,CACxC,0FACA,iDACA,yDACA,wHACA,gEACA,gJACA,sEACA,mFACA,+EACA,mIACA,8EACA,iHACA,+EACA,6DACA,4GACA,kGACA,iHACA,2MACA,0IACA,8MACA,+JACA,sOACA,qMACA,2KACA,2NACA,iDACA,4FACA,mHACA,oEACA,iEACA,8EACA,mMACA,4GACA,oHACA,6GACA,+GACA,0GACA,6HACA,sIACA,+HACA,kIACA,0HACA,qFACA,iIACA,qIACA,uIACA,kFACA,oOACA,yLACA,4KACA,+DACA,6BACA,2HACA,uIACA,yHACA,sGACA,6EACA,sPACA,+GACA,4DACA,0DACA,wHACA,uHACA,6DACA,wNACA,iIACA,oFACA;AAAA;AAAA;AAAA,2EACA,sCACA,wDACA,8FACA,yHACA,wHACA,sCACA,kDACA,oEACA,0DACA,6FACA,iGACA,4FACA,kFACA,mIACA,8GACA,0EACA,6DACA,qDACA,kDACA,sDACA,0EACA,iZACA,6CACA,qEACA,kGACA,4DACA,8DACA,8FACA,kCACA,6MACA,mFACA,iEACA,gEACA,kEACA,qFACA,mEACA,kEACA,oEACA,gDACA,mDACA,mGACA,oHACA,oMACA,0FACA,8KACA,qGACA,iGACA,uDACA,sGACA,0FACA,+GACA,oHACA,uEACA,iFACA,oNACA,2IACA,oFACA,yCACA,8EACA,gDACA,uEACA,4FACA,uEACA,6DACA,oFACA,kEACA,iEACA,2CACA,4FACA,8CACA,uEACA,2EACA,wEACA,wKACA,8HACA,kFACA,+JACA,2FACA,sFACA,yGACA,mFACA,qWACA,8EACA,uDACA,gGACA,6FACA,mHACA,uGACA,wJACA,gFACA,+EACA,qFACA,iEACA,6TACA,+EACA,kFACA,+EACA,gFACA,mFACA,gFACA,kFACA,+EACA,kFACA,8EACA,0EACA,kFACA,8EACA,oFACA,iFACA,0EACA,0EACA,8EACA,mFACA,qFACA,4EACA,uFACA,iFACA,2EACA,6EACA,oFACA,uFACA,wEACA,wFACA,8FACA,8FACA,qDACA,iKACA,oIACA,iGACA,sIACA,4DACA,iIACA,uFACA,6HACA,oOACA,6EACA,uKACA,uTACA,uBACA,qEACA,+GACA,2GACA,mIACA,6GACA,kHACA,uIACA,oGACA,mGACA,oFACA,kEACA,+GACA,2GACA,iIACA,8DACA,8DACA,sGACA,8KACA,4MACA,yIACA,4GACA,gDACA,gDACA,yGACA,qKACA,0CACA,gDACA,iIACA,gHACA,wHACA,4FACA,mFACA,6FACA,2GACA,yGACA,yEACA,iDACA,kFACA,wIACA,sFACA,sEACA,iCACA,uMACA,8FACA,8CACA,8DACA,qFACA,0FACA,wDACA,wEACA,iDACA,yFACA,sEACA,kEACA,8HACA,yDACA,0DACA,2DACA,0FACA,uMACA,sHACA,kCACA,mLACA,oGACA,qIACA,8EACA,kDACA,gMACA,2EACA,uCACA,4GACA,6FACA,8CACA,mEACA,iEACA,wSACA,oDACA,sCACA,4GACA,4FACA,+BACA,sDACA,oFACA,0EACA,uFACA,gDACA,8CACA,yFACA,wFACA,2FACA,wEACA,yDACA,gEACA,wGACA,4CACA,+EACA,oHACA,gGACA,gFACA,gEACA,2FACA,0EACA,gHACA,wFACA,gMACA,iEACA,qFACA,4CACA,iFACA,mFACA,0EACA,wDACA,6CACA,kFACA,kFACA,wEACA,qDACA,kGACA,+DACA,yJACA,qGACA,+HACA,yGACA,6CACA,0KACA,4IACA,iFACA,sHACA,kDACA,0JACA,kCACA,2EACA,wDACA,wEACA,kFACA,mFACA,oDACA,4JACA,+FACA,6HACA,0CACA,8DACA,wEACA,kIACA,kDACA,uGACA,8HACA,wGACD,EACA,4CAA6C,CAC5C,0EACA,kFACA,oKACA,mEACA,kLACA,oEACA,+BACA,yHACA,2DACA,8CACA,kDACA,qGACA,0DACA,iHACA,sDACA,sDACA,sDACA,sDACA,sDACA,sDACA,6DACA,6DACA,6DACA,6DACA,6DACA,6DACA,sDACA,sEACA,sDACA,mGACA,iCACA,sDACA,6CACA,8CACA,oDACA,mEACA,sHACA,2EACA,sSACA,mDACA,yDACA,kDACA,4IACA,sEACA,0EACA,oFACA,8GACA,8GACA,8GACA,8GACA,8GACA,8GACA,kDACA,qHACA,qHACA,qHACA,qHACA,qHACA,qHACA,qHACA,mHACA,qHACA,mHACA,mHACA,mHACA,yDACA,wDACD,EACA,qCAAsC,CACrC,4DACA,+GACA,sFACA,kCACA,6CACA,wDACA,0DACA,sFACA,4CACA,+EACA,uDACA,mGACA,+CACA,wCACA,wCACA,wDACA,wDACA,4CACA,mDACA,kDACA,0DACA,2DACA,+EACA,qCACA,kEACA,2DACA,8CACA,kDACA,mDACA,uDACA,0DACA,6EACA,6DACA,+DACA,gDACA,sDACA,wDACA,4DACA,2DACA,4EACA,kEACA,mFACD,EACA,6BAA8B,CAC7B,SACA,WACA,QACA,YACA,cACA,iBACA,+BACA,SACA,QACA,UACA,aACA,WACA,QACA,YACA,YACA,qBACA,OACA,YACA,SACA,WACA,UACA,YACA,SACA,aACA,uBACA,WACA,WACD,EACA,2CAA4C,CAC3C,mBACD,EACA,mCAAoC,CACnC,aACD,EACA,qCAAsC,CACrC,qCACA,6BACA,oDACA,qBACA,8BACA,qBACA,sCACA,uBACA,0DACA,kCACA,0CACD,EACA,+CAAgD,CAC/C,uBACA,gBACD,EACA,sDAAuD,CACtD,iCACA,qCACA,+CACA,uCACA,mDACA,4CACD,EACA,4DAA6D,CAC5D,qDACA,iBACA,4BACA,mBACA,mBACA,kFACD,EACA,4DAA6D,CAC5D,6CACA,0CACD,EACA,sDAAuD,CACtD,mBACD,EACA,gDAAiD,CAChD,WACA,SACA,SACA,SACA,WACA,SACA,SACA,SACA,cACA,cACA,YACA,YACA,YACA,UACA,QACA,QACA,QACA,kCACD,EACA,kDAAmD,CAClD,2EACD,EACA,0DAA2D,CAC1D,0DACA,wDACA,8DACA,sEACA,iDACA,2EACA,0BACA,2CACA,iEACA,+DACA,sDACA,2CACA,kBACA,2DACA,gDACA,yDACA,uCACA,mCACA,8DACA,sEACA,oEACA,wCACA,0BACA,gDACA,gBACA,sDACA,iCACA,qCACD,EACA,+DAAgE,CAC/D,mHACA,oKACD,EACA,4DAA6D,CAC5D,kDACA,wBACA,kCACD,EACA,sDAAuD,CACtD,qBACA,0BACA,UACA,YACA,aACA,QACA,gBACA,qBACD,EACA,uDAAwD,CACvD,oFACA,sCACA,gCACA,iCACA,2BACA,8BACD,EACA,wDAAyD,CACxD,iEACA,wBACD,EACA,0DAA2D,CAC1D,8DACA,0CACD,EACA,qEAAsE,CACrE,uDACA,wDACA,iCACA,sDACD,EACA,4CAA6C,CAC5C,kCACA,oCACA,iCACA,gCACA,gCACA,iCACD,EACA,oDAAqD,CACpD,WACA,yBACA,qBACA,eACA,UACA,UACA,qBACA,iBACA,UACA,uCACD,EACA,kDAAmD,CAClD,kBACA,gBACD,EACA,kEAAmE,CAClE,gBACA,8GACD,EACA,gEAAiE,CAChE,oCACA,gCACA,+DACA,8BACA,oCACD,EACA,6DAA8D,CAC7D,YACA,6BACA,gBACA,eACA,2BACA,0BACA,qCACA,kCACD,EACA,uEAAwE,CACvE,uHACD,EACA,qEAAsE,CACrE,2CACA,uCACA,qEACD,EACA,+DAAgE,CAC/D,kHACD,EACA,gDAAiD,CAChD,oFACA,SACA,WACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA,6BACA,0BACA,mBACA,kBACA,uBACA,kDACA,gFACA,qCACA,qCACA,gCACA,+BACA,aACA,cACD,EACA,4CAA6C,CAC5C,8EACA,mFACA,gFACA,kEACA,uEACA,uEACA,wEACA,sBACA,SACA,SACA,wBACA,yBACA,yBACA,SACA,aACA,aACA,aACA,kBACA,sBACA,2HACA,aACA,oBACA,mBACA,4BACA,oCACA,4BACA,mLACD,EACA,4CAA6C,CAC5C,YACA,+BACA,SACA,mBACA,4BACA,yCACA,4BACA,+BACA,yCACA,4CACA,cACA,iBACA,yBACA,kCACA,mCACA,qDACA,oCACA,0BACD,EACA,uDAAwD,CACvD,4HACA,wDACA,+DACA,kEACA,mFACA,+EACD,EACA,8CAA+C,CAC9C,0CACA,0CACA,wDACD,EACA,iDAAkD,CACjD,2BACA,+BACD,EACA,gDAAiD,CAChD,gEACA,uCACA,+DACA,sCACA,4EACA,uBACA,2EACA,qBACD,EACA,sDAAuD,CACtD,QACA,cACA,iBACA,aACA,eACA,uBACA,sBACA,6EACA,qFACA,kFACA,6FACA,uEACA,6FACA,6DACD,EACA,oDAAqD,CACpD,MACA,eACA,qDACA,0CACA,qBACA,uBACA,oCACA,iCACA,gBACA,qDACA,2CACA,qBACA,wBACA,qDACA,2CACA,6BACA,uBACA,6DACA,kDACA,gCACA,kCACA,qCACA,mBACA,yDACA,8CACA,wBACA,0BACA,gCACA,qDACA,gCACA,mBACA,qBACA,cACA,2BACA,cACA,4BACA,cACA,+BACA,aACD,EACA,qEAAsE,CACrE,0CACD,EACA,iEAAkE,CACjE,qIACA,cACA,WACD,EACA,2DAA4D,CAC3D,kBACA,iBACA,aACD,EACA,6DAA8D,CAC7D,6BACA,oBACA,aACD,EACA,uDAAwD,CACvD,8CACA,kDACA,mDACA,sDACA,+BACA,kCACA,qCACA,6CACD,EACA,wDAAyD,CACxD,2FACA,+CACA,uBACD,EACA,wCAAyC,CACxC,6CACA,kGACA,0GACA,6FACA,2EACA,gDACA,+CACA,kDACA,gDACA,2CACA,0CACA,yBACA,+CACD,EACA,2DAA4D,CAC3D,cACA,uJACA,sJACD,EACA,yDAA0D,CACzD,kBACA,6CACA,sCACA,6CACA,yBACD,EACA,0DAA2D,CAC1D,mCACA,mCACD,EACA,oDAAqD,CACpD,mCACA,uCACA,yCACA,4CACA,oCACA,gEACA,sCACA,kCACA,yDACA,oCACA,0CACA,uDACD,EACA,uDAAwD,CACvD,gCACA,aACA,cACA,mBACA,aACA,iGACA,2BACA,kBACD,EACA,uDAAwD,CACvD,wCACA,uCACA,mCACA,uDACA,kBACA,wDACA,mBACA,kCACA,UACA,kCACA,0CACD,EACA,+DAAgE,CAC/D,aACD,EACA,0EAA2E,CAC1E,6CACA,8DACA,wHACA,+DACD,EACA,0EAA2E,CAC1E,+CACD,EACA,2EAA4E,CAC3E,8DACA,6DACA,YACA,WACA,WACD,EACA,wDAAyD,CACxD,mCACD,EACA,4DAA6D,CAC5D,yBACA,2BACA,wBACA,0BACA,yBACA,0BACA,8BACA,0BACA,6BACA,yBACA,wCACA,yCACA,gCACA,yBACA,oBACA,yBACA,gCACA,2BACA,2BACA,+BACA,4CACA,iBACA,6CACA,8BACA,8BACA,4BACA,4BACA,oDACA,2BACD,EACA,wDAAyD,CACxD,+BACA,kFACD,EACA,wCAAyC,CACxC,4EACA,4DACA,mBACA,oBACA,aACA,cACA,mBACA,aACA,0BACA,kBACD,EACA,sDAAuD,CACtD,8DACD,EACA,oDAAqD,CACpD,uBACA,0BACA,wBACA,0BACA,wBACA,yBACA,2CACA,4CACA,yCACA,yCACA,sEACA,oCACA,qEACA,mCACA,8EACA,6EACA,oEACA,uCACA,gCACA,8BACA,6BACA,0BACA,2BACD,EACA,0DAA2D,CAC1D,wCACD,EACA,gEAAiE,CAChE,8DACA,6DACA,kBACA,6EACD,EACA,8CAA+C,CAC9C,kFACA,SACA,wEACA,mDACA,yEACA,gEACA,sEACA,iFACA,oFACA,iGACA,wGACA,uDACA,oEACA,uFACA,qGACA,mFACA,kEACD,EACA,4DAA6D,CAC5D,0DACA,+CACA,wBACA,4GACA,0FACD,EACA,8DAA+D,CAC9D,6FACA,0EACA,sDACA,iCACA,sBACA,6BACA,oBACA,oBACA,mBACA,kBACA,sBACA,kBACA,eACA,oBACA,gBACA,mBACA,mBACA,4BACA,iBACA,8BACA,mBACA,oBACA,sBACA,mCACA,gBACA,iBACA,iBACA,mBACA,kBACA,gBACA,eACA,eACA,kBACD,EACA,yDAA0D,CACzD,mDACA,oDACD,EACA,0CAA2C,CAC1C,8BACA,uEACA,uCACA,iCACA,+DACA,gEACA,8DACA,mCACA,yFACD,EACA,oDAAqD,CACpD,sEACA,4FACA,+DACD,EACA,oDAAqD,CACpD,wBACA,0BACA,0BACA,wBACD,EACA,uDAAwD,CACvD,yEACA,kGACA,0FACA,4CACD,EACA,qDAAsD,CACrD,UACA,QACA,SACA,eACA,SACA,UACA,YACA,MACA,MACA,MACA,SACA,MACA,MACA,SACA,QACA,UACA,QACA,QACA,MACA,QACA,QACA,SACA,aACA,UACA,YACA,YACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KACD,EACA,6DAA8D,CAC7D,qCACA,uCACA,4BACA,8BACA,oCACA,sCACA,gEACA,+DACA,wDACA,uBACD,EACA,4CAA6C,CAC5C,qCACA,+DACA,gDACA,iGACA,yDACA,sFACA,uEACA,4EACD,EACA,sDAAuD,CACtD,0DACA,2BACA,WACA,WACA,aACA,aACA,WACA,gBACA,iBACA,iDACD,EACA,kDAAmD,CAClD,sCACA,sCACA,6CACA,8EACA,0FACA,iEACA,wDACA,oGACA,wDACA,cACA,sBACA,UACA,eACA,UACA,WACA,sBACD,EACA,yDAA0D,CACzD,SACA,aACD,EACA,0DAA2D,CAC1D,wEACA,aACD,EACA,wDAAyD,CACxD,WACD,EACA,oDAAqD,CACpD,sJACA,sJACA,qJACA,qJACA,uJACA,2JACA,0JACA,sKACA,sJACA,qJACA,uJACA,uJACA,0JACA,wJACA,sJACA,6JACA,yJACA,yJACA,kKACA,kJACA,2JACA,sJACA,2JACA,uJACA,yJACA,0JACA,sKACA,sJACA,0JACA,qJACA,sKACA,sJACA,qJACD,EACA,kEAAmE,CAClE,qEACA,qFACA,8EACD,EACA,sDAAuD,CACtD,oDACD,EACA,kEAAmE,CAClE,kFACA,wEACA,6DACA,+DACA,wHACA,kHACA,mCACA,2DACA,8BACA,wCACA,sDACA,oCACA,kDACA,iCACA,mDACA,mCACA,qDACA,yCACA,8DACA,mCACA,wDACA,+BACA,gEACA,0CACD,EACA,0EAA2E,CAC1E,qCACA,sDACA;AAAA;AAAA,0GACA,8CACA,QACD,EACA,iEAAkE,CACjE,sKACA,0KACA,iJACA,yGACA,+GACA,gEACA,2IACA,qKACA,+KACD,EACA,4DAA6D,CAC5D,uCACA,sCACA,yCACD,EACA,0DAA2D,CAC1D,kBACD,EACA,mDAAoD,CACnD,MACA,QACA,SACA,UACA,eACA,eACD,EACA,8CAA+C,CAC9C,2CACA,mBACA,yCACA,qBACD,EACA,gDAAiD,CAChD,mFACA,iDACA,iCACA,mCACA,oCACA,oCACA,2CACD,EACA,sDAAuD,CACtD,YACA,YACA;AAAA,UACD,EACA,sCAAuC,CACtC,UACA,qBACD,EACA,yCAA0C,CACzC,eACD,EACA,gDAAiD,CAChD,uBACA,6BACA,iCACA,0CACA,sCACA,uCACA,oDACA,uDACA,oBACA,oBACA,gCACA,sBACA,qCACA,sCACA,mCACA,mCACA,oCACA,oCACA,6BACA,8BACA,SACA,UACA,SACD,EACA,yDAA0D,CACzD,+DACA,kEACA,sFACA,kEACA,sFACA,kEACA,gDACA,oNACA,qEACA,uFACD,EACA,mDAAoD,CACnD,kEACD,EACA,2CAA4C,CAC3C,6CACA,8IACA,4BACA,8BACA,mBACA,iDACA,mCACA,+CACA;AAAA,iBACD,EACA,4CAA6C,CAC5C,mCACA,mCACA,qCACA,wCACA,6EACA,iCACA,iDACA,6BACA,+DACD,EACA,wCAAyC,CACxC,uBACA,6BACA,kCACA,0CACA,+IACD,EACA,yDAA0D,CACzD,8CACD,EACA,0DAA2D,CAC1D,wDACA,0DACA,2DACA,0DACD,EACA,uCAAwC,CACvC,qBACA,mEACA,iEACA,iWACA,uNACA,6LACA,4FACA,oDACA,mEACA,0EACA,4GACA,sEACA,0GACA,8BACA,oGACA,mJACA,4LACA,4IACA,2IACA,0FACA,4CACA,yCACA,sGACA,wMACA,iFACA,iJACA,+OACD,EACA,qCAAsC,CACrC,QACA,cACA,gBACD,EACA,qDAAsD,CACrD,sBACA,qBACA,uBACA,iBACA,qBACA,WACA,2CACD,EACA,iDAAkD,CACjD,UACD,EACA,4CAA6C,CAC5C,WACA,oEACA,UACA,yCACD,EACA,sDAAuD,CACtD,0CACA,iBACA,oBACA,UACA,gBACA,iBACA,UACD,EACA,gDAAiD,CAChD,mBACD,EACA,iDAAkD,CACjD,6CACD,EACA,yCAA0C,CACzC,yFACA,iHACA,oHACA,2HACA,oEACA,oGACA,6GACA,qHACA,iLACA,uCACA,0DACA,+FACA,wEACA,6DACA,4CACA,4CACA,4DACA,2EACA,0EACA,8BACA,qCACA,8BACA,6DACA,qEACA,+EACA,4EACA,iEACA,8EACA,qFACA,8EACA,2EACA,wFACA,2EACA,qEACA,kFACA,oEACA,8BACA,iCACA,qCACA,8BACA,sCACA,kCACA,+BACA,sDACA,+BACA,iDACA,0CACA,+DACA,sJACA,6JACA,4EACA,mEACA,qFACA,8DACA,2GACA,yHACA,6DACA,yEACA,+HACA,mEACA,8EACA,kIACA,+EACA,+EACA,0DACA,6EACA,wCACA,6BACA,mDACA,6DACA,oFACA,8DACA,6EACA,2HACA,uMACA,uIACA,8IACA,qJACA,kEACA,+DACA,4IACA,mJACA,gJACA,yJACA,uCACA,oDACA,uHACA,yIACA,6EACA,kDACA,4HACA,mHACA,2DACA,mDACA,iIACA,iEACA,0EACA,+EACA,6JACA,sDACA,6DACA,uDACA,4EACA,oCACA,sDACA,+CACA,8EACA,uEACA,6EACA,sEACA,4DACA,8FACA,2EACA,2HACA,uHACA,uHACA,4HACA,+DACA,6DACA,iGACA,+FACA,6CACA,2CACA,iDACA,4HACA,2EACA,kFACA,0EACA,8NACA,qOACA,sOACA,sPACA,+NACA,sOACA,0OACA,iOACA,0NACA,4OACA,qOACA,wOACA,4EACA,wGACA,2FACA,6HACA,mKACA,qIACA,sEACA,2EACA,sEACA,yEACA,sGACA,8EACA,8CACA,8CACA,8DACA,sFACA,6CACA,kDACA,6EACA,8DACA,mGACA,qEACA,+DACA,mGACA,kDACA,6EACA,uFACA,sEACA,mCACA,qDACA,8CACA,4EACA,qEACA,6DACA,6DACA,oGACA,wFACA,4EACA,4EACA,6EACA,qFACA,8FACA,uEACA,2DACA,uEACA,iEACA,qEACA,wJACA,sJACA,yJACA,wJACA,gKACA,8JACA,6FACA,oGACA,qGACA,kHACA,4IACA,0JACA,mEACA,gFACA,kEACA,sDACA,oDACA,+CACA,+BACA,sJACA,8CACA,kEACA,oEACA,uDACA,6DACA,gEACA,uDACA,uEACA,+DACA,+DACA,mEACA,kEACA,gEACA,oEACD,EACA,wCAAyC,CACxC,8GACA,+DACA,kDACA,uDACA,uDACD,EACA,8CAA+C,CAC9C,+EACA,wEACA,uDACA,uDACA,sFACA,8HACA,0HACA,kDACA,6BACA,0BACA,kGACA,6BACA,UACA,KACA,sDACA,sDACA,4FACA,6HACA,yHACA,gGACD,EACA,yCAA0C,CACzC,iCACD,CACD,CAAC", "names": [], "file": "editor.main.nls.es.js"}