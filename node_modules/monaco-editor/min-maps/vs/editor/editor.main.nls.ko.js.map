{"version": 3, "sources": ["out-editor/vs/editor/editor.main.nls.ko.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/editor/editor.main.nls.ko\", {\n\t\"vs/base/browser/ui/actionbar/actionViewItems\": [\n\t\t\"{0}({1})\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInput\": [\n\t\t\"입력\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInputToggles\": [\n\t\t\"대/소문자 구분\",\n\t\t\"단어 단위로\",\n\t\t\"정규식 사용\",\n\t],\n\t\"vs/base/browser/ui/findinput/replaceInput\": [\n\t\t\"입력\",\n\t\t\"대/소문자 보존\",\n\t],\n\t\"vs/base/browser/ui/hover/hoverWidget\": [\n\t\t\"{0}을(를) 사용하여 접근성 보기에서 이를 검사합니다.\",\n\t\t\"현재 키 바인딩을 통해 트리거할 수 없는 접근성 보기 열기 명령을 통해 접근성 보기에서 이를 검사합니다.\",\n\t],\n\t\"vs/base/browser/ui/iconLabel/iconLabelHover\": [\n\t\t\"로드 중...\",\n\t],\n\t\"vs/base/browser/ui/inputbox/inputBox\": [\n\t\t\"오류: {0}\",\n\t\t\"경고: {0}\",\n\t\t\"정보: {0}\",\n\t\t\" 또는 {0} 기록의 경우\",\n\t\t\" ({0} 기록용)\",\n\t\t\"입력이 지워짐\",\n\t],\n\t\"vs/base/browser/ui/keybindingLabel/keybindingLabel\": [\n\t\t\"바인딩 안 됨\",\n\t],\n\t\"vs/base/browser/ui/selectBox/selectBoxCustom\": [\n\t\t\"Box 선택\",\n\t],\n\t\"vs/base/browser/ui/toolbar/toolbar\": [\n\t\t\"기타 작업...\",\n\t],\n\t\"vs/base/browser/ui/tree/abstractTree\": [\n\t\t\"필터\",\n\t\t\"유사 항목 일치\",\n\t\t\"필터링할 형식\",\n\t\t\"입력하여 검색\",\n\t\t\"입력하여 검색\",\n\t\t\"닫기\",\n\t\t\"찾은 요소가 없습니다.\",\n\t],\n\t\"vs/base/common/actions\": [\n\t\t\"(비어 있음)\",\n\t],\n\t\"vs/base/common/errorMessage\": [\n\t\t\"{0}: {1}\",\n\t\t\"시스템 오류가 발생했습니다({0}).\",\n\t\t\"알 수 없는 오류가 발생했습니다. 자세한 내용은 로그를 참조하세요.\",\n\t\t\"알 수 없는 오류가 발생했습니다. 자세한 내용은 로그를 참조하세요.\",\n\t\t\"{0}(총 {1}개의 오류)\",\n\t\t\"알 수 없는 오류가 발생했습니다. 자세한 내용은 로그를 참조하세요.\",\n\t],\n\t\"vs/base/common/keybindingLabels\": [\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"<Alt>\",\n\t\t\"Windows\",\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"<Alt>\",\n\t\t\"슈퍼\",\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"옵션\",\n\t\t\"명령\",\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"<Alt>\",\n\t\t\"Windows\",\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"<Alt>\",\n\t\t\"슈퍼\",\n\t],\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/browser/controller/textAreaHandler\": [\n\t\t\"편집기\",\n\t\t\"현재 편집기에 액세스할 수 없습니다.\",\n\t\t\"{0} 화면 읽기 프로그램 최적화 모드를 사용하도록 설정하려면 {1}\",\n\t\t\"{0} 화면 읽기 프로그램 최적화 모드를 사용하도록 설정하려면 {1}을(를) 사용하여 빠른 선택을 열고 화면 읽기 프로그램 접근성 모드 토글 명령을 실행합니다(이 명령은 현재 키보드를 통해 트리거할 수 없음).\",\n\t\t\"{0} {1}을(를) 사용하여 키 바인딩 편집기에 액세스하여 화면 읽기 프로그램 접근성 모드 토글 명령에 대한 키 바인딩을 할당하고 실행하세요.\",\n\t],\n\t\"vs/editor/browser/coreCommands\": [\n\t\t\"더 긴 줄로 이동하는 경우에도 끝에 고정\",\n\t\t\"더 긴 줄로 이동하는 경우에도 끝에 고정\",\n\t\t\"보조 커서가 제거됨\",\n\t],\n\t\"vs/editor/browser/editorExtensions\": [\n\t\t\"실행 취소(&&U)\",\n\t\t\"실행 취소\",\n\t\t\"다시 실행(&&R)\",\n\t\t\"다시 실행\",\n\t\t\"모두 선택(&&S)\",\n\t\t\"모두 선택\",\n\t],\n\t\"vs/editor/browser/widget/codeEditorWidget\": [\n\t\t\"커서 수를 {0}개로 제한했습니다. 더 큰 변경 내용을 위해서는 [찾아서 교체](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace)를 사용하거나 편집기 다중 커서 제한 설정을 늘리는 것이 좋습니다.\",\n\t\t\"다중 커서 제한 늘리기\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/accessibleDiffViewer\": [\n\t\t\"액세스 가능한 Diff 뷰어의 \\'삽입\\' 아이콘.\",\n\t\t\"액세스 가능한 Diff 뷰어의 \\'제거\\' 아이콘.\",\n\t\t\"접근 가능한 Diff 뷰어의 \\'닫기\\' 아이콘.\",\n\t\t\"닫기\",\n\t\t\"액세스 가능한 Diff 뷰어입니다. 탐색하려면 위쪽 및 아래쪽 화살표를 사용합니다.\",\n\t\t\"변경된 줄 없음\",\n\t\t\"선 1개 변경됨\",\n\t\t\"줄 {0}개 변경됨\",\n\t\t\"차이 {0}/{1}: 원래 줄 {2}, {3}, 수정된 줄 {4}, {5}\",\n\t\t\"비어 있음\",\n\t\t\"{0} 변경되지 않은 줄 {1}\",\n\t\t\"{0} 원래 줄 {1} 수정된 줄 {2}\",\n\t\t\"+ {0} 수정된 줄 {1}\",\n\t\t\"- {0} 원래 줄 {1}\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/colors\": [\n\t\t\"diff 편집기에서 이동된 텍스트의 테두리 색입니다.\",\n\t\t\"diff 편집기에서 이동된 텍스트의 활성 테두리 색입니다.\",\n\t\t\"변경되지 않은 영역 위젯 주위의 그림자 색입니다.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/decorations\": [\n\t\t\"diff 편집기의 삽입에 대한 줄 데코레이션입니다.\",\n\t\t\"diff 편집기의 제거에 대한 줄 데코레이션입니다.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditor.contribution\": [\n\t\t\"변경되지 않은 영역 축소 토글\",\n\t\t\"이동된 코드 블록 표시 토글\",\n\t\t\"공간이 제한된 경우 인라인 보기 사용 설정/해제\",\n\t\t\"공간이 제한된 경우 인라인 보기 사용\",\n\t\t\"이동된 코드 블록 표시\",\n\t\t\"diff 편집기\",\n\t\t\"스위치 쪽\",\n\t\t\"비교 이동 종료\",\n\t\t\"변경되지 않은 모든 영역 축소\",\n\t\t\"변경되지 않은 모든 영역 표시\",\n\t\t\"액세스 가능한 Diff 뷰어\",\n\t\t\"다음 다른 항목으로 이동\",\n\t\t\"액세스 가능한 Diff 뷰어 열기\",\n\t\t\"다음 다른 항목으로 이동\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorDecorations\": [\n\t\t\"선택한 변경 내용 되돌리기\",\n\t\t\"변경 내용 되돌리기\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorEditors\": [\n\t\t\" {0}을(를) 사용하여 접근성 도움말을 엽니다.\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature\": [\n\t\t\"변경되지 않은 영역 접기\",\n\t\t\"위에 자세히 표시하려면 클릭하거나 끌어다 놓기\",\n\t\t\"변경되지 않은 영역 표시\",\n\t\t\"아래에 자세히 표시하려면 클릭하거나 끌어다 놓기\",\n\t\t\"숨겨진 선 {0}개\",\n\t\t\"두 번 클릭하여 펼치기\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin\": [\n\t\t\"삭제된 줄 복사\",\n\t\t\"삭제된 줄 복사\",\n\t\t\"변경된 줄 복사\",\n\t\t\"변경된 줄 복사\",\n\t\t\"삭제된 줄 복사({0})\",\n\t\t\"변경된 줄({0}) 복사\",\n\t\t\"이 변경 내용 되돌리기\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/movedBlocksLines\": [\n\t\t\"변경 사항과 함께 코드가 {0} - {1}줄로 이동됨\",\n\t\t\"변경 사항과 함께 코드가 {0} - {1}줄에서 이동됨\",\n\t\t\"코드가 {0} - {1}줄로 이동됨\",\n\t\t\"코드가 {0} - {1}줄에서 이동됨\",\n\t],\n\t\"vs/editor/browser/widget/multiDiffEditorWidget/colors\": [\n\t\t\"diff 편집기 헤더의 배경색입니다.\",\n\t],\n\t\"vs/editor/common/config/editorConfigurationSchema\": [\n\t\t\"편집기\",\n\t\t\"탭이 같은 공백의 수입니다. 이 설정은 {0}이(가) 켜져 있을 때 파일 내용을 기반으로 재정의됩니다.\",\n\t\t\"들여쓰기 또는 `\\\"tabSize\\\"에서 \\'#editor.tabSize#\\'의 값을 사용하는 데 사용되는 공백 수입니다. 이 설정은 \\'#editor.detectIndentation#\\'이 켜져 있는 경우 파일 내용에 따라 재정의됩니다.\",\n\t\t\"`Tab`을 누를 때 공백을 삽입하세요. 이 설정은 {0}이(가) 켜져 있을 때 파일 내용을 기반으로 재정의됩니다.\",\n\t\t\"파일 내용을 기반으로 파일을 열 때 {0} 및 {1}을(를) 자동으로 감지할지 여부를 제어합니다.\",\n\t\t\"끝에 자동 삽입된 공백을 제거합니다.\",\n\t\t\"큰 파일에 대한 특수 처리로, 메모리를 많이 사용하는 특정 기능을 사용하지 않도록 설정합니다.\",\n\t\t\"단어 기반 추천을 끕니다.\",\n\t\t\"활성 문서에서만 단어를 제안합니다.\",\n\t\t\"같은 언어의 모든 열린 문서에서 단어를 제안합니다.\",\n\t\t\"모든 열린 문서에서 단어를 제안합니다.\",\n\t\t\"문서의 단어를 기준으로 완성도를 계산해야 하는지 여부 및 완성도가 계산되는 문서를 기준으로 계산되는지 여부를 제어합니다.\",\n\t\t\"모든 색 테마에 대해 의미 체계 강조 표시를 사용합니다.\",\n\t\t\"모든 색 테마에 대해 의미 체계 강조 표시를 사용하지 않습니다.\",\n\t\t\"의미 체계 강조 표시는 현재 색 테마의 `semanticHighlighting` 설정에 따라 구성됩니다.\",\n\t\t\"semanticHighlighting이 지원하는 언어에 대해 표시되는지 여부를 제어합니다.\",\n\t\t\"해당 콘텐츠를 두 번 클릭하거나 \\'Esc\\' 키를 누르더라도 Peek 편집기를 열린 상태로 유지합니다.\",\n\t\t\"이 길이를 초과하는 줄은 성능상의 이유로 토큰화되지 않습니다.\",\n\t\t\"웹 작업자에서 토큰화가 비동기적으로 수행되어야 하는지 여부를 제어합니다.\",\n\t\t\"비동기 토큰화가 기록되어야 하는지 여부를 제어합니다. 디버깅 전용입니다.\",\n\t\t\"레거시 백그라운드 토큰화에 대해 비동기 토큰화를 확인해야 하는지 여부를 제어합니다. 토큰화 속도가 느려질 수 있습니다. 디버깅 전용입니다.\",\n\t\t\"들여쓰기를 늘리거나 줄이는 대괄호 기호를 정의합니다.\",\n\t\t\"여는 대괄호 문자 또는 문자열 시퀀스입니다.\",\n\t\t\"닫는 대괄호 문자 또는 문자열 시퀀스입니다.\",\n\t\t\"대괄호 쌍 색 지정을 사용하는 경우 중첩 수준에 따라 색이 지정된 대괄호 쌍을 정의합니다.\",\n\t\t\"여는 대괄호 문자 또는 문자열 시퀀스입니다.\",\n\t\t\"닫는 대괄호 문자 또는 문자열 시퀀스입니다.\",\n\t\t\"diff 계산이 취소된 후 밀리초 단위로 시간을 제한합니다. 제한 시간이 없는 경우 0을 사용합니다.\",\n\t\t\"차이를 계산할 최대 파일 크기(MB)입니다. 제한이 없으면 0을 사용합니다.\",\n\t\t\"diff 편집기에서 diff를 나란히 표시할지 인라인으로 표시할지를 제어합니다.\",\n\t\t\"diff 편집기 너비가 이 값보다 작으면 인라인 뷰가 사용됩니다.\",\n\t\t\"사용하도록 설정하고 편집기 너비가 너무 작을 경우 인라인 보기가 사용됩니다.\",\n\t\t\"활성화되면 diff 편집기는 변경 내용을 되돌리기 위해 글리프 여백에 화살표를 표시합니다.\",\n\t\t\"사용하도록 설정하면 Diff 편집기가 선행 또는 후행 공백의 변경 내용을 무시합니다.\",\n\t\t\"diff 편집기에서 추가/제거된 변경 내용에 대해 +/- 표시기를 표시하는지 여부를 제어합니다.\",\n\t\t\"편집기에서 CodeLens를 표시할 것인지 여부를 제어합니다.\",\n\t\t\"줄이 바뀌지 않습니다.\",\n\t\t\"뷰포트 너비에서 줄이 바뀝니다.\",\n\t\t\"줄은 {0} 설정에 따라 줄 바꿈됩니다.\",\n\t\t\"레거시 비교 알고리즘을 사용합니다.\",\n\t\t\"고급 비교 알고리즘을 사용합니다.\",\n\t\t\"diff 편집기에 변경되지 않은 영역이 표시되는지 여부를 제어합니다.\",\n\t\t\"변경되지 않은 영역에 사용되는 줄 수를 제어합니다.\",\n\t\t\"변경되지 않은 영역의 최소값으로 사용되는 줄 수를 제어합니다.\",\n\t\t\"변경되지 않은 영역을 비교할 때 컨텍스트로 사용되는 줄 수를 제어합니다.\",\n\t\t\"diff 편집기에서 감지된 코드 이동을 표시할지 여부를 제어합니다.\",\n\t\t\"문자가 삽입되거나 삭제된 위치를 볼 수 있도록 diff 편집기에 빈 장식적 요소를 표시할지 여부를 제어합니다.\",\n\t],\n\t\"vs/editor/common/config/editorOptions\": [\n\t\t\"플랫폼 API를 사용하여 화면 읽기 프로그램이 연결된 시기를 감지합니다.\",\n\t\t\"화면 읽기 프로그램을 사용하여 사용을 최적화합니다.\",\n\t\t\"화면 읽기 프로그램이 연결되어 있지 않다고 가정합니다.\",\n\t\t\"화면 판독기에 최적화된 모드에서 UI를 실행해야 하는지 여부를 제어합니다.\",\n\t\t\"주석을 달 때 공백 문자를 삽입할지 여부를 제어합니다.\",\n\t\t\"빈 줄을 줄 주석에 대한 토글, 추가 또는 제거 작업으로 무시해야 하는지를 제어합니다.\",\n\t\t\"선택 영역 없이 현재 줄 복사 여부를 제어합니다.\",\n\t\t\"입력하는 동안 일치 항목을 찾기 위한 커서 이동 여부를 제어합니다.\",\n\t\t\"편집기 선택 영역에서 검색 문자열을 시드하지 마세요.\",\n\t\t\"커서 위치의 단어를 포함하여 항상 편집기 선택 영역에서 검색 문자열을 시드합니다.\",\n\t\t\"편집기 선택 영역에서만 검색 문자열을 시드하세요.\",\n\t\t\"편집기 선택에서 Find Widget의 검색 문자열을 시딩할지 여부를 제어합니다.\",\n\t\t\"선택 영역에서 찾기를 자동으로 켜지 않습니다(기본값).\",\n\t\t\"선택 영역에서 찾기를 항상 자동으로 켭니다.\",\n\t\t\"여러 줄의 콘텐츠를 선택하면 선택 항목에서 찾기가 자동으로 켜집니다.\",\n\t\t\"선택 영역에서 찾기를 자동으로 설정하는 조건을 제어합니다.\",\n\t\t\"macOS에서 Find Widget이 공유 클립보드 찾기를 읽을지 수정할지 제어합니다.\",\n\t\t\"위젯 찾기에서 편집기 맨 위에 줄을 추가해야 하는지 여부를 제어합니다. true인 경우 위젯 찾기가 표시되면 첫 번째 줄 위로 스크롤할 수 있습니다.\",\n\t\t\"더 이상 일치하는 항목이 없을 때 검색을 처음이나 끝에서 자동으로 다시 시작할지 여부를 제어합니다.\",\n\t\t\"글꼴 합자(\\'calt\\' 및 \\'liga\\' 글꼴 기능)를 사용하거나 사용하지 않도록 설정합니다. \\'font-feature-settings\\' CSS 속성의 세분화된 제어를 위해 문자열로 변경합니다.\",\n\t\t\"명시적 \\'font-feature-settings\\' CSS 속성입니다. 합자를 켜거나 꺼야 하는 경우에만 부울을 대신 전달할 수 있습니다.\",\n\t\t\"글꼴 합자 또는 글꼴 기능을 구성합니다. CSS \\'font-feature-settings\\' 속성의 값에 대해 합자 또는 문자열을 사용하거나 사용하지 않도록 설정하기 위한 부울일 수 있습니다.\",\n\t\t\"font-weight에서 font-variation-settings로 변환을 사용/사용하지 않습니다. \\'font-variation-settings\\' CSS 속성의 세분화된 컨트롤을 위해 이를 문자열로 변경합니다.\",\n\t\t\"명시적 \\'font-variation-settings\\' CSS 속성입니다. font-weight만 font-variation-settings로 변환해야 하는 경우 부울을 대신 전달할 수 있습니다.\",\n\t\t\"글꼴 변형을 구성합니다. font-weight에서 font-variation-settings로 변환을 사용/사용하지 않도록 설정하는 부울이거나 CSS \\'font-variation-settings\\' 속성 값에 대한 문자열일 수 있습니다.\",\n\t\t\"글꼴 크기(픽셀)를 제어합니다.\",\n\t\t\"\\\"표준\\\" 및 \\\"굵게\\\" 키워드 또는 1~1000 사이의 숫자만 허용됩니다.\",\n\t\t\"글꼴 두께를 제어합니다. \\\"표준\\\" 및 \\\"굵게\\\" 키워드 또는 1~1000 사이의 숫자를 허용합니다.\",\n\t\t\"결과의 Peek 보기 표시(기본값)\",\n\t\t\"기본 결과로 이동하여 Peek 보기를 표시합니다.\",\n\t\t\"기본 결과로 이동하여 다른 항목에 대해 Peek 없는 탐색을 사용하도록 설정합니다.\",\n\t\t\"이 설정은 더 이상 사용되지 않습니다. 대신 \\'editor.editor.gotoLocation.multipleDefinitions\\' 또는 \\'editor.editor.gotoLocation.multipleImplementations\\'와 같은 별도의 설정을 사용하세요.\",\n\t\t\"여러 대상 위치가 있는 경우 \\'정의로 이동\\' 명령 동작을 제어합니다.\",\n\t\t\"여러 대상 위치가 있는 경우 \\'유형 정의로 이동\\' 명령 동작을 제어합니다.\",\n\t\t\"여러 대상 위치가 있는 경우 \\'Go to Declaration\\' 명령 동작을 제어합니다.\",\n\t\t\"여러 대상 위치가 있는 경우 \\'구현으로 이동\\' 명령 동작을 제어합니다.\",\n\t\t\"여러 대상 위치가 있는 경우 \\'참조로 이동\\' 명령 동작을 제어합니다.\",\n\t\t\"\\'정의로 이동\\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\n\t\t\"\\'형식 정의로 이동\\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\n\t\t\"\\'선언으로 이동\\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\n\t\t\"\\'구현으로 이동\\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\n\t\t\"\\'참조로 이동\\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\n\t\t\"호버 표시 여부를 제어합니다.\",\n\t\t\"호버가 표시되기 전까지의 지연 시간(밀리초)을 제어합니다.\",\n\t\t\"마우스를 해당 항목 위로 이동할 때 호버를 계속 표시할지 여부를 제어합니다.\",\n\t\t\"호버가 숨겨지기 전까지의 지연 시간(밀리초)을 제어합니다. \\'editor.hover.sticky\\'를 사용하도록 설정해야 합니다.\",\n\t\t\"공백이 있는 경우 선 위에 마우스를 가져가는 것을 표시하는 것을 선호합니다.\",\n\t\t\"모든 문자가 동일한 너비라고 가정합니다. 이 알고리즘은 고정 폭 글꼴과 문자 모양의 너비가 같은 특정 스크립트(예: 라틴 문자)에 적절히 작동하는 빠른 알고리즘입니다.\",\n\t\t\"래핑 점 계산을 브라우저에 위임합니다. 이 알고리즘은 매우 느려서 대용량 파일의 경우 중단될 수 있지만 모든 경우에 적절히 작동합니다.\",\n\t\t\"래핑 지점을 계산하는 알고리즘을 제어합니다. 접근성 모드에서는 최상의 환경을 위해 고급 기능이 사용됩니다.\",\n\t\t\"편집기에서 코드 동작 전구를 사용하도록 설정합니다.\",\n\t\t\"AI 아이콘을 표시하지 않습니다.\",\n\t\t\"코드 작업 메뉴에 AI 작업이 포함되어 있지만 코드에서만 AI 아이콘을 표시합니다.\",\n\t\t\"코드 작업 메뉴에 AI 작업이 포함된 경우 코드 및 빈 줄에 AI 아이콘을 표시합니다.\",\n\t\t\"코드 작업 메뉴에 AI 작업이 포함된 경우 AI 아이콘을 전구와 함께 표시합니다.\",\n\t\t\"편집기 위쪽에서 스크롤하는 동안 중첩된 현재 범위를 표시합니다.\",\n\t\t\"표시할 최대 고정 선 수를 정의합니다.\",\n\t\t\"고정할 줄을 결정하는 데 사용할 모델을 정의합니다. 개요 모델이 없으면 들여쓰기 모델에 해당하는 접기 공급자 모델에서 대체됩니다. 이 순서는 세 가지 경우 모두 적용됩니다.\",\n\t\t\"편집기의 가로 스크롤 막대를 사용하여 고정 스크롤 스크롤을 사용하도록 설정합니다.\",\n\t\t\"편집기에서 인레이 힌트를 사용하도록 설정합니다.\",\n\t\t\"인레이 힌트를 사용할 수 있음\",\n\t\t\"인레이 힌트는 기본적으로 표시되고 {0}을(를) 길게 누를 때 숨겨집니다.\",\n\t\t\"인레이 힌트는 기본값으로 숨겨져 있으며 {0}을(를) 길게 누르면 표시됩니다.\",\n\t\t\"인레이 힌트는 사용할 수 없음\",\n\t\t\"편집기에서 인레이 힌트의 글꼴 크기를 제어합니다. 기본적으로 {0}은(는) 구성된 값이 {1}보다 작거나 편집기 글꼴 크기보다 큰 경우에 사용됩니다.\",\n\t\t\"편집기에서 인레이 힌트의 글꼴 패밀리를 제어합니다. 비워 두면 {0}이(가) 사용됩니다.\",\n\t\t\"편집기에서 인레이 힌트 주위의 패딩을 사용하도록 설정합니다.\",\n\t\t\"선 높이를 제어합니다. \\r\\n - 0을 사용하여 글꼴 크기에서 줄 높이를 자동으로 계산합니다.\\r\\n - 0에서 8 사이의 값은 글꼴 크기의 승수로 사용됩니다.\\r\\n - 8보다 크거나 같은 값이 유효 값으로 사용됩니다.\",\n\t\t\"미니맵 표시 여부를 제어합니다.\",\n\t\t\"미니맵을 자동으로 숨길지 여부를 제어합니다.\",\n\t\t\"미니맵의 크기는 편집기 내용과 동일하며 스크롤할 수 있습니다.\",\n\t\t\"편집기의 높이를 맞추기 위해 필요에 따라 미니맵이 확장되거나 축소됩니다(스크롤 없음).\",\n\t\t\"미니맵을 편집기보다 작게 유지할 수 있도록 필요에 따라 미니맵이 축소됩니다(스크롤 없음).\",\n\t\t\"미니맵의 크기를 제어합니다.\",\n\t\t\"미니맵을 렌더링할 측면을 제어합니다.\",\n\t\t\"미니맵 슬라이더가 표시되는 시기를 제어합니다.\",\n\t\t\"미니맵에 그려진 콘텐츠의 배율: 1, 2 또는 3.\",\n\t\t\"줄의 실제 문자(색 블록 아님)를 렌더링합니다.\",\n\t\t\"최대 특정 수의 열을 렌더링하도록 미니맵의 너비를 제한합니다.\",\n\t\t\"편집기의 위쪽 가장자리와 첫 번째 줄 사이의 공백을 제어합니다.\",\n\t\t\"편집기의 아래쪽 가장자리와 마지막 줄 사이의 공백을 제어합니다.\",\n\t\t\"입력과 동시에 매개변수 문서와 유형 정보를 표시하는 팝업을 사용하도록 설정합니다.\",\n\t\t\"매개변수 힌트 메뉴의 주기 혹은 목록의 끝에 도달하였을때 종료할 것인지 여부를 결정합니다.\",\n\t\t\"제안 위젯 내부에 빠른 제안이 표시됩니다.\",\n\t\t\"빠른 제안이 유령 텍스트로 표시됨\",\n\t\t\"빠른 제안이 사용 중지되었습니다.\",\n\t\t\"문자열 내에서 빠른 제안을 사용합니다.\",\n\t\t\"주석 내에서 빠른 제안을 사용합니다.\",\n\t\t\"문자열 및 주석 외부에서 빠른 제안을 사용합니다.\",\n\t\t\"입력하는 동안 제안을 자동으로 표시할지 여부를 제어합니다. 이것은 주석, 문자열 및 기타 코드를 입력하기 위해 제어할 수 있습니다. 빠른 제안은 고스트 텍스트 또는 제안 위젯으로 표시하도록 구성할 수 있습니다. 또한 제안이 특수 문자에 의해 실행되는지 여부를 제어하는 \\'{0}\\'-설정에 유의하세요.\",\n\t\t\"줄 번호는 렌더링되지 않습니다.\",\n\t\t\"줄 번호는 절대값으로 렌더링 됩니다.\",\n\t\t\"줄 번호는 커서 위치에서 줄 간격 거리로 렌더링 됩니다.\",\n\t\t\"줄 번호는 매 10 줄마다 렌더링이 이루어집니다.\",\n\t\t\"줄 번호의 표시 여부를 제어합니다.\",\n\t\t\"이 편집기 눈금자에서 렌더링할 고정 폭 문자 수입니다.\",\n\t\t\"이 편집기 눈금자의 색입니다.\",\n\t\t\"특정 수의 고정 폭 문자 뒤에 세로 눈금자를 렌더링합니다. 여러 눈금자의 경우 여러 값을 사용합니다. 배열이 비어 있는 경우 눈금자가 그려지지 않습니다.\",\n\t\t\"세로 스크롤 막대는 필요한 경우에만 표시됩니다.\",\n\t\t\"세로 스크롤 막대가 항상 표시됩니다.\",\n\t\t\"세로 스크롤 막대를 항상 숨깁니다.\",\n\t\t\"세로 스크롤 막대의 표시 유형을 제어합니다.\",\n\t\t\"가로 스크롤 막대는 필요한 경우에만 표시됩니다.\",\n\t\t\"가로 스크롤 막대가 항상 표시됩니다.\",\n\t\t\"가로 스크롤 막대를 항상 숨깁니다.\",\n\t\t\"가로 스크롤 막대의 표시 유형을 제어합니다.\",\n\t\t\"세로 스크롤 막대의 너비입니다.\",\n\t\t\"가로 스크롤 막대의 높이입니다.\",\n\t\t\"클릭이 페이지별로 스크롤되는지 또는 클릭 위치로 이동할지 여부를 제어합니다.\",\n\t\t\"설정하면 가로 스크롤 막대가 편집기 콘텐츠의 크기를 늘리지 않습니다.\",\n\t\t\"기본이 아닌 모든 ASCII 문자를 강조 표시할지 여부를 제어합니다. U+0020과 U+007E 사이의 문자, 탭, 줄 바꿈 및 캐리지 리턴만 기본 ASCII로 간주됩니다.\",\n\t\t\"공백만 예약하거나 너비가 전혀 없는 문자를 강조 표시할지 여부를 제어합니다.\",\n\t\t\"현재 사용자 로캘에서 공통되는 문자를 제외한 기본 ASCII 문자와 혼동할 수 있는 문자를 강조 표시할지 여부를 제어합니다.\",\n\t\t\"주석의 문자에도 유니코드 강조 표시를 적용해야 하는지 여부를 제어합니다.\",\n\t\t\"문자열의 문자에도 유니코드 강조 표시를 적용해야 하는지 여부를 제어합니다.\",\n\t\t\"강조 표시되지 않는 허용된 문자를 정의합니다.\",\n\t\t\"허용된 로캘에서 공통적인 유니코드 문자는 강조 표시되지 않습니다.\",\n\t\t\"편집기에서 인라인 제안을 자동으로 표시할지 여부를 제어합니다.\",\n\t\t\"인라인 추천을 표시힐 때마다 인라인 추천 도구 모음을 표시합니다.\",\n\t\t\"인라인 추천을 마우스로 가리키면 인라인 추천 도구 모음을 표시합니다.\",\n\t\t\"인라인 추천 도구 모음을 표시하지 않습니다.\",\n\t\t\"인라인 추천 도구 모음을 표시할 시기를 제어합니다.\",\n\t\t\"인라인 제안이 제안 위젯과 상호 작용하는 방법을 제어합니다. 사용하도록 설정하면 인라인 제안을 사용할 수 있을 때 제안 위젯이 자동으로 표시되지 않습니다.\",\n\t\t\"대괄호 쌍 색 지정을 사용할지 여부를 제어합니다. {0}을(를) 사용하여 대괄호 강조 색을 재정의합니다.\",\n\t\t\"각 대괄호 형식에 고유한 독립적인 색 풀이 있는지 여부를 제어합니다.\",\n\t\t\"대괄호 쌍 가이드를 사용하도록 설정합니다.\",\n\t\t\"활성 대괄호 쌍에 대해서만 대괄호 쌍 가이드를 사용하도록 설정합니다.\",\n\t\t\"대괄호 쌍 가이드를 비활성화합니다.\",\n\t\t\"대괄호 쌍 안내선의 사용 여부를 제어합니다.\",\n\t\t\"수직 대괄호 쌍 가이드에 추가하여 수평 가이드를 사용하도록 설정합니다.\",\n\t\t\"활성 대괄호 쌍에 대해서만 수평 가이드를 사용하도록 설정합니다.\",\n\t\t\"수평 대괄호 쌍 가이드를 비활성화합니다.\",\n\t\t\"가로 대괄호 쌍 안내선의 사용 여부를 제어합니다.\",\n\t\t\"편집기가 활성 브래킷 쌍을 강조 표시해야 하는지 여부를 제어합니다.\",\n\t\t\"편집기에서 들여쓰기 가이드를 렌더링할지를 제어합니다.\",\n\t\t\"활성 들여쓰기 안내선을 강조 표시합니다.\",\n\t\t\"브래킷 안내선이 강조 표시된 경우에도 활성 들여쓰기 안내선을 강조 표시합니다.\",\n\t\t\"활성 들여쓰기 안내선을 강조 표시하지 마세요.\",\n\t\t\"편집기에서 활성 들여쓰기 가이드를 강조 표시할지 여부를 제어합니다.\",\n\t\t\"커서의 텍스트 오른쪽을 덮어 쓰지않고 제안을 삽입합니다.\",\n\t\t\"제안을 삽입하고 커서의 오른쪽 텍스트를 덮어씁니다.\",\n\t\t\"완료를 수락할 때 단어를 덮어쓸지 여부를 제어합니다. 이것은 이 기능을 선택하는 확장에 따라 다릅니다.\",\n\t\t\"제안 필터링 및 정렬에서 작은 오타를 설명하는지 여부를 제어합니다.\",\n\t\t\"정렬할 때 커서 근처에 표시되는 단어를 우선할지를 제어합니다.\",\n\t\t\"저장된 제안 사항 선택 항목을 여러 작업 영역 및 창에서 공유할 것인지 여부를 제어합니다(`#editor.suggestSelection#` 필요).\",\n\t\t\"IntelliSense를 자동으로 트리거할 때 항상 제안을 선택합니다.\",\n\t\t\"IntelliSense를 자동으로 트리거할 때 제안을 선택하지 마세요.\",\n\t\t\"트리거 문자에서 IntelliSense를 트리거할 때만 제안을 선택합니다.\",\n\t\t\"입력할 때 IntelliSense를 트리거할 때만 제안을 선택합니다.\",\n\t\t\"위젯이 표시될 때 제안을 선택할지 여부를 제어합니다. 이는 자동으로 트리거된 제안(\\'#editor.quickSuggestions#\\' 및 \\'#editor.suggestOnTriggerCharacters#\\')에만 적용되며, 제안이 명시적으로 호출될 때 항상 선택됩니다(예: \\'Ctrl+Space\\'를 통해).\",\n\t\t\"활성 코드 조각이 빠른 제안을 방지하는지 여부를 제어합니다.\",\n\t\t\"제안의 아이콘을 표시할지 여부를 제어합니다.\",\n\t\t\"제안 위젯 하단의 상태 표시줄 가시성을 제어합니다.\",\n\t\t\"편집기에서 제안 결과를 미리볼지 여부를 제어합니다.\",\n\t\t\"제안 세부 정보가 레이블과 함께 인라인에 표시되는지 아니면 세부 정보 위젯에만 표시되는지를 제어합니다.\",\n\t\t\"이 설정은 더 이상 사용되지 않습니다. 이제 제안 위젯의 크기를 조정할 수 있습니다.\",\n\t\t\"이 설정은 더 이상 사용되지 않습니다. 대신 \\'editor.suggest.showKeywords\\'또는 \\'editor.suggest.showSnippets\\'와 같은 별도의 설정을 사용하세요.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 `메서드` 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'함수\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'생성자\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'사용되지 않음\\' 제안이 표시됩니다.\",\n\t\t\"IntelliSense 필터링을 활성화하면 첫 번째 문자가 단어 시작 부분과 일치해야 합니다(예: `c`의 경우 `Console` 또는 `WebContext`가 될 수 있으며 `description`은 _안 됨_). 비활성화하면 IntelliSense가 더 많은 결과를 표시하지만 여전히 일치 품질을 기준으로 정렬합니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'필드\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'변수\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'클래스\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'구조\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'인터페이스\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'모듈\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'속성\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'이벤트\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 `연산자` 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'단위\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'값\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'상수\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'열거형\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 `enumMember` 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'키워드\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'텍스트\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'색\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 `파일` 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'참조\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'사용자 지정 색\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'폴더\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정된 경우 IntelliSense에 \\'typeParameter\\' 제안이 표시됩니다.\",\n\t\t\"사용하도록 설정되면 IntelliSense에 \\'코드 조각\\' 제안이 표시됩니다.\",\n\t\t\"IntelliSense를 사용하도록 설정하면 `user`-제안이 표시됩니다.\",\n\t\t\"IntelliSense를 사용하도록 설정한 경우 `issues`-제안을 표시합니다.\",\n\t\t\"선행 및 후행 공백을 항상 선택해야 하는지 여부입니다.\",\n\t\t\"하위 단어(예: \\'fooBar\\'의 \\'foo\\' 또는 \\'foo_bar\\')를 선택해야 하는지 여부입니다.\",\n\t\t\"들여쓰기가 없습니다. 줄 바꿈 행이 열 1에서 시작됩니다.\",\n\t\t\"줄 바꿈 행의 들여쓰기가 부모와 동일합니다.\",\n\t\t\"줄 바꿈 행이 부모 쪽으로 +1만큼 들여쓰기됩니다.\",\n\t\t\"줄 바꿈 행이 부모 쪽으로 +2만큼 들여쓰기됩니다.\",\n\t\t\"줄 바꿈 행의 들여쓰기를 제어합니다.\",\n\t\t\"편집기에서 파일을 여는 대신 `shift`를 누른 채 파일을 텍스트 편집기로 끌어서 놓을 수 있는지 여부를 제어합니다.\",\n\t\t\"편집기에 파일을 끌어 놓을 때 위젯을 표시할지 여부를 제어합니다. 이 위젯을 사용하면 파일을 드롭하는 방법을 제어할 수 있습니다.\",\n\t\t\"파일이 편집기에 드롭된 후 드롭 선택기 위젯을 표시합니다.\",\n\t\t\"드롭 선택기 위젯을 표시하지 않습니다. 대신 기본 드롭 공급자가 항상 사용됩니다.\",\n\t\t\"콘텐츠를 다른 방법으로 붙여넣을 수 있는지 여부를 제어합니다.\",\n\t\t\"콘텐츠를 편집기에 붙여넣을 때 위젯을 표시할지 여부를 제어합니다. 이 위젯을 사용하여 파일을 붙여넣는 방법을 제어할 수 있습니다.\",\n\t\t\"콘텐츠를 편집기에 붙여넣은 후 붙여넣기 선택기 위젯을 표시합니다.\",\n\t\t\"붙여넣기 선택기 위젯을 표시하지 않습니다. 대신 기본 붙여넣기 동작이 항상 사용됩니다.\",\n\t\t\"커밋 문자에 대한 제안을 허용할지를 제어합니다. 예를 들어 JavaScript에서는 세미콜론(\\';\\')이 제안을 허용하고 해당 문자를 입력하는 커밋 문자일 수 있습니다.\",\n\t\t\"텍스트를 변경할 때 `Enter` 키를 사용한 제안만 허용합니다.\",\n\t\t\"\\'Tab\\' 키 외에 \\'Enter\\' 키에 대한 제안도 허용할지를 제어합니다. 새 줄을 삽입하는 동작과 제안을 허용하는 동작 간의 모호함을 없앨 수 있습니다.\",\n\t\t\"화면 읽기 프로그램에서 한 번에 읽을 수 있는 편집기 줄 수를 제어합니다. 화면 읽기 프로그램을 검색하면 기본값이 500으로 자동 설정됩니다. 경고: 기본값보다 큰 수의 경우 성능에 영향을 미칩니다.\",\n\t\t\"편집기 콘텐츠\",\n\t\t\"화면 읽기 프로그램에서 인라인 제안을 발표하는지 여부를 제어합니다.\",\n\t\t\"언어 구성을 사용하여 대괄호를 자동으로 닫을 경우를 결정합니다.\",\n\t\t\"커서가 공백의 왼쪽에 있는 경우에만 대괄호를 자동으로 닫습니다.\",\n\t\t\"사용자가 여는 괄호를 추가한 후 편집기에서 괄호를 자동으로 닫을지 여부를 제어합니다.\",\n\t\t\"언어 구성을 사용하여 주석을 자동으로 닫을 경우를 결정합니다.\",\n\t\t\"커서가 공백의 왼쪽에 있는 경우에만 주석을 자동으로 닫습니다.\",\n\t\t\"사용자가 여는 주석을 추가한 후 편집기에서 주석을 자동으로 닫을지 여부를 제어합니다.\",\n\t\t\"인접한 닫는 따옴표 또는 대괄호가 자동으로 삽입된 경우에만 제거합니다.\",\n\t\t\"삭제할 때 편집기에서 인접한 닫는 따옴표 또는 대괄호를 제거해야 할지를 제어합니다.\",\n\t\t\"닫기 따옴표 또는 대괄호가 자동으로 삽입된 경우에만 해당 항목 위에 입력합니다.\",\n\t\t\"편집자가 닫는 따옴표 또는 대괄호 위에 입력할지 여부를 제어합니다.\",\n\t\t\"언어 구성을 사용하여 따옴표를 자동으로 닫을 경우를 결정합니다.\",\n\t\t\"커서가 공백의 왼쪽에 있는 경우에만 따옴표를 자동으로 닫습니다.\",\n\t\t\"사용자가 여는 따옴표를 추가한 후 편집기에서 따옴표를 자동으로 닫을지 여부를 제어합니다.\",\n\t\t\"편집기는 들여쓰기를 자동으로 삽입하지 않습니다.\",\n\t\t\"편집기는 현재 줄의 들여쓰기를 유지합니다.\",\n\t\t\"편집기는 현재 줄의 들여쓰기를 유지하고 언어 정의 대괄호를 사용합니다.\",\n\t\t\"편집기는 현재 줄의 들여쓰기를 유지하고 언어 정의 대괄호를 존중하며 언어별로 정의된 특별 EnterRules를 호출합니다.\",\n\t\t\"편집기는 현재 줄의 들여쓰기를 유지하고, 언어 정의 대괄호를 존중하고, 언어에 의해 정의된 특별 EnterRules를 호출하고, 언어에 의해 정의된 들여쓰기 규칙을 존중합니다.\",\n\t\t\"사용자가 줄을 입력, 붙여넣기, 이동 또는 들여쓰기 할 때 편집기에서 들여쓰기를 자동으로 조정하도록 할지 여부를 제어합니다.\",\n\t\t\"언어 구성을 사용하여 선택 항목을 자동으로 둘러쌀 경우를 결정합니다.\",\n\t\t\"대괄호가 아닌 따옴표로 둘러쌉니다.\",\n\t\t\"따옴표가 아닌 대괄호로 둘러쌉니다.\",\n\t\t\"따옴표 또는 대괄호 입력 시 편집기가 자동으로 선택 영역을 둘러쌀지 여부를 제어합니다.\",\n\t\t\"들여쓰기에 공백을 사용할 때 탭 문자의 선택 동작을 에뮬레이트합니다. 선택 영역이 탭 정지에 고정됩니다.\",\n\t\t\"편집기에서 CodeLens를 표시할 것인지 여부를 제어합니다.\",\n\t\t\"CodeLens의 글꼴 패밀리를 제어합니다.\",\n\t\t\"CodeLens의 글꼴 크기(픽셀)를 제어합니다. 0으로 설정하면 `#editor.fontSize#`의 90%가 사용됩니다.\",\n\t\t\"편집기에서 인라인 색 데코레이터 및 색 선택을 렌더링할지를 제어합니다.\",\n\t\t\"색 데코레이터를 클릭하고 마우스로 가리킬 때 색 선택기를 표시합니다.\",\n\t\t\"색 데코레이터를 마우스로 가리키면 색 선택기가 표시되도록 설정\",\n\t\t\"색 데코레이터를 클릭할 때 색 선택기를 표시합니다.\",\n\t\t\"색 데코레이터에서 색 선택기를 표시하도록 조건을 제어합니다.\",\n\t\t\"편집기에서 한 번에 렌더링할 수 있는 최대 색 데코레이터 수를 제어합니다.\",\n\t\t\"마우스와 키로 선택한 영역에서 열을 선택하도록 설정합니다.\",\n\t\t\"구문 강조 표시를 클립보드로 복사할지 여부를 제어합니다.\",\n\t\t\"커서 애니메이션 스타일을 제어합니다.\",\n\t\t\"부드러운 캐럿 애니메이션을 사용할 수 없습니다.\",\n\t\t\"부드러운 캐럿 애니메이션은 사용자가 명시적 제스처를 사용하여 커서를 이동할 때만 사용됩니다.\",\n\t\t\"부드러운 캐럿 애니메이션은 항상 사용됩니다.\",\n\t\t\"매끄러운 캐럿 애니메이션의 사용 여부를 제어합니다.\",\n\t\t\"커서 스타일을 제어합니다.\",\n\t\t\"커서 주변에 표시되는 선행 줄(최소 0)과 후행 줄(최소 1)의 최소 수를 제어합니다. 일부 다른 편집기에서는 \\'scrollOff\\' 또는 \\'scrollOffset\\'으로 알려져 있습니다.\",\n\t\t\"\\'cursorSurroundingLines\\'는 키보드 나 API를 통해 트리거될 때만 적용됩니다.\",\n\t\t\"`cursorSurroundingLines`는 항상 적용됩니다.\",\n\t\t\"\\'#cursorSurroundingLines#\\'를 적용해야 하는 경우를 제어합니다.\",\n\t\t\"`#editor.cursorStyle#` 설정이 \\'line\\'으로 설정되어 있을 때 커서의 넓이를 제어합니다.\",\n\t\t\"편집기에서 끌어서 놓기로 선택 영역을 이동할 수 있는지 여부를 제어합니다.\",\n\t\t\"svgs와 함께 새 렌더링 메서드를 사용합니다.\",\n\t\t\"글꼴 문자와 함께 새 렌더링 방법을 사용합니다.\",\n\t\t\"안정적인 렌더링 방법을 사용합니다.\",\n\t\t\"공백이 새로운 실험적 메서드로 렌더링되는지 여부를 제어합니다.\",\n\t\t\"\\'Alt\\' 키를 누를 때 스크롤 속도 승수입니다.\",\n\t\t\"편집기에 코드 접기가 사용하도록 설정되는지 여부를 제어합니다.\",\n\t\t\"사용 가능한 경우 언어별 접기 전략을 사용합니다. 그렇지 않은 경우 들여쓰기 기반 전략을 사용합니다.\",\n\t\t\"들여쓰기 기반 접기 전략을 사용합니다.\",\n\t\t\"접기 범위를 계산하기 위한 전략을 제어합니다.\",\n\t\t\"편집기에서 접힌 범위를 강조 표시할지 여부를 제어합니다.\",\n\t\t\"편집기에서 가져오기 범위를 자동으로 축소할지 여부를 제어합니다.\",\n\t\t\"폴더블 영역의 최대 수입니다. 현재 원본에 폴더블 영역이 많을 때 이 값을 늘리면 편집기의 반응이 떨어질 수 있습니다.\",\n\t\t\"접힌 줄이 줄을 펼친 후 빈 콘텐츠를 클릭할지 여부를 제어합니다.\",\n\t\t\"글꼴 패밀리를 제어합니다.\",\n\t\t\"붙여넣은 콘텐츠의 서식을 편집기에서 자동으로 지정할지 여부를 제어합니다. 포맷터를 사용할 수 있어야 하며 포맷터가 문서에서 범위의 서식을 지정할 수 있어야 합니다.\",\n\t\t\"입력 후 편집기에서 자동으로 줄의 서식을 지정할지 여부를 제어합니다.\",\n\t\t\"편집기에서 세로 문자 모양 여백을 렌더링할지 여부를 제어합니다. 문자 모양 여백은 주로 디버깅에 사용됩니다.\",\n\t\t\"커서가 개요 눈금자에서 가려져야 하는지 여부를 제어합니다.\",\n\t\t\"문자 간격(픽셀)을 제어합니다.\",\n\t\t\"편집기에서 연결된 편집이 사용하도록 설정되었는지를 제어합니다. 언어에 따라 관련 기호(예: HTML 태그)가 편집 중에 업데이트됩니다.\",\n\t\t\"편집기에서 링크를 감지하고 클릭할 수 있게 만들지 여부를 제어합니다.\",\n\t\t\"일치하는 대괄호를 강조 표시합니다.\",\n\t\t\"마우스 휠 스크롤 이벤트의 `deltaX` 및 `deltaY`에서 사용할 승수입니다.\",\n\t\t\"마우스 휠을 사용할 때 \\'Ctrl\\' 키를 누르고 있으면 편집기의 글꼴을 확대/축소합니다.\",\n\t\t\"여러 커서가 겹치는 경우 커서를 병합합니다.\",\n\t\t\"Windows와 Linux의 \\'Control\\'을 macOS의 \\'Command\\'로 매핑합니다.\",\n\t\t\"Windows와 Linux의 \\'Alt\\'를 macOS의 \\'Option\\'으로 매핑합니다.\",\n\t\t\"마우스로 여러 커서를 추가할 때 사용할 수정자입니다. [정의로 이동] 및 [링크 열기] 마우스 제스처가 [멀티커서 수정자와](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier) 충돌하지 않도록 조정됩니다.\",\n\t\t\"각 커서는 텍스트 한 줄을 붙여넣습니다.\",\n\t\t\"각 커서는 전체 텍스트를 붙여넣습니다.\",\n\t\t\"붙여넣은 텍스트의 줄 수가 커서 수와 일치하는 경우 붙여넣기를 제어합니다.\",\n\t\t\"한 번에 활성 편집기에 있을 수 있는 최대 커서 수를 제어합니다.\",\n\t\t\"발생 항목을 강조 표시하지 않습니다.\",\n\t\t\"현재 파일의 발생 항목만 강조 표시합니다.\",\n\t\t\"실험적: 모든 유효한 열린 파일에서 발생 항목을 강조 표시합니다.\",\n\t\t\"열린 파일 전체에서 발생 수를 강조 표시할지 여부를 제어합니다.\",\n\t\t\"개요 눈금자 주위에 테두리를 그릴지 여부를 제어합니다.\",\n\t\t\"Peek를 여는 동안 트리에 포커스\",\n\t\t\"미리 보기를 열 때 편집기에 포커스\",\n\t\t\"미리 보기 위젯에서 인라인 편집기에 포커스를 둘지 또는 트리에 포커스를 둘지를 제어합니다.\",\n\t\t\"이동 정의 마우스 제스처가 항상 미리 보기 위젯을 열지 여부를 제어합니다.\",\n\t\t\"빠른 제안을 표시하기 전까지의 지연 시간(밀리초)을 제어합니다.\",\n\t\t\"편집기가 유형에 따라 자동으로 이름을 바꿀지 여부를 제어합니다.\",\n\t\t\"사용되지 않습니다. 대신 `editor.linkedEditing`을 사용하세요.\",\n\t\t\"편집기에서 제어 문자를 렌더링할지를 제어합니다.\",\n\t\t\"파일이 줄 바꿈으로 끝나면 마지막 줄 번호를 렌더링합니다.\",\n\t\t\"제본용 여백과 현재 줄을 모두 강조 표시합니다.\",\n\t\t\"편집기가 현재 줄 강조 표시를 렌더링하는 방식을 제어합니다.\",\n\t\t\"편집기에 포커스가 있는 경우에만 편집기에서 현재 줄 강조 표시를 렌더링해야 하는지 제어합니다.\",\n\t\t\"단어 사이의 공백 하나를 제외한 공백 문자를 렌더링합니다.\",\n\t\t\"선택한 텍스트에서만 공백 문자를 렌더링합니다.\",\n\t\t\"후행 공백 문자만 렌더링합니다.\",\n\t\t\"편집기에서 공백 문자를 렌더링할 방법을 제어합니다.\",\n\t\t\"선택 항목의 모서리를 둥글게 할지 여부를 제어합니다.\",\n\t\t\"편집기에서 가로로 스크롤되는 범위를 벗어나는 추가 문자의 수를 제어합니다.\",\n\t\t\"편집기에서 마지막 줄 이후로 스크롤할지 여부를 제어합니다.\",\n\t\t\"세로와 가로로 동시에 스크롤할 때에만 주축을 따라서 스크롤합니다. 트랙패드에서 세로로 스크롤할 때 가로 드리프트를 방지합니다.\",\n\t\t\"Linux 주 클립보드의 지원 여부를 제어합니다.\",\n\t\t\"편집기가 선택 항목과 유사한 일치 항목을 강조 표시해야하는지 여부를 제어합니다.\",\n\t\t\"접기 컨트롤을 항상 표시합니다.\",\n\t\t\"접기 컨트롤을 표시하지 않고 여백 크기를 줄이세요.\",\n\t\t\"마우스가 여백 위에 있을 때에만 접기 컨트롤을 표시합니다.\",\n\t\t\"여백의 접기 컨트롤이 표시되는 시기를 제어합니다.\",\n\t\t\"사용하지 않는 코드의 페이드 아웃을 제어합니다.\",\n\t\t\"취소선 사용되지 않는 변수를 제어합니다.\",\n\t\t\"다른 제안 위에 조각 제안을 표시합니다.\",\n\t\t\"다른 제안 아래에 조각 제안을 표시합니다.\",\n\t\t\"다른 제안과 함께 조각 제안을 표시합니다.\",\n\t\t\"코드 조각 제안을 표시하지 않습니다.\",\n\t\t\"코드 조각이 다른 추천과 함께 표시되는지 여부 및 정렬 방법을 제어합니다.\",\n\t\t\"편집기에서 애니메이션을 사용하여 스크롤할지 여부를 제어합니다.\",\n\t\t\"인라인 완성이 표시될 때 화면 읽기 프로그램 사용자에게 접근성 힌트를 제공해야 하는지 여부를 제어합니다.\",\n\t\t\"제안 위젯의 글꼴 크기입니다. {0}(으)로 설정하면 {1} 값이 사용됩니다.\",\n\t\t\"제안 위젯의 줄 높이입니다. {0}(으)로 설정하면 {1} 값이 사용됩니다. 최소값은 8입니다.\",\n\t\t\"트리거 문자를 입력할 때 제안을 자동으로 표시할지 여부를 제어합니다.\",\n\t\t\"항상 첫 번째 제안을 선택합니다.\",\n\t\t\"`log`가 최근에 완료되었으므로 추가 입력에서 제안을 선택하지 않은 경우 최근 제안을 선택하세요(예: `console.| -> console.log`).\",\n\t\t\"해당 제안을 완료한 이전 접두사에 따라 제안을 선택합니다(예: `co -> console` 및 `con -> const`).\",\n\t\t\"제안 목록을 표시할 때 제한이 미리 선택되는 방식을 제어합니다.\",\n\t\t\"탭 완료는 탭을 누를 때 가장 일치하는 제안을 삽입합니다.\",\n\t\t\"탭 완성을 사용하지 않도록 설정합니다.\",\n\t\t\"접두사가 일치하는 경우 코드 조각을 탭 완료합니다. \\'quickSuggestions\\'를 사용하지 않을 때 가장 잘 작동합니다.\",\n\t\t\"탭 완성을 사용하도록 설정합니다.\",\n\t\t\"비정상적인 줄 종결자가 자동으로 제거됩니다.\",\n\t\t\"비정상적인 줄 종결자가 무시됩니다.\",\n\t\t\"제거할 비정상적인 줄 종결자 프롬프트입니다.\",\n\t\t\"문제를 일으킬 수 있는 비정상적인 줄 종결자를 제거합니다.\",\n\t\t\"탭 정지 뒤에 공백을 삽입 및 삭제합니다.\",\n\t\t\"기본 줄 바꿈 규칙을 사용합니다.\",\n\t\t\"단어 분리는 중국어/일본어/한국어(CJK) 텍스트에 사용할 수 없습니다. CJK가 아닌 텍스트 동작은 일반 텍스트 동작과 같습니다.\",\n\t\t\"중국어/일본어/한국어(CJK) 텍스트에 사용되는 단어 분리 규칙을 제어합니다.\",\n\t\t\"단어 관련 탐색 또는 작업을 수행할 때 단어 구분 기호로 사용할 문자입니다.\",\n\t\t\"줄이 바뀌지 않습니다.\",\n\t\t\"뷰포트 너비에서 줄이 바뀝니다.\",\n\t\t\"`#editor.wordWrapColumn#`에서 줄이 바뀝니다.\",\n\t\t\"뷰포트의 최소값 및 `#editor.wordWrapColumn#`에서 줄이 바뀝니다.\",\n\t\t\"줄 바꿈 여부를 제어합니다.\",\n\t\t\"`#editor.wordWrap#`이 `wordWrapColumn` 또는 \\'bounded\\'인 경우 편집기의 열 줄 바꿈을 제어합니다.\",\n\t\t\"기본 문서 색 공급자를 사용하여 인라인 색 장식을 표시할지 여부를 제어합니다.\",\n\t\t\"편집기에서 탭을 받을지 또는 탐색을 위해 워크벤치로 미룰지를 제어합니다.\",\n\t],\n\t\"vs/editor/common/core/editorColorRegistry\": [\n\t\t\"커서 위치의 줄 강조 표시에 대한 배경색입니다.\",\n\t\t\"커서 위치의 줄 테두리에 대한 배경색입니다.\",\n\t\t\"빠른 열기 및 찾기 기능 등을 통해 강조 표시된 영역의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"강조 영역 주변의 테두리에 대한 배경색입니다\",\n\t\t\"강조 표시된 기호(예: 정의로 이동 또는 다음/이전 기호로 이동)의 배경색입니다. 이 색상은 기본 장식을 숨기지 않도록 불투명하지 않아야 합니다.\",\n\t\t\"강조 표시된 기호 주위의 테두리 배경색입니다.\",\n\t\t\"편집기 커서 색입니다.\",\n\t\t\"편집기 커서의 배경색입니다. 블록 커서와 겹치는 글자의 색상을 사용자 정의할 수 있습니다.\",\n\t\t\"편집기의 공백 문자 색입니다.\",\n\t\t\"편집기 줄 번호 색입니다.\",\n\t\t\"편집기 들여쓰기 안내선 색입니다.\",\n\t\t\"\\'editorIndentGuide.background\\'는 더 이상 사용되지 않습니다. 대신 \\'editorIndentGuide.background1\\'을 사용하세요.\",\n\t\t\"활성 편집기 들여쓰기 안내선 색입니다.\",\n\t\t\"\\'editorIndentGuide.activeBackground\\'는 더 이상 사용되지 않습니다. 대신 \\'editorIndentGuide.activeBackground1\\'을 사용하세요.\",\n\t\t\"편집기 들여쓰기 안내선 색(1).\",\n\t\t\"편집기 들여쓰기 안내선 색(2).\",\n\t\t\"편집기 들여쓰기 안내선 색(3).\",\n\t\t\"편집기 들여쓰기 안내선 색(4).\",\n\t\t\"편집기 들여쓰기 안내선 색(5).\",\n\t\t\"편집기 들여쓰기 안내선 색(6).\",\n\t\t\"활성 편집기 들여쓰기 안내선 색(1).\",\n\t\t\"활성 편집기 들여쓰기 안내선 색(2).\",\n\t\t\"활성 편집기 들여쓰기 안내선 색(3).\",\n\t\t\"활성 편집기 들여쓰기 안내선 색(4).\",\n\t\t\"활성 편집기 들여쓰기 안내선 색(5).\",\n\t\t\"활성 편집기 들여쓰기 안내선 색(6).\",\n\t\t\"편집기 활성 영역 줄번호 색상\",\n\t\t\"ID는 사용되지 않습니다. 대신 \\'editorLineNumber.activeForeground\\'를 사용하세요.\",\n\t\t\"편집기 활성 영역 줄번호 색상\",\n\t\t\"editor.renderFinalNewline이 흐리게 설정된 경우 최종 편집기 줄의 색입니다.\",\n\t\t\"편집기 눈금의 색상입니다.\",\n\t\t\"편집기 코드 렌즈의 전경색입니다.\",\n\t\t\"일치하는 괄호 뒤의 배경색\",\n\t\t\"일치하는 브래킷 박스의 색상\",\n\t\t\"개요 눈금 경계의 색상입니다.\",\n\t\t\"편집기 개요 눈금자의 배경색입니다.\",\n\t\t\"편집기 거터의 배경색입니다. 거터에는 글리프 여백과 행 수가 있습니다.\",\n\t\t\"편집기의 불필요한(사용하지 않는) 소스 코드 테두리 색입니다.\",\n\t\t\"편집기의 불필요한(사용하지 않는) 소스 코드 불투명도입니다. 예를 들어 \\\"#000000c0\\\"은 75% 불투명도로 코드를 렌더링합니다. 고대비 테마의 경우 페이드 아웃하지 않고 \\'editorUnnecessaryCode.border\\' 테마 색을 사용하여 불필요한 코드에 밑줄을 그으세요.\",\n\t\t\"편집기에서 고스트 텍스트의 테두리 색입니다.\",\n\t\t\"편집기에서 고스트 텍스트의 전경색입니다.\",\n\t\t\"편집기에서 고스트 텍스트의 배경색입니다.\",\n\t\t\"범위의 개요 눈금자 표식 색이 강조 표시됩니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"오류의 개요 눈금자 마커 색입니다.\",\n\t\t\"경고의 개요 눈금자 마커 색입니다.\",\n\t\t\"정보의 개요 눈금자 마커 색입니다.\",\n\t\t\"대괄호의 전경색(1)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\n\t\t\"대괄호의 전경색(2)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\n\t\t\"대괄호의 전경색(3)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\n\t\t\"대괄호의 전경색(4)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\n\t\t\"대괄호의 전경색(5)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\n\t\t\"대괄호의 전경색(6)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\n\t\t\"예기치 않은 대괄호의 전경색입니다.\",\n\t\t\"비활성 대괄호 쌍 안내선의 배경색입니다(1). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"비활성 대괄호 쌍 안내선의 배경색입니다(2). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"비활성 대괄호 쌍 안내선의 배경색입니다(3). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"비활성 대괄호 쌍 안내선의 배경색입니다(4). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"비활성 대괄호 쌍 안내선의 배경색입니다(5). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"비활성 대괄호 쌍 안내선의 배경색입니다(6). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"활성 대괄호 쌍 안내선의 배경색입니다(1). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"활성 대괄호 쌍 안내선의 배경색입니다(2). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"활성 대괄호 쌍 안내선의 배경색입니다(3). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"활성 대괄호 쌍 안내선의 배경색입니다(4). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"활성 대괄호 쌍 안내선의 배경색입니다(5). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"활성 대괄호 쌍 안내선의 배경색입니다(6). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\n\t\t\"유니코드 문자를 강조 표시하는 데 사용되는 테두리 색입니다.\",\n\t\t\"유니코드 문자를 강조 표시하는 데 사용되는 배경색입니다.\",\n\t],\n\t\"vs/editor/common/editorContextKeys\": [\n\t\t\"편집기 텍스트에 포커스가 있는지 여부(커서가 깜박임)\",\n\t\t\"편집기 또는 편집기 위젯에 포커스가 있는지 여부(예: 포커스가 찾기 위젯에 있음)\",\n\t\t\"편집기 또는 서식 있는 텍스트 입력에 포커스가 있는지 여부(커서가 깜박임)\",\n\t\t\"편집기가 읽기 전용인지 여부\",\n\t\t\"컨텍스트가 diff 편집기인지 여부\",\n\t\t\"컨텍스트가 포함된 diff 편집기인지 여부\",\n\t\t\"컨텍스트가 다중 diff 편집기인지 여부\",\n\t\t\"다중 diff 편집기의 모든 파일이 축소되는지 여부\",\n\t\t\"diff 편집기에 변경 사항이 있는지 여부\",\n\t\t\"이동된 코드 블록이 비교를 위해 선택되었는지 여부\",\n\t\t\"액세스 가능한 Diff 뷰어 표시 여부\",\n\t\t\"diff 편집기에서 나란히 인라인 중단점에 연결할지 여부\",\n\t\t\"\\'editor.columnSelection\\'을 사용하도록 설정되어 있는지 여부\",\n\t\t\"편집기에 선택된 텍스트가 있는지 여부\",\n\t\t\"편집기에 여러 개의 선택 항목이 있는지 여부\",\n\t\t\"\\'Tab\\' 키를 누르면 편집기 밖으로 포커스가 이동하는지 여부\",\n\t\t\"편집기 호버가 표시되는지 여부\",\n\t\t\"편집기 가리키기에 포커스가 있는지 여부\",\n\t\t\"스티키 스크롤의 포커스 여부\",\n\t\t\"스티키 스크롤의 가시성 여부\",\n\t\t\"독립 실행형 색 편집기가 표시되는지 여부\",\n\t\t\"독립 실행형 색 편집기가 포커스되는지 여부\",\n\t\t\"편집기가 더 큰 편집기(예: 전자 필기장)에 속해 있는지 여부\",\n\t\t\"편집기의 언어 식별자\",\n\t\t\"편집기에 완성 항목 공급자가 있는지 여부\",\n\t\t\"편집기에 코드 작업 공급자가 있는지 여부\",\n\t\t\"편집기에 CodeLens 공급자가 있는지 여부\",\n\t\t\"편집기에 정의 공급자가 있는지 여부\",\n\t\t\"편집기에 선언 공급자가 있는지 여부\",\n\t\t\"편집기에 구현 공급자가 있는지 여부\",\n\t\t\"편집기에 형식 정의 공급자가 있는지 여부\",\n\t\t\"편집기에 호버 공급자가 있는지 여부\",\n\t\t\"편집기에 문서 강조 표시 공급자가 있는지 여부\",\n\t\t\"편집기에 문서 기호 공급자가 있는지 여부\",\n\t\t\"편집기에 참조 공급자가 있는지 여부\",\n\t\t\"편집기에 이름 바꾸기 공급자가 있는지 여부\",\n\t\t\"편집기에 시그니처 도움말 공급자가 있는지 여부\",\n\t\t\"편집기에 인라인 힌트 공급자가 있는지 여부\",\n\t\t\"편집기에 문서 서식 공급자가 있는지 여부\",\n\t\t\"편집기에 문서 선택 서식 공급자가 있는지 여부\",\n\t\t\"편집기에 여러 개의 문서 서식 공급자가 있는지 여부\",\n\t\t\"편집기에 여러 개의 문서 선택 서식 공급자가 있는지 여부\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"배열\",\n\t\t\"부울\",\n\t\t\"클래스\",\n\t\t\"상수\",\n\t\t\"생성자\",\n\t\t\"열거형\",\n\t\t\"열거형 멤버\",\n\t\t\"이벤트\",\n\t\t\"필드\",\n\t\t\"파일\",\n\t\t\"함수\",\n\t\t\"인터페이스\",\n\t\t\"키\",\n\t\t\"메서드\",\n\t\t\"모듈\",\n\t\t\"네임스페이스\",\n\t\t\"Null\",\n\t\t\"숫자\",\n\t\t\"개체\",\n\t\t\"연산자\",\n\t\t\"패키지\",\n\t\t\"속성\",\n\t\t\"문자열\",\n\t\t\"구조체\",\n\t\t\"형식 매개 변수\",\n\t\t\"변수\",\n\t\t\"{0}({1})\",\n\t],\n\t\"vs/editor/common/languages/modesRegistry\": [\n\t\t\"일반 텍스트\",\n\t],\n\t\"vs/editor/common/model/editStack\": [\n\t\t\"입력하는 중\",\n\t],\n\t\"vs/editor/common/standaloneStrings\": [\n\t\t\"개발자: 검사 토큰\",\n\t\t\"줄/열로 이동...\",\n\t\t\"빠른 액세스 공급자 모두 표시\",\n\t\t\"명령 팔레트\",\n\t\t\"명령 표시 및 실행\",\n\t\t\"기호로 가서...\",\n\t\t\"범주별 기호로 이동...\",\n\t\t\"편집기 콘텐츠\",\n\t\t\"접근성 옵션은 Alt+F1을 눌러여 합니다.\",\n\t\t\"고대비 테마로 전환\",\n\t\t\"{1} 파일에서 편집을 {0}개 했습니다.\",\n\t],\n\t\"vs/editor/common/viewLayout/viewLineRenderer\": [\n\t\t\"자세히 표시({0})\",\n\t\t\"{0}자\",\n\t],\n\t\"vs/editor/contrib/anchorSelect/browser/anchorSelect\": [\n\t\t\"선택 앵커 지점\",\n\t\t\"{0}에 설정된 앵커: {1}\",\n\t\t\"선택 앵커 지점 설정\",\n\t\t\"선택 앵커 지점으로 이동\",\n\t\t\"앵커에서 커서로 선택\",\n\t\t\"선택 앵커 지점 취소\",\n\t],\n\t\"vs/editor/contrib/bracketMatching/browser/bracketMatching\": [\n\t\t\"괄호에 해당하는 영역을 표시자에 채색하여 표시합니다.\",\n\t\t\"대괄호로 이동\",\n\t\t\"괄호까지 선택\",\n\t\t\"대괄호 제거\",\n\t\t\"대괄호로 이동(&&B)\",\n\t\t\"대괄호 또는 중괄호를 포함하여 내부 텍스트를 선택합니다.\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/caretOperations\": [\n\t\t\"선택한 텍스트를 왼쪽으로 이동\",\n\t\t\"선택한 텍스트를 오른쪽으로 이동\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/transpose\": [\n\t\t\"문자 바꾸기\",\n\t],\n\t\"vs/editor/contrib/clipboard/browser/clipboard\": [\n\t\t\"잘라내기(&&T)\",\n\t\t\"잘라내기\",\n\t\t\"잘라내기\",\n\t\t\"잘라내기\",\n\t\t\"복사(&&C)\",\n\t\t\"복사\",\n\t\t\"복사\",\n\t\t\"복사\",\n\t\t\"다음으로 복사\",\n\t\t\"다음으로 복사\",\n\t\t\"공유\",\n\t\t\"공유\",\n\t\t\"공유\",\n\t\t\"붙여넣기(&&P)\",\n\t\t\"붙여넣기\",\n\t\t\"붙여넣기\",\n\t\t\"붙여넣기\",\n\t\t\"구문을 강조 표시하여 복사\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeAction\": [\n\t\t\"코드 작업을 적용하는 중 알 수 없는 오류가 발생했습니다.\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionCommands\": [\n\t\t\"실행할 코드 작업의 종류입니다.\",\n\t\t\"반환된 작업이 적용되는 경우를 제어합니다.\",\n\t\t\"항상 반환된 첫 번째 코드 작업을 적용합니다.\",\n\t\t\"첫 번째 반환된 코드 작업을 적용합니다(이 작업만 있는 경우).\",\n\t\t\"반환된 코드 작업을 적용하지 마세요.\",\n\t\t\"기본 코드 작업만 반환되도록 할지 여부를 제어합니다.\",\n\t\t\"빠른 수정...\",\n\t\t\"사용 가능한 코드 동작이 없습니다.\",\n\t\t\"\\'{0}\\'에 대한 기본 코드 작업을 사용할 수 없음\",\n\t\t\"\\'{0}\\'에 대한 코드 작업을 사용할 수 없음\",\n\t\t\"사용할 수 있는 기본 코드 작업 없음\",\n\t\t\"사용 가능한 코드 동작이 없습니다.\",\n\t\t\"리팩터링...\",\n\t\t\"\\'{0}\\'에 대한 기본 리팩터링 없음\",\n\t\t\"\\'{0}\\'에 대한 리팩터링 없음\",\n\t\t\"기본 설정 리팩터링을 사용할 수 없음\",\n\t\t\"사용 가능한 리펙터링이 없습니다.\",\n\t\t\"소스 작업...\",\n\t\t\"\\'{0}\\'에 대한 기본 소스 작업을 사용할 수 없음\",\n\t\t\"\\'{0}\\'에 대한 소스 작업을 사용할 수 없음\",\n\t\t\"사용할 수 있는 기본 원본 작업 없음\",\n\t\t\"사용 가능한 소스 작업이 없습니다.\",\n\t\t\"가져오기 구성\",\n\t\t\"사용 가능한 가져오기 구성 작업이 없습니다.\",\n\t\t\"모두 수정\",\n\t\t\"모든 작업 수정 사용 불가\",\n\t\t\"자동 수정...\",\n\t\t\"사용할 수 있는 자동 수정 없음\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionContributions\": [\n\t\t\"코드 작업 메뉴에 그룹 헤더 표시를 활성화/비활성화합니다.\",\n\t\t\"현재 진단 중이 아닐 때 줄 내에서 가장 가까운 빠른 수정 표시를 사용/사용 안 함으로 설정합니다.\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionController\": [\n\t\t\"컨텍스트: 줄 {1} 및 열 {2}의 {0}입니다.\",\n\t\t\"사용하지 않는 항목 숨기기\",\n\t\t\"비활성화된 항목 표시\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionMenu\": [\n\t\t\"추가 작업...\",\n\t\t\"빠른 수정\",\n\t\t\"추출\",\n\t\t\"인라인\",\n\t\t\"재작성\",\n\t\t\"이동\",\n\t\t\"코드 감싸기\",\n\t\t\"소스 작업\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/lightBulbWidget\": [\n\t\t\"코드 작업 표시. 기본 설정 빠른 수정 사용 가능({0})\",\n\t\t\"코드 작업 표시({0})\",\n\t\t\"코드 작업 표시\",\n\t\t\"인라인 채팅 시작({0})\",\n\t\t\"인라인 채팅 시작\",\n\t\t\"AI 작업 트리거\",\n\t],\n\t\"vs/editor/contrib/codelens/browser/codelensController\": [\n\t\t\"현재 줄에 대한 코드 렌즈 명령 표시\",\n\t\t\"명령 선택\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/colorPickerWidget\": [\n\t\t\"색 옵션을 토글하려면 클릭하세요(rgb/hsl/hex).\",\n\t\t\"색 편집기를 닫는 아이콘\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions\": [\n\t\t\"독립 실행형 색 편집기 표시 또는 포커스\",\n\t\t\"독립 실행형 색 편집기 표시 또는 포커스(&&S)\",\n\t\t\"색 편집기 숨기기\",\n\t\t\"독립 실행형 색 편집기로 색 삽입\",\n\t],\n\t\"vs/editor/contrib/comment/browser/comment\": [\n\t\t\"줄 주석 설정/해제\",\n\t\t\"줄 주석 설정/해제(&&T)\",\n\t\t\"줄 주석 추가\",\n\t\t\"줄 주석 제거\",\n\t\t\"블록 주석 설정/해제\",\n\t\t\"블록 주석 설정/해제(&&B)\",\n\t],\n\t\"vs/editor/contrib/contextmenu/browser/contextmenu\": [\n\t\t\"미니맵\",\n\t\t\"문자 렌더링\",\n\t\t\"세로 크기\",\n\t\t\"비례\",\n\t\t\"채우기\",\n\t\t\"맞춤\",\n\t\t\"슬라이더\",\n\t\t\"마우스 위로\",\n\t\t\"항상\",\n\t\t\"편집기 상황에 맞는 메뉴 표시\",\n\t],\n\t\"vs/editor/contrib/cursorUndo/browser/cursorUndo\": [\n\t\t\"커서 실행 취소\",\n\t\t\"커서 다시 실행\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution\": [\n\t\t\"다른 이름으로 붙여넣기...\",\n\t\t\"적용할 붙여넣기 편집의 ID입니다. 제공하지 않으면 편집기에 선택기가 표시됩니다.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController\": [\n\t\t\"붙여넣기 위젯이 표시되는지 여부\",\n\t\t\"붙여넣기 옵션 표시...\",\n\t\t\"붙여넣기 처리기를 실행하는 중입니다. 취소하려면 클릭하세요.\",\n\t\t\"붙여넣기 작업 선택\",\n\t\t\"붙여넣기 처리기를 실행하는 중\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders\": [\n\t\t\"기본 제공\",\n\t\t\"일반 텍스트 삽입\",\n\t\t\"URI 삽입\",\n\t\t\"URI 삽입\",\n\t\t\"경로 삽입\",\n\t\t\"경로 삽입\",\n\t\t\"상대 경로 삽입\",\n\t\t\"상대 경로 삽입\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution\": [\n\t\t\"지정된 MIME 형식의 콘텐츠에 사용할 기본 드롭 공급자를 구성합니다.\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController\": [\n\t\t\"드롭 위젯이 표시되는지 여부\",\n\t\t\"드롭 옵션 표시...\",\n\t\t\"드롭 처리기를 실행하는 중입니다. 취소하려면 클릭하세요.\",\n\t],\n\t\"vs/editor/contrib/editorState/browser/keybindingCancellation\": [\n\t\t\"편집기에서 취소 가능한 작업(예: \\'참조 피킹\\')을 실행하는지 여부\",\n\t],\n\t\"vs/editor/contrib/find/browser/findController\": [\n\t\t\"파일이 너무 커서 모두 바꾸기 작업을 수행할 수 없습니다.\",\n\t\t\"찾기\",\n\t\t\"찾기(&&F)\",\n\t\t\"\\\"정규식 사용\\\" 플래그를 재정의합니다.\\r\\n플래그는 미래를 위해 저장되지 않습니다.\\r\\n0: 아무것도 하지 않음\\r\\n1: True\\r\\n2: False\",\n\t\t\"\\\"전체 단어 일치\\\" 플래그를 재정의합니다.\\r\\n플래그는 미래를 위해 저장되지 않습니다.\\r\\n0: 아무것도 하지 않음\\r\\n1: True\\r\\n2: False\",\n\t\t\"\\\"Math Case\\\" 플래그를 재정의합니다.\\r\\n플래그는 미래를 위해 저장되지 않습니다.\\r\\n0: 아무것도 하지 않음\\r\\n1: True\\r\\n2: False\",\n\t\t\"\\\"케이스 보존\\\" 플래그를 재정의합니다.\\r\\n플래그는 미래를 위해 저장되지 않습니다.\\r\\n0: 아무것도 하지 않음\\r\\n1: True\\r\\n2: False\",\n\t\t\"인수로 찾기\",\n\t\t\"선택 영역에서 찾기\",\n\t\t\"다음 찾기\",\n\t\t\"이전 찾기\",\n\t\t\"일치 항목으로 이동...\",\n\t\t\"일치하는 항목이 없습니다. 다른 내용으로 검색해 보세요.\",\n\t\t\"특정 일치 항목으로 이동하려면 숫자를 입력하세요(1~{0} 사이).\",\n\t\t\"1에서 {0} 사이의 숫자를 입력하세요\",\n\t\t\"1에서 {0} 사이의 숫자를 입력하세요\",\n\t\t\"다음 선택 찾기\",\n\t\t\"이전 선택 찾기\",\n\t\t\"바꾸기\",\n\t\t\"바꾸기(&&R)\",\n\t],\n\t\"vs/editor/contrib/find/browser/findWidget\": [\n\t\t\"편집기 찾기 위젯에서 \\'선택 영역에서 찾기\\'의 아이콘입니다.\",\n\t\t\"편집기 찾기 위젯이 축소되었음을 나타내는 아이콘입니다.\",\n\t\t\"편집기 찾기 위젯이 확장되었음을 나타내는 아이콘입니다.\",\n\t\t\"편집기 찾기 위젯에서 \\'바꾸기\\'의 아이콘입니다.\",\n\t\t\"편집기 찾기 위젯에서 \\'모두 바꾸기\\'의 아이콘입니다.\",\n\t\t\"편집기 찾기 위젯에서 \\'이전 찾기\\'의 아이콘입니다.\",\n\t\t\"편집기 찾기 위젯에서 \\'다음 찾기\\'의 아이콘입니다.\",\n\t\t\"찾기/바꾸기\",\n\t\t\"찾기\",\n\t\t\"찾기\",\n\t\t\"이전 검색 결과\",\n\t\t\"다음 검색 결과\",\n\t\t\"선택 항목에서 찾기\",\n\t\t\"닫기\",\n\t\t\"바꾸기\",\n\t\t\"바꾸기\",\n\t\t\"바꾸기\",\n\t\t\"모두 바꾸기\",\n\t\t\"바꾸기 설정/해제\",\n\t\t\"처음 {0}개의 결과가 강조 표시되지만 모든 찾기 작업은 전체 텍스트에 대해 수행됩니다.\",\n\t\t\"{1}의 {0}\",\n\t\t\"결과 없음\",\n\t\t\"{0}개 찾음\",\n\t\t\"\\'{1}\\'에 대한 {0}을(를) 찾음\",\n\t\t\"{2}에서 \\'{1}\\'에 대한 {0}을(를) 찾음\",\n\t\t\"\\'{1}\\'에 대한 {0}을(를) 찾음\",\n\t\t\"Ctrl+Enter를 누르면 이제 모든 항목을 바꾸지 않고 줄 바꿈을 삽입합니다. editor.action.replaceAll의 키 바인딩을 수정하여 이 동작을 재정의할 수 있습니다.\",\n\t],\n\t\"vs/editor/contrib/folding/browser/folding\": [\n\t\t\"펼치기\",\n\t\t\"재귀적으로 펼치기\",\n\t\t\"접기\",\n\t\t\"접기 전환\",\n\t\t\"재귀적으로 접기\",\n\t\t\"모든 블록 코멘트를 접기\",\n\t\t\"모든 영역 접기\",\n\t\t\"모든 영역 펼치기\",\n\t\t\"선택한 항목을 제외한 모든 항목 접기\",\n\t\t\"선택한 항목을 제외한 모든 항목 표시\",\n\t\t\"모두 접기\",\n\t\t\"모두 펼치기\",\n\t\t\"부모 폴딩으로 이동\",\n\t\t\"이전 접기 범위로 이동\",\n\t\t\"다음 접기 범위로 이동\",\n\t\t\"선택 영역에서 접기 범위 만들기\",\n\t\t\"수동 폴딩 범위 제거\",\n\t\t\"수준 {0} 접기\",\n\t],\n\t\"vs/editor/contrib/folding/browser/foldingDecorations\": [\n\t\t\"접힌 범위의 배경색입니다. 색은 기본 장식을 숨기지 않기 위해 불투명해서는 안 됩니다.\",\n\t\t\"편집기 여백의 접기 컨트롤 색입니다.\",\n\t\t\"편집기 문자 모양 여백에서 확장된 범위의 아이콘입니다.\",\n\t\t\"편집기 문자 모양 여백에서 축소된 범위의 아이콘입니다.\",\n\t\t\"편집기 문자 모양 여백에서 수동으로 축소된 범위에 대한 아이콘입니다.\",\n\t\t\"편집기 문자 모양 여백에서 수동으로 확장된 범위에 대한 아이콘입니다.\",\n\t],\n\t\"vs/editor/contrib/fontZoom/browser/fontZoom\": [\n\t\t\"편집기 글꼴 확대\",\n\t\t\"편집기 글꼴 축소\",\n\t\t\"편집기 글꼴 확대/축소 다시 설정\",\n\t],\n\t\"vs/editor/contrib/format/browser/formatActions\": [\n\t\t\"문서 서식\",\n\t\t\"선택 영역 서식\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoError\": [\n\t\t\"다음 문제로 이동 (오류, 경고, 정보)\",\n\t\t\"다음 마커로 이동의 아이콘입니다.\",\n\t\t\"이전 문제로 이동 (오류, 경고, 정보)\",\n\t\t\"이전 마커로 이동의 아이콘입니다.\",\n\t\t\"파일의 다음 문제로 이동 (오류, 경고, 정보)\",\n\t\t\"다음 문제(&&P)\",\n\t\t\"파일의 이전 문제로 이동 (오류, 경고, 정보)\",\n\t\t\"이전 문제(&&P)\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoErrorWidget\": [\n\t\t\"오류\",\n\t\t\"경고\",\n\t\t\"정보\",\n\t\t\"힌트\",\n\t\t\"{1}의 {0}입니다. \",\n\t\t\"문제 {1}개 중 {0}개\",\n\t\t\"문제 {1}개 중 {0}개\",\n\t\t\"편집기 표식 탐색 위젯 오류 색입니다.\",\n\t\t\"편집기 마커 탐색 위젯 오류 제목 배경.\",\n\t\t\"편집기 표식 탐색 위젯 경고 색입니다.\",\n\t\t\"편집기 마커 탐색 위젯 경고 제목 배경.\",\n\t\t\"편집기 표식 탐색 위젯 정보 색입니다.\",\n\t\t\"편집기 마커 탐색 위젯 정보 제목 배경.\",\n\t\t\"편집기 표식 탐색 위젯 배경입니다.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/goToCommands\": [\n\t\t\"피킹\",\n\t\t\"정의\",\n\t\t\"\\'{0}\\'에 대한 정의를 찾을 수 없습니다.\",\n\t\t\"정의를 찾을 수 없음\",\n\t\t\"정의로 이동\",\n\t\t\"정의로 이동(&&D)\",\n\t\t\"측면에서 정의 열기\",\n\t\t\"정의 피킹\",\n\t\t\"선언\",\n\t\t\"\\'{0}\\'에 대한 선언을 찾을 수 없음\",\n\t\t\"선언을 찾을 수 없음\",\n\t\t\"선언으로 이동\",\n\t\t\"선언으로 이동(&&D)\",\n\t\t\"\\'{0}\\'에 대한 선언을 찾을 수 없음\",\n\t\t\"선언을 찾을 수 없음\",\n\t\t\"선언 미리 보기\",\n\t\t\"형식 정의\",\n\t\t\"\\'{0}\\'에 대한 형식 정의를 찾을 수 없습니다.\",\n\t\t\"형식 정의를 찾을 수 없습니다.\",\n\t\t\"형식 정의로 이동\",\n\t\t\"형식 정의로 이동(&&T)\",\n\t\t\"형식 정의 미리 보기\",\n\t\t\"구현\",\n\t\t\"\\'{0}\\'에 대한 구현을 찾을 수 없습니다.\",\n\t\t\"구현을 찾을 수 없습니다.\",\n\t\t\"구현으로 이동\",\n\t\t\"구현으로 이동(&&I)\",\n\t\t\"피킹 구현\",\n\t\t\"\\'{0}\\'에 대한 참조가 없습니다.\",\n\t\t\"참조가 없습니다.\",\n\t\t\"참조로 이동\",\n\t\t\"참조로 이동(&&R)\",\n\t\t\"참조\",\n\t\t\"참조 미리 보기\",\n\t\t\"참조\",\n\t\t\"임의의 기호로 이동\",\n\t\t\"위치\",\n\t\t\"\\'{0}\\'에 대한 검색 결과가 없음\",\n\t\t\"참조\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition\": [\n\t\t\"{0}개 정의를 표시하려면 클릭하세요.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesController\": [\n\t\t\"\\'참조 피킹\\' 또는 \\'정의 피킹\\'과 같이 참조 피킹이 표시되는지 여부\",\n\t\t\"로드 중...\",\n\t\t\"{0}({1})\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree\": [\n\t\t\"참조 {0}개\",\n\t\t\"참조 {0}개\",\n\t\t\"참조\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget\": [\n\t\t\"미리 보기를 사용할 수 없음\",\n\t\t\"결과 없음\",\n\t\t\"참조\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/referencesModel\": [\n\t\t\"{2} 열에 있는 {1} 행의 {0}에\",\n\t\t\"{3} 열에서 {2} 행의 {1}에 {0}\",\n\t\t\"{0}의 기호 1개, 전체 경로 {1}\",\n\t\t\"{1}의 기호 {0}개, 전체 경로 {2}\",\n\t\t\"결과 없음\",\n\t\t\"{0}에서 기호 1개를 찾았습니다.\",\n\t\t\"{1}에서 기호 {0}개를 찾았습니다.\",\n\t\t\"{1}개 파일에서 기호 {0}개를 찾았습니다.\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/symbolNavigation\": [\n\t\t\"키보드만으로 탐색할 수 있는 기호 위치가 있는지 여부\",\n\t\t\"{1}의 {0} 기호, 다음의 경우 {2}\",\n\t\t\"{1}의 기호 {0}\",\n\t],\n\t\"vs/editor/contrib/hover/browser/hover\": [\n\t\t\"가리키기 또는 포커스 표시\",\n\t\t\"마우스로 가리켜도 포커스가 옮겨 가지 않습니다.\",\n\t\t\"마우스로 가리키면 이미 표시된 경우에만 포커스가 옮겨 갑니다.\",\n\t\t\"마우스로 가리키면 포커스가 나타나는 경우 포커스가 자동으로 옮겨 갑니다.\",\n\t\t\"정의 미리 보기 가리킨 항목 표시\",\n\t\t\"위로 스크롤 가리키기\",\n\t\t\"아래로 스크롤 가리키기\",\n\t\t\"왼쪽으로 스크롤 가리키기\",\n\t\t\"오른쪽으로 스크롤 가리키기\",\n\t\t\"페이지 위로 가리키기\",\n\t\t\"페이지 아래쪽 가리키기\",\n\t\t\"위쪽 가리키기로 이동\",\n\t\t\"아래쪽 가리키기로 이동\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markdownHoverParticipant\": [\n\t\t\"로드 중...\",\n\t\t\"성능상의 이유로 긴 줄로 인해 렌더링이 일시 중지되었습니다. `editor.stopRenderingLineAfter`를 통해 구성할 수 있습니다.\",\n\t\t\"성능상의 이유로 긴 줄의 경우 토큰화를 건너뜁니다. 이 항목은 \\'editor.maxTokenizationLineLength\\'를 통해 구성할 수 있습니다.\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markerHoverParticipant\": [\n\t\t\"문제 보기\",\n\t\t\"빠른 수정을 사용할 수 없음\",\n\t\t\"빠른 수정을 확인하는 중...\",\n\t\t\"빠른 수정을 사용할 수 없음\",\n\t\t\"빠른 수정...\",\n\t],\n\t\"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace\": [\n\t\t\"이전 값으로 바꾸기\",\n\t\t\"다음 값으로 바꾸기\",\n\t],\n\t\"vs/editor/contrib/indentation/browser/indentation\": [\n\t\t\"들여쓰기를 공백으로 변환\",\n\t\t\"들여쓰기를 탭으로 변환\",\n\t\t\"구성된 탭 크기\",\n\t\t\"기본 탭 크기\",\n\t\t\"현재 탭 크기\",\n\t\t\"현재 파일의 탭 크기 선택\",\n\t\t\"탭을 사용한 들여쓰기\",\n\t\t\"공백을 사용한 들여쓰기\",\n\t\t\"탭 표시 크기 변경\",\n\t\t\"콘텐츠에서 들여쓰기 감지\",\n\t\t\"줄 다시 들여쓰기\",\n\t\t\"선택한 줄 다시 들여쓰기\",\n\t],\n\t\"vs/editor/contrib/inlayHints/browser/inlayHintsHover\": [\n\t\t\"삽입하려면 두 번 클릭\",\n\t\t\"Cmd+클릭\",\n\t\t\"Ctrl+클릭\",\n\t\t\"Option+클릭\",\n\t\t\"Alt+클릭\",\n\t\t\"정의({0})로 이동하여 자세히 알아보려면 마우스 오른쪽 단추를 클릭합니다.\",\n\t\t\"정의로 이동({0})\",\n\t\t\"명령 실행\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/commands\": [\n\t\t\"다음 인라인 제안 표시\",\n\t\t\"이전 인라인 제안 표시\",\n\t\t\"인라인 제안 트리거\",\n\t\t\"인라인 제안의 다음 단어 수락\",\n\t\t\"단어 수락\",\n\t\t\"인라인 제안의 다음 줄 수락\",\n\t\t\"줄 수락\",\n\t\t\"인라인 추천 수락\",\n\t\t\"수락\",\n\t\t\"인라인 제안 숨기기\",\n\t\t\"항상 도구 모음 표시\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/hoverParticipant\": [\n\t\t\"제안:\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys\": [\n\t\t\"인라인 제안 표시 여부\",\n\t\t\"인라인 제안이 공백으로 시작하는지 여부\",\n\t\t\"인라인 제안이 탭에 의해 삽입되는 것보다 작은 공백으로 시작하는지 여부\",\n\t\t\"현재 제안에 대한 제안 표시 여부\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController\": [\n\t\t\"접근성 보기에서 이를 검사({0})\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget\": [\n\t\t\"다음 매개 변수 힌트 표시의 아이콘입니다.\",\n\t\t\"이전 매개 변수 힌트 표시의 아이콘입니다.\",\n\t\t\"{0}({1})\",\n\t\t\"이전\",\n\t\t\"다음\",\n\t],\n\t\"vs/editor/contrib/lineSelection/browser/lineSelection\": [\n\t\t\"선 선택 영역 확장\",\n\t],\n\t\"vs/editor/contrib/linesOperations/browser/linesOperations\": [\n\t\t\"위에 줄 복사\",\n\t\t\"위에 줄 복사(&&C)\",\n\t\t\"아래에 줄 복사\",\n\t\t\"아래에 줄 복사(&&P)\",\n\t\t\"중복된 선택 영역\",\n\t\t\"중복된 선택 영역(&&D)\",\n\t\t\"줄 위로 이동\",\n\t\t\"줄 위로 이동(&&V)\",\n\t\t\"줄 아래로 이동\",\n\t\t\"줄 아래로 이동(&&L)\",\n\t\t\"줄을 오름차순 정렬\",\n\t\t\"줄을 내림차순으로 정렬\",\n\t\t\"중복 라인 삭제\",\n\t\t\"후행 공백 자르기\",\n\t\t\"줄 삭제\",\n\t\t\"줄 들여쓰기\",\n\t\t\"줄 내어쓰기\",\n\t\t\"위에 줄 삽입\",\n\t\t\"아래에 줄 삽입\",\n\t\t\"왼쪽 모두 삭제\",\n\t\t\"우측에 있는 항목 삭제\",\n\t\t\"줄 연결\",\n\t\t\"커서 주위 문자 바꾸기\",\n\t\t\"대문자로 변환\",\n\t\t\"소문자로 변환\",\n\t\t\"단어의 첫 글자를 대문자로 변환\",\n\t\t\"스네이크 표기법으로 변환\",\n\t\t\"Camel Case로 변환\",\n\t\t\"Kebab 사례로 변환\",\n\t],\n\t\"vs/editor/contrib/linkedEditing/browser/linkedEditing\": [\n\t\t\"연결된 편집 시작\",\n\t\t\"형식의 편집기에서 자동으로 이름을 바꿀 때의 배경색입니다.\",\n\t],\n\t\"vs/editor/contrib/links/browser/links\": [\n\t\t\"{0} 형식이 올바르지 않으므로 이 링크를 열지 못했습니다\",\n\t\t\"대상이 없으므로 이 링크를 열지 못했습니다.\",\n\t\t\"명령 실행\",\n\t\t\"링크로 이동\",\n\t\t\"Cmd+클릭\",\n\t\t\"Ctrl+클릭\",\n\t\t\"Option+클릭\",\n\t\t\"Alt+클릭\",\n\t\t\"명령 {0} 실행\",\n\t\t\"링크 열기\",\n\t],\n\t\"vs/editor/contrib/message/browser/messageController\": [\n\t\t\"편집기에서 현재 인라인 메시지를 표시하는지 여부\",\n\t],\n\t\"vs/editor/contrib/multicursor/browser/multicursor\": [\n\t\t\"커서가 추가됨: {0}\",\n\t\t\"커서가 추가됨: {0}\",\n\t\t\"위에 커서 추가\",\n\t\t\"위에 커서 추가(&&A)\",\n\t\t\"아래에 커서 추가\",\n\t\t\"아래에 커서 추가(&&D)\",\n\t\t\"줄 끝에 커서 추가\",\n\t\t\"줄 끝에 커서 추가(&&U)\",\n\t\t\"맨 아래에 커서 추가\",\n\t\t\"맨 위에 커서 추가\",\n\t\t\"다음 일치 항목 찾기에 선택 항목 추가\",\n\t\t\"다음 항목 추가(&&N)\",\n\t\t\"이전 일치 항목 찾기에 선택 항목 추가\",\n\t\t\"이전 항목 추가(&&R)\",\n\t\t\"다음 일치 항목 찾기로 마지막 선택 항목 이동\",\n\t\t\"마지막 선택 항목을 이전 일치 항목 찾기로 이동\",\n\t\t\"일치 항목 찾기의 모든 항목 선택\",\n\t\t\"모든 항목 선택(&&O)\",\n\t\t\"모든 항목 변경\",\n\t\t\"다음 커서 포커스\",\n\t\t\"다음 커서에 포커스를 맞춥니다.\",\n\t\t\"이전 커서 포커스\",\n\t\t\"이전 커서에 포커스를 맞춥니다.\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHints\": [\n\t\t\"매개 변수 힌트 트리거\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHintsWidget\": [\n\t\t\"다음 매개 변수 힌트 표시의 아이콘입니다.\",\n\t\t\"이전 매개 변수 힌트 표시의 아이콘입니다.\",\n\t\t\"{0}, 힌트\",\n\t\t\"매개 변수 힌트에 있는 활성 항목의 전경색입니다.\",\n\t],\n\t\"vs/editor/contrib/peekView/browser/peekView\": [\n\t\t\"현재 코드 편집기가 피킹 내부에 포함되었는지 여부\",\n\t\t\"닫기\",\n\t\t\"Peek 뷰 제목 영역의 배경색입니다.\",\n\t\t\"Peek 뷰 제목 색입니다.\",\n\t\t\"Peek 뷰 제목 정보 색입니다.\",\n\t\t\"Peek 뷰 테두리 및 화살표 색입니다.\",\n\t\t\"Peek 뷰 결과 목록의 배경색입니다.\",\n\t\t\"Peek 뷰 결과 목록에서 라인 노드의 전경색입니다.\",\n\t\t\"Peek 뷰 결과 목록에서 파일 노드의 전경색입니다.\",\n\t\t\"Peek 뷰 결과 목록에서 선택된 항목의 배경색입니다.\",\n\t\t\"Peek 뷰 결과 목록에서 선택된 항목의 전경색입니다.\",\n\t\t\"Peek 뷰 편집기의 배경색입니다.\",\n\t\t\"Peek 뷰 편집기의 거터 배경색입니다.\",\n\t\t\"피킹 뷰 편집기의 고정 스크롤 배경색입니다.\",\n\t\t\"Peek 뷰 결과 목록의 일치 항목 강조 표시 색입니다.\",\n\t\t\"Peek 뷰 편집기의 일치 항목 강조 표시 색입니다.\",\n\t\t\"Peek 뷰 편집기의 일치 항목 강조 표시 테두리입니다.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess\": [\n\t\t\"우선 텍스트 편집기를 열고 줄로 이동합니다.\",\n\t\t\"줄 {0} 및 문자 {1}(으)로 이동합니다.\",\n\t\t\"{0} 줄로 이동합니다.\",\n\t\t\"현재 줄: {0}, 문자: {1} 이동할 줄 1~{2} 사이의 번호를 입력합니다.\",\n\t\t\"현재 줄: {0}, 문자: {1}. 이동할 줄 번호를 입력합니다.\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess\": [\n\t\t\"기호로 이동하려면 먼저 기호 정보가 있는 텍스트 편집기를 엽니다.\",\n\t\t\"활성 상태의 텍스트 편집기는 기호 정보를 제공하지 않습니다.\",\n\t\t\"일치하는 편집기 기호 없음\",\n\t\t\"편집기 기호 없음\",\n\t\t\"측면에서 열기\",\n\t\t\"하단에 열기\",\n\t\t\"기호({0})\",\n\t\t\"속성({0})\",\n\t\t\"메서드({0})\",\n\t\t\"함수({0})\",\n\t\t\"생성자({0})\",\n\t\t\"변수({0})\",\n\t\t\"클래스({0})\",\n\t\t\"구조체({0})\",\n\t\t\"이벤트({0})\",\n\t\t\"연산자({0})\",\n\t\t\"인터페이스({0})\",\n\t\t\"네임스페이스({0})\",\n\t\t\"패키지({0})\",\n\t\t\"형식 매개 변수({0})\",\n\t\t\"모듈({0})\",\n\t\t\"속성({0})\",\n\t\t\"열거형({0})\",\n\t\t\"열거형 멤버({0})\",\n\t\t\"문자열({0})\",\n\t\t\"파일({0})\",\n\t\t\"배열({0})\",\n\t\t\"숫자({0})\",\n\t\t\"부울({0})\",\n\t\t\"개체({0})\",\n\t\t\"키({0})\",\n\t\t\"필드({0})\",\n\t\t\"상수({0})\",\n\t],\n\t\"vs/editor/contrib/readOnlyMessage/browser/contribution\": [\n\t\t\"읽기 전용 입력에서는 편집할 수 없습니다.\",\n\t\t\"읽기 전용 편집기에서는 편집할 수 없습니다.\",\n\t],\n\t\"vs/editor/contrib/rename/browser/rename\": [\n\t\t\"결과가 없습니다.\",\n\t\t\"위치 이름을 바꾸는 중 알 수 없는 오류가 발생했습니다.\",\n\t\t\"\\'{0}\\'에서 \\'{1}\\'(으)로 이름을 바꾸는 중\",\n\t\t\"{1}에 {0} 이름 바꾸기\",\n\t\t\"\\'{0}\\'을(를) \\'{1}\\'(으)로 이름을 변경했습니다. 요약: {2}\",\n\t\t\"이름 바꾸기를 통해 편집 내용을 적용하지 못했습니다.\",\n\t\t\"이름 바꾸기를 통해 편집 내용을 계산하지 못했습니다.\",\n\t\t\"기호 이름 바꾸기\",\n\t\t\"이름을 바꾸기 전에 변경 내용을 미리 볼 수 있는 기능 사용/사용 안 함\",\n\t],\n\t\"vs/editor/contrib/rename/browser/renameInputField\": [\n\t\t\"입력 이름 바꾸기 위젯이 표시되는지 여부\",\n\t\t\"입력 이름을 바꾸세요. 새 이름을 입력한 다음 [Enter] 키를 눌러 커밋하세요.\",\n\t\t\"이름 바꾸기 {0}, 미리 보기 {1}\",\n\t],\n\t\"vs/editor/contrib/smartSelect/browser/smartSelect\": [\n\t\t\"선택 영역 확장\",\n\t\t\"선택 영역 확장(&&E)\",\n\t\t\"선택 영역 축소\",\n\t\t\"선택 영역 축소(&&S)\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetController2\": [\n\t\t\"현재 편집기가 코드 조각 모드인지 여부\",\n\t\t\"코드 조각 모드일 때 다음 탭 정지가 있는지 여부\",\n\t\t\"코드 조각 모드일 때 이전 탭 정지가 있는지 여부\",\n\t\t\"다음 자리 표시자로 이동...\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetVariables\": [\n\t\t\"일요일\",\n\t\t\"월요일\",\n\t\t\"화요일\",\n\t\t\"수요일\",\n\t\t\"목요일\",\n\t\t\"금요일\",\n\t\t\"토요일\",\n\t\t\"일\",\n\t\t\"월\",\n\t\t\"화\",\n\t\t\"수\",\n\t\t\"목\",\n\t\t\"금\",\n\t\t\"토\",\n\t\t\"1월\",\n\t\t\"2월\",\n\t\t\"3월\",\n\t\t\"4월\",\n\t\t\"5월\",\n\t\t\"6월\",\n\t\t\"7월\",\n\t\t\"8월\",\n\t\t\"9월\",\n\t\t\"10월\",\n\t\t\"11월\",\n\t\t\"12월\",\n\t\t\"1월\",\n\t\t\"2월\",\n\t\t\"3월\",\n\t\t\"4월\",\n\t\t\"5월\",\n\t\t\"6월\",\n\t\t\"7월\",\n\t\t\"8월\",\n\t\t\"9월\",\n\t\t\"10월\",\n\t\t\"11월\",\n\t\t\"12월\",\n\t],\n\t\"vs/editor/contrib/stickyScroll/browser/stickyScrollActions\": [\n\t\t\"고정 스크롤 토글\",\n\t\t\"고정 스크롤 토글(&&T)\",\n\t\t\"고정 스크롤\",\n\t\t\"고정 스크롤(&&S)\",\n\t\t\"고정 스크롤 포커스\",\n\t\t\"고정 스크롤 포커스(&&F)\",\n\t\t\"다음 고정 스크롤 선 선택\",\n\t\t\"이전 고정 스크롤 선 선택\",\n\t\t\"포커스가 있는 고정 스크롤 선으로 이동\",\n\t\t\"편집기 선택\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggest\": [\n\t\t\"제안에 초점을 맞추는지 여부\",\n\t\t\"제안 세부 정보가 표시되는지 여부\",\n\t\t\"선택할 수 있는 여러 제안이 있는지 여부\",\n\t\t\"현재 제안을 삽입하면 변경 내용이 생성되는지 또는 모든 항목이 이미 입력되었는지 여부\",\n\t\t\"<Enter> 키를 누를 때 제안이 삽입되는지 여부\",\n\t\t\"현재 제안에 삽입 및 바꾸기 동작이 있는지 여부\",\n\t\t\"기본 동작이 삽입인지 또는 바꾸기인지 여부\",\n\t\t\"현재 제안에서 추가 세부 정보를 확인하도록 지원하는지 여부\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestController\": [\n\t\t\"{0}의 {1}개의 수정사항을 수락하는 중\",\n\t\t\"제안 항목 트리거\",\n\t\t\"삽입\",\n\t\t\"삽입\",\n\t\t\"바꾸기\",\n\t\t\"바꾸기\",\n\t\t\"삽입\",\n\t\t\"간단히 표시\",\n\t\t\"더 보기\",\n\t\t\"제안 위젯 크기 다시 설정\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidget\": [\n\t\t\"제안 위젯의 배경색입니다.\",\n\t\t\"제안 위젯의 테두리 색입니다.\",\n\t\t\"제안 위젯의 전경색입니다.\",\n\t\t\"제한 위젯에서 선택된 항목의 전경색입니다.\",\n\t\t\"제한 위젯에서 선택된 항목의 아이콘 전경색입니다.\",\n\t\t\"제한 위젯에서 선택된 항목의 배경색입니다.\",\n\t\t\"제안 위젯의 일치 항목 강조 표시 색입니다.\",\n\t\t\"항목에 포커스가 있을 때 추천 위젯에서 일치하는 항목의 색이 강조 표시됩니다.\",\n\t\t\"제안 위젯 상태의 배경색입니다.\",\n\t\t\"로드 중...\",\n\t\t\"제안 항목이 없습니다.\",\n\t\t\"제안\",\n\t\t\"{0} {1}, {2}\",\n\t\t\"{0} {1}\",\n\t\t\"{0}, {1}\",\n\t\t\"{0}, 문서: {1}\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetDetails\": [\n\t\t\"닫기\",\n\t\t\"로드 중...\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetRenderer\": [\n\t\t\"제안 위젯에서 자세한 정보의 아이콘입니다.\",\n\t\t\"자세한 정보\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetStatus\": [\n\t\t\"{0}({1})\",\n\t],\n\t\"vs/editor/contrib/symbolIcons/browser/symbolIcons\": [\n\t\t\"배열 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"부울 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"클래스 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"색 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안에 표시됩니다.\",\n\t\t\"상수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"생성자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"열거자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"열거자 멤버 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"이벤트 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"필드 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"파일 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"폴더 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"함수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"인터페이스 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"키 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"키워드 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"메서드 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"모듈 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"네임스페이스 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"null 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"숫자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"개체 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"연산자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"패키지 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"속성 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"참조 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"코드 조각 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"문자열 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"구조 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"텍스트 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\n\t\t\"형식 매개변수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"단위 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t\t\"변수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\n\t],\n\t\"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode\": [\n\t\t\"<Tab> 키로 포커스 이동 설정/해제\",\n\t\t\"이제 <Tab> 키를 누르면 포커스가 다음 포커스 가능한 요소로 이동합니다.\",\n\t\t\"이제 <Tab> 키를 누르면 탭 문자가 삽입됩니다.\",\n\t],\n\t\"vs/editor/contrib/tokenization/browser/tokenization\": [\n\t\t\"개발자: 강제로 다시 토큰화\",\n\t],\n\t\"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter\": [\n\t\t\"확장 편집기에 경고 메시지와 함께 표시되는 아이콘입니다.\",\n\t\t\"이 문서에는 기본 ASCII 유니코드 문자가 아닌 문자가 많이 포함되어 있습니다.\",\n\t\t\"이 문서에는 모호한 유니코드 문자가 많이 포함되어 있습니다.\",\n\t\t\"이 문서에는 보이지 않는 유니코드 문자가 많이 포함되어 있습니다.\",\n\t\t\"문자 {0}은(는) 소스 코드에서 더 일반적인 ASCII 문자 {1}과(와) 혼동될 수 있습니다.\",\n\t\t\"{0} 문자는 소스 코드에서 더 일반적인 {1} 문자와 혼동될 수 있습니다.\",\n\t\t\"{0} 문자가 보이지 않습니다.\",\n\t\t\"{0} 문자는 기본 ASCII 문자가 아닙니다.\",\n\t\t\"설정 조정\",\n\t\t\"메모에서 강조 표시 사용 안 함\",\n\t\t\"메모에서 문자 강조 표시 사용 안 함\",\n\t\t\"문자열에서 강조 표시 사용 안 함\",\n\t\t\"문자열에서 문자 강조 표시 사용 안 함\",\n\t\t\"모호한 강조 사용 안 함\",\n\t\t\"모호한 문자 강조 표시 사용 안 함\",\n\t\t\"보이지 않는 강조 사용 안 함\",\n\t\t\"보이지 않는 문자 강조 표시 사용 안 함\",\n\t\t\"ASCII가 문자가 아닌 강조 사용 안 함\",\n\t\t\"기본이 아닌 ASCII 문자 강조 표시 사용 안 함\",\n\t\t\"제외 옵션 표시\",\n\t\t\"{0}(보이지 않는 문자)이(가) 강조 표시되지 않도록 제외\",\n\t\t\"강조 표시에서 {0} 제외\",\n\t\t\"언어 \\\"{0}\\\"에서 더 일반적인 유니코드 문자를 허용합니다.\",\n\t\t\"유니코드 강조 표시 옵션 구성\",\n\t],\n\t\"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators\": [\n\t\t\"비정상적인 줄 종결자\",\n\t\t\"비정상적인 줄 종결자가 검색됨\",\n\t\t\"이 파일 ‘\\r\\n’에 LS(줄 구분 기호) 또는 PS(단락 구분 기호) 같은 하나 이상의 비정상적인 줄 종결자 문자가 포함되어 있습니다.{0}\\r\\n파일에서 제거하는 것이 좋습니다. `editor.unusualLineTerminators`를 통해 구성할 수 있습니다.\",\n\t\t\"비정상적인 줄 종결자 제거(&&R)\",\n\t\t\"무시\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/highlightDecorations\": [\n\t\t\"변수 읽기와 같은 읽기 액세스 중 기호의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"변수에 쓰기와 같은 쓰기 액세스 중 기호의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"기호에 대한 텍스트 항목의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"변수 읽기와 같은 읽기 액세스 중 기호의 테두리 색입니다.\",\n\t\t\"변수에 쓰기와 같은 쓰기 액세스 중 기호의 테두리 색입니다.\",\n\t\t\"기호에 대한 텍스트 항목의 테두리 색입니다.\",\n\t\t\"기호 강조 표시의 개요 눈금자 표식 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"쓰기 액세스 기호에 대한 개요 눈금자 표식 색이 강조 표시됩니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"기호에 대한 텍스트 항목의 개요 눈금자 마커 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/wordHighlighter\": [\n\t\t\"다음 강조 기호로 이동\",\n\t\t\"이전 강조 기호로 이동\",\n\t\t\"기호 강조 표시 트리거\",\n\t],\n\t\"vs/editor/contrib/wordOperations/browser/wordOperations\": [\n\t\t\"단어 삭제\",\n\t],\n\t\"vs/platform/action/common/actionCommonCategories\": [\n\t\t\"보기\",\n\t\t\"도움말\",\n\t\t\"테스트\",\n\t\t\"파일\",\n\t\t\"기본 설정\",\n\t\t\"개발자\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionList\": [\n\t\t\"적용하려면 {0}, 미리 보기를 보려면 {1}\",\n\t\t\"신청하려면 {0}\",\n\t\t\"{0}, 사용 안 함 이유: {1}\",\n\t\t\"작업 위젯\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionWidget\": [\n\t\t\"작업 표시줄에서 토글된 작업 항목의 배경색입니다.\",\n\t\t\"작업 위젯 목록 표시 여부\",\n\t\t\"작업 위젯 숨기기\",\n\t\t\"이전 작업 선택\",\n\t\t\"다음 작업 선택\",\n\t\t\"선택한 작업 수락\",\n\t\t\"선택한 작업 미리 보기\",\n\t],\n\t\"vs/platform/actions/browser/menuEntryActionViewItem\": [\n\t\t\"{0}({1})\",\n\t\t\"{0}({1})\",\n\t\t\"{0}\\r\\n[{1}] {2}\",\n\t],\n\t\"vs/platform/actions/browser/toolbar\": [\n\t\t\"숨기기\",\n\t\t\"메뉴 다시 설정\",\n\t],\n\t\"vs/platform/actions/common/menuService\": [\n\t\t\"\\'{0}\\' 숨기기\",\n\t],\n\t\"vs/platform/audioCues/browser/audioCueService\": [\n\t\t\"줄에 대한 오류\",\n\t\t\"줄에 대한 경고\",\n\t\t\"줄의 접힌 부분\",\n\t\t\"줄의 중단점\",\n\t\t\"줄의 인라인 제안\",\n\t\t\"터미널 빠른 수정\",\n\t\t\"중단점에서 중지된 디버거\",\n\t\t\"줄의 인레이 힌트 없음\",\n\t\t\"완료된 작업\",\n\t\t\"작업 실패\",\n\t\t\"터미널 명령 실패\",\n\t\t\"터미널 벨\",\n\t\t\"Notebook 셀 완료됨\",\n\t\t\"Notebook 셀 실패\",\n\t\t\"Diff 줄 삽입됨\",\n\t\t\"Diff 줄 삭제됨\",\n\t\t\"Diff 줄 수정됨\",\n\t\t\"채팅 요청 전송됨\",\n\t\t\"채팅 응답 수신됨\",\n\t\t\"채팅 응답 대기 중\",\n\t\t\"지우기\",\n\t\t\"저장\",\n\t\t\"서식\",\n\t],\n\t\"vs/platform/configuration/common/configurationRegistry\": [\n\t\t\"기본 언어 구성 재정의\",\n\t\t\"{0}에서 재정의할 설정을 구성합니다.\",\n\t\t\"언어에 대해 재정의할 편집기 설정을 구성합니다.\",\n\t\t\"이 설정은 언어별 구성을 지원하지 않습니다.\",\n\t\t\"언어에 대해 재정의할 편집기 설정을 구성합니다.\",\n\t\t\"이 설정은 언어별 구성을 지원하지 않습니다.\",\n\t\t\"빈 속성을 등록할 수 없음\",\n\t\t\"\\'{0}\\'을(를) 등록할 수 없습니다. 이는 언어별 편집기 설정을 설명하는 속성 패턴인 \\'\\\\\\\\[.*\\\\\\\\]$\\'과(와) 일치합니다. \\'configurationDefaults\\' 기여를 사용하세요.\",\n\t\t\"\\'{0}\\'을(를) 등록할 수 없습니다. 이 속성은 이미 등록되어 있습니다.\",\n\t\t\"\\'{0}\\'을(를) 등록할 수 없습니다. 연결된 정책 {1}이(가) 이미 {2}에 등록되어 있습니다.\",\n\t],\n\t\"vs/platform/contextkey/browser/contextKeyService\": [\n\t\t\"컨텍스트 키에 대한 정보를 반환하는 명령\",\n\t],\n\t\"vs/platform/contextkey/common/contextkey\": [\n\t\t\"빈 컨텍스트 키 식\",\n\t\t\"식 쓰는 것을 잊으셨나요? 항상 \\'false\\' 또는 \\'true\\'를 넣어 각각 false 또는 true로 평가할 수도 있습니다.\",\n\t\t\"\\'not\\' 뒤에 \\'in\\'이 있습니다.\",\n\t\t\"닫는 괄호 \\')\\'\",\n\t\t\"예기치 않은 토큰\",\n\t\t\"토큰 앞에 && 또는 ||를 입력하는 것을 잊으셨나요?\",\n\t\t\"필요하지 않은 식의 끝\",\n\t\t\"컨텍스트 키를 입력하는 것을 잊으셨나요?\",\n\t\t\"예상: {0}\\r\\n수신됨: \\'{1}\\'.\",\n\t],\n\t\"vs/platform/contextkey/common/contextkeys\": [\n\t\t\"운영 체제가 macOS인지 여부\",\n\t\t\"운영 체제가 Linux인지 여부\",\n\t\t\"운영 체제가 Windows인지 여부\",\n\t\t\"플랫폼이 웹 브라우저인지 여부\",\n\t\t\"브라우저 기반이 아닌 플랫폼에서 운영 체제가 macOS인지 여부\",\n\t\t\"운영 체제가 iOS인지 여부\",\n\t\t\"플랫폼이 모바일 웹 브라우저인지 여부\",\n\t\t\"VS 코드의 품질 유형\",\n\t\t\"키보드 포커스가 입력 상자 내에 있는지 여부\",\n\t],\n\t\"vs/platform/contextkey/common/scanner\": [\n\t\t\"{0}을(를) 사용하시겠습니까?\",\n\t\t\"{0} 또는 {1}을(를) 사용하시겠습니까?\",\n\t\t\"{0}, {1} 또는 {2}을(를) 사용하시겠습니까?\",\n\t\t\"견적을 열거나 닫는 것을 잊으셨나요?\",\n\t\t\"\\'/\\'(슬래시) 문자를 이스케이프하는 것을 잊으셨나요? 이스케이프하려면 앞에 백슬라시 두 개(예: \\'\\\\\\\\/\\')를 넣습니다.\",\n\t],\n\t\"vs/platform/history/browser/contextScopedHistoryWidget\": [\n\t\t\"제안이 표시되는지 여부\",\n\t],\n\t\"vs/platform/keybinding/common/abstractKeybindingService\": [\n\t\t\"({0})을(를) 눌렀습니다. 둘째 키는 잠시 기다렸다가 누르십시오...\",\n\t\t\"({0})을(를) 눌렀습니다. 코드의 다음 키를 기다리는 중...\",\n\t\t\"키 조합({0}, {1})은 명령이 아닙니다.\",\n\t\t\"키 조합({0}, {1})은 명령이 아닙니다.\",\n\t],\n\t\"vs/platform/list/browser/listService\": [\n\t\t\"워크벤치\",\n\t\t\"Windows와 Linux의 \\'Control\\'을 macOS의 \\'Command\\'로 매핑합니다.\",\n\t\t\"Windows와 Linux의 \\'Alt\\'를 macOS의 \\'Option\\'으로 매핑합니다.\",\n\t\t\"마우스로 트리와 목록의 항목을 다중 선택에 추가할 때 사용할 한정자입니다(예를 들어 탐색기에서 편집기와 SCM 보기를 여는 경우). \\'옆에서 열기\\' 마우스 제스처(지원되는 경우)는 다중 선택 한정자와 충돌하지 않도록 조정됩니다.\",\n\t\t\"트리와 목록에서 마우스를 사용하여 항목을 여는 방법을 제어합니다(지원되는 경우). 일부 트리와 목록에서는 이 설정을 적용할 수 없는 경우 무시하도록 선택할 수 있습니다.\",\n\t\t\"워크벤치에서 목록 및 트리의 가로 스크롤 여부를 제어합니다. 경고: 이 설정을 켜면 성능에 영향을 미칩니다.\",\n\t\t\"스크롤 막대 스크롤 페이지의 페이지별 클릭 여부를 제어합니다.\",\n\t\t\"트리 들여쓰기를 픽셀 단위로 제어합니다.\",\n\t\t\"트리에서 들여쓰기 가이드를 렌더링할지 여부를 제어합니다.\",\n\t\t\"목록과 트리에 부드러운 화면 이동 기능이 있는지를 제어합니다.\",\n\t\t\"마우스 휠 스크롤 이벤트의 `deltaX` 및 `deltaY`에서 사용할 승수입니다.\",\n\t\t\"\\'Alt\\' 키를 누를 때 스크롤 속도 승수입니다.\",\n\t\t\"검색할 때 요소를 강조 표시합니다. 추가 위아래 탐색은 강조 표시된 요소만 탐색합니다.\",\n\t\t\"검색할 때 요소를 필터링합니다.\",\n\t\t\"워크벤치에서 목록 및 트리의 기본 찾기 모드를 제어합니다.\",\n\t\t\"간단한 키보드 탐색에서는 키보드 입력과 일치하는 요소에 집중합니다. 일치는 접두사에서만 수행됩니다.\",\n\t\t\"키보드 탐색 강조 표시에서는 키보드 입력과 일치하는 요소를 강조 표시합니다. 이후로 탐색에서 위 및 아래로 이동하는 경우 강조 표시된 요소만 트래버스합니다.\",\n\t\t\"키보드 탐색 필터링에서는 키보드 입력과 일치하지 않는 요소를 모두 필터링하여 숨깁니다.\",\n\t\t\"워크벤치의 목록 및 트리 키보드 탐색 스타일을 제어합니다. 간소화하고, 강조 표시하고, 필터링할 수 있습니다.\",\n\t\t\"대신 \\'workbench.list.defaultFindMode\\' 및 \\'workbench.list.typeNavigationMode\\'를 사용하세요.\",\n\t\t\"검색할 때 유사 항목 일치를 사용합니다.\",\n\t\t\"검색할 때 연속 일치를 사용합니다.\",\n\t\t\"워크벤치에서 목록 및 트리를 검색할 때 사용하는 일치 유형을 제어합니다.\",\n\t\t\"폴더 이름을 클릭할 때 트리 폴더가 확장되는 방법을 제어합니다. 일부 트리와 목록에서는 이 설정을 적용할 수 없는 경우 무시하도록 선택할 수 있습니다.\",\n\t\t\"트리에서 고정 스크롤을 사용할지 여부를 제어합니다.\",\n\t\t\"`#workbench.tree.enableStickyScroll#`을 사용하도록 설정한 경우 트리에 표시되는 고정 요소의 수를 제어합니다.\",\n\t\t\"워크벤치의 목록 및 트리에서 형식 탐색이 작동하는 방식을 제어합니다. \\'trigger\\'로 설정 시 \\'list.triggerTypeNavigation\\' 명령이 실행되면 형식 탐색이 시작됩니다.\",\n\t],\n\t\"vs/platform/markers/common/markers\": [\n\t\t\"오류\",\n\t\t\"경고\",\n\t\t\"정보\",\n\t],\n\t\"vs/platform/quickinput/browser/commandsQuickAccess\": [\n\t\t\"최근에 사용한 항목\",\n\t\t\"유사한 명령\",\n\t\t\"일반적으로 사용됨\",\n\t\t\"기타 명령\",\n\t\t\"유사한 명령\",\n\t\t\"{0}, {1}\",\n\t\t\"\\'{0}\\' 명령에서 오류가 발생했습니다.\",\n\t],\n\t\"vs/platform/quickinput/browser/helpQuickAccess\": [\n\t\t\"{0}, {1}\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInput\": [\n\t\t\"뒤로\",\n\t\t\"입력을 확인하려면 \\'Enter\\' 키를 누르고, 취소하려면 \\'Esc\\' 키를 누르세요.\",\n\t\t\"{0} / {1}\",\n\t\t\"결과의 범위를 축소하려면 입력하세요.\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputController\": [\n\t\t\"모든 확인란 선택/해제\",\n\t\t\"{0}개 결과\",\n\t\t\"{0} 선택됨\",\n\t\t\"확인\",\n\t\t\"사용자 지정\",\n\t\t\"뒤로({0})\",\n\t\t\"뒤로\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputList\": [\n\t\t\"빠른 입력\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputUtils\": [\n\t\t\"\\'{0}\\' 명령을 실행하려면 클릭\",\n\t],\n\t\"vs/platform/theme/common/colorRegistry\": [\n\t\t\"전체 전경색입니다. 이 색은 구성 요소에서 재정의하지 않은 경우에만 사용됩니다.\",\n\t\t\"비활성화된 요소의 전체 전경입니다. 이 색은 구성 요소에서 재정의하지 않는 경우에만 사용됩니다.\",\n\t\t\"오류 메시지에 대한 전체 전경색입니다. 이 색은 구성 요소에서 재정의하지 않은 경우에만 사용됩니다.\",\n\t\t\"레이블과 같이 추가 정보를 제공하는 설명 텍스트의 전경색입니다.\",\n\t\t\"워크벤치 아이콘의 기본 색상입니다.\",\n\t\t\"포커스가 있는 요소의 전체 테두리 색입니다. 이 색은 구성 요소에서 재정의하지 않은 경우에만 사용됩니다.\",\n\t\t\"더 뚜렷이 대비되도록 요소를 다른 요소와 구분하는 요소 주위의 추가 테두리입니다.\",\n\t\t\"더 뚜렷이 대비되도록 요소를 다른 요소와 구분하는 활성 요소 주위의 추가 테두리입니다.\",\n\t\t\"워크벤치의 텍스트 선택(예: 입력 필드 또는 텍스트 영역) 전경색입니다. 편집기 내의 선택에는 적용되지 않습니다.\",\n\t\t\"텍스트 구분자 색상입니다.\",\n\t\t\"텍스트 내 링크의 전경색입니다.\",\n\t\t\"클릭하고 마우스가 올라간 상태의 텍스트 내 링크의 전경색입니다.\",\n\t\t\"미리 서식이 지정된 텍스트 세그먼트의 전경색입니다.\",\n\t\t\"미리 서식이 지정된 텍스트 세그먼트의 배경색입니다.\",\n\t\t\"텍스트 내 블록 인용의 전경색입니다.\",\n\t\t\"텍스트 내 블록 인용의 테두리 색입니다.\",\n\t\t\"텍스트 내 코드 블록의 전경색입니다.\",\n\t\t\"편집기 내에서 찾기/바꾸기 같은 위젯의 그림자 색입니다.\",\n\t\t\"편집기 내에서 찾기/바꾸기와 같은 위젯의 테두리 색입니다.\",\n\t\t\"입력 상자 배경입니다.\",\n\t\t\"입력 상자 전경입니다.\",\n\t\t\"입력 상자 테두리입니다.\",\n\t\t\"입력 필드에서 활성화된 옵션의 테두리 색입니다.\",\n\t\t\"입력 필드에서 활성화된 옵션의 배경색입니다.\",\n\t\t\"입력 필드에 있는 옵션의 배경 가리키기 색입니다.\",\n\t\t\"입력 필드에서 활성화된 옵션의 전경색입니다.\",\n\t\t\"위치 표시자 텍스트에 대한 입력 상자 전경색입니다.\",\n\t\t\"정보 심각도의 입력 유효성 검사 배경색입니다.\",\n\t\t\"정보 심각도의 입력 유효성 검사 전경색입니다.\",\n\t\t\"정보 심각도의 입력 유효성 검사 테두리 색입니다.\",\n\t\t\"경고 심각도의 입력 유효성 검사 배경색입니다.\",\n\t\t\"경고 심각도의 입력 유효성 검사 전경색입니다.\",\n\t\t\"경고 심각도의 입력 유효성 검사 테두리 색입니다.\",\n\t\t\"오류 심각도의 입력 유효성 검사 배경색입니다.\",\n\t\t\"오류 심각도의 입력 유효성 검사 전경색입니다.\",\n\t\t\"오류 심각도의 입력 유효성 검사 테두리 색입니다.\",\n\t\t\"드롭다운 배경입니다.\",\n\t\t\"드롭다운 목록 배경입니다.\",\n\t\t\"드롭다운 전경입니다.\",\n\t\t\"드롭다운 테두리입니다.\",\n\t\t\"단추 기본 전경색입니다.\",\n\t\t\"단추 구분 기호 색입니다.\",\n\t\t\"단추 배경색입니다.\",\n\t\t\"마우스로 가리킬 때 단추 배경색입니다.\",\n\t\t\"버튼 테두리 색입니다.\",\n\t\t\"보조 단추 전경색입니다.\",\n\t\t\"보조 단추 배경색입니다.\",\n\t\t\"마우스로 가리킬 때 보조 단추 배경색입니다.\",\n\t\t\"배지 배경색입니다. 배지는 검색 결과 수와 같은 소량의 정보 레이블입니다.\",\n\t\t\"배지 전경색입니다. 배지는 검색 결과 수와 같은 소량의 정보 레이블입니다.\",\n\t\t\"스크롤되는 보기를 나타내는 스크롤 막대 그림자입니다.\",\n\t\t\"스크롤 막대 슬라이버 배경색입니다.\",\n\t\t\"마우스로 가리킬 때 스크롤 막대 슬라이더 배경색입니다.\",\n\t\t\"클릭된 상태일 때 스크롤 막대 슬라이더 배경색입니다.\",\n\t\t\"장기 작업을 대상으로 표시될 수 있는 진행률 표시줄의 배경색입니다.\",\n\t\t\"편집기에서 오류 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"편집기 내 오류 표시선의 전경색입니다.\",\n\t\t\"설정된 경우 편집기에서 오류를 나타내는 이중 밑줄의 색입니다.\",\n\t\t\"편집기에서 경고 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"편집기 내 경고 표시선의 전경색입니다.\",\n\t\t\"설정된 경우 편집기에서 경고를 나타내는 이중 밑줄의 색입니다.\",\n\t\t\"편집기에서 정보 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"편집기 내 정보 표시선의 전경색입니다.\",\n\t\t\"설정된 경우 편집기에서 정보를 나타내는 이중 밑줄 색입니다.\",\n\t\t\"편집기에서 힌트 표시선의 전경색입니다.\",\n\t\t\"설정된 경우 편집기에서 힌트를 나타내는 이중 밑줄 색입니다.\",\n\t\t\"활성 섀시의 테두리 색입니다.\",\n\t\t\"편집기 배경색입니다.\",\n\t\t\"편집기 기본 전경색입니다.\",\n\t\t\"편집기의 고정 스크롤 배경색\",\n\t\t\"편집기의 가리킨 항목 배경색에 고정 스크롤\",\n\t\t\"찾기/바꾸기 같은 편집기 위젯의 배경색입니다.\",\n\t\t\"찾기/바꾸기와 같은 편집기 위젯의 전경색입니다.\",\n\t\t\"편집기 위젯의 테두리 색입니다. 위젯에 테두리가 있고 위젯이 색상을 무시하지 않을 때만 사용됩니다.\",\n\t\t\"편집기 위젯 크기 조정 막대의 테두리 색입니다. 이 색은 위젯에서 크기 조정 막대를 표시하도록 선택하고 위젯에서 색을 재지정하지 않는 경우에만 사용됩니다.\",\n\t\t\"빠른 선택기 배경색. 빠른 선택기 위젯은 명령 팔레트와 같은 선택기를 위한 컨테이너입니다.\",\n\t\t\"빠른 선택기 전경색. 이 빠른 선택기 위젯은 명령 팔레트와 같은 선택기를 위한 컨테이너입니다.\",\n\t\t\"빠른 선택기 제목 배경색. 이 빠른 선택기 위젯은 명령 팔레트와 같은 선택기를 위한 컨테이너입니다.\",\n\t\t\"그룹화 레이블에 대한 빠른 선택기 색입니다.\",\n\t\t\"그룹화 테두리에 대한 빠른 선택기 색입니다.\",\n\t\t\"키 바인딩 레이블 배경색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.\",\n\t\t\"키 바인딩 레이블 전경색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.\",\n\t\t\"키 바인딩 레이블 테두리 색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.\",\n\t\t\"키 바인딩 레이블 테두리 아래쪽 색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.\",\n\t\t\"편집기 선택 영역의 색입니다.\",\n\t\t\"고대비를 위한 선택 텍스트의 색입니다.\",\n\t\t\"비활성 편집기의 선택 항목 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"선택 영역과 동일한 콘텐츠가 있는 영역의 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"선택 영역과 동일한 콘텐츠가 있는 영역의 테두리 색입니다.\",\n\t\t\"현재 검색 일치 항목의 색입니다.\",\n\t\t\"기타 검색 일치 항목의 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"검색을 제한하는 범위의 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"현재 검색과 일치하는 테두리 색입니다.\",\n\t\t\"다른 검색과 일치하는 테두리 색입니다.\",\n\t\t\"검색을 제한하는 범위의 테두리 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"검색 편집기 쿼리의 색상이 일치합니다.\",\n\t\t\"검색 편집기 쿼리의 테두리 색상이 일치합니다.\",\n\t\t\"검색 뷰렛 완료 메시지의 텍스트 색입니다.\",\n\t\t\"호버가 표시된 단어 아래를 강조 표시합니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"편집기 호버의 배경색.\",\n\t\t\"편집기 호버의 전경색입니다.\",\n\t\t\"편집기 호버의 테두리 색입니다.\",\n\t\t\"편집기 호버 상태 표시줄의 배경색입니다.\",\n\t\t\"활성 링크의 색입니다.\",\n\t\t\"인라인 힌트의 전경색\",\n\t\t\"인라인 힌트의 배경색\",\n\t\t\"형식에 대한 인라인 힌트의 전경색\",\n\t\t\"형식에 대한 인라인 힌트의 배경색\",\n\t\t\"매개 변수에 대한 인라인 힌트의 전경색\",\n\t\t\"매개 변수에 대한 인라인 힌트의 배경색\",\n\t\t\"전구 작업 아이콘에 사용되는 색상입니다.\",\n\t\t\"전구 자동 수정 작업 아이콘에 사용되는 색상입니다.\",\n\t\t\"전구 AI 아이콘에 사용되는 색상입니다.\",\n\t\t\"삽입된 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"제거된 텍스트 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"삽입된 줄의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"제거된 줄의 배경색입니다. 색상은 기본 장식을 숨기지 않도록 불투명하지 않아야 합니다.\",\n\t\t\"줄이 삽입된 여백의 배경색입니다.\",\n\t\t\"줄이 제거된 여백의 배경색입니다.\",\n\t\t\"삽입된 콘텐츠에 대한 차등 개요 눈금자 전경입니다.\",\n\t\t\"제거된 콘텐츠에 대한 차등 개요 눈금자 전경입니다.\",\n\t\t\"삽입된 텍스트의 윤곽선 색입니다.\",\n\t\t\"제거된 텍스트의 윤곽선 색입니다.\",\n\t\t\"두 텍스트 편집기 사이의 테두리 색입니다.\",\n\t\t\"diff 편집기의 대각선 채우기 색입니다. 대각선 채우기는 diff 나란히 보기에서 사용됩니다.\",\n\t\t\"diff 편집기에서 변경되지 않은 블록의 배경색입니다.\",\n\t\t\"diff 편집기에서 변경되지 않은 블록의 전경색입니다.\",\n\t\t\"diff 편집기에서 변경되지 않은 코드의 배경색입니다.\",\n\t\t\"목록/트리가 활성 상태인 경우 포커스가 있는 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 활성 상태인 경우 포커스가 있는 항목의 목록/트리 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 활성 상태인 경우 포커스가 있는 항목의 목록/트리 윤곽선 색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 활성화되고 선택되었을 때 초점이 맞춰진 항목의 목록/트리 윤곽선 색상입니다. 활성 목록/트리에는 키보드 포커스가 있고 비활성에는 그렇지 않습니다.\",\n\t\t\"목록/트리가 활성 상태인 경우 선택한 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 활성 상태인 경우 선택한 항목의 목록/트리 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 활성 상태인 경우 선택한 항목의 목록/트리 아이콘 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 비활성 상태인 경우 선택한 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 비활성 상태인 경우 선택한 항목의 목록/트리 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 비활성 상태인 경우 선택한 항목의 목록/트리 아이콘 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 비활성 상태인 경우 포커스가 있는 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"목록/트리가 비활성 상태인 경우 포커스가 있는 항목의 목록/트리 윤곽선 색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\n\t\t\"마우스로 항목을 가리킬 때 목록/트리 배경입니다.\",\n\t\t\"마우스로 항목을 가리킬 때 목록/트리 전경입니다.\",\n\t\t\"마우스로 항목을 이동할 때 목록/트리 끌어서 놓기 배경입니다.\",\n\t\t\"목록/트리 내에서 검색할 때 일치 항목 강조 표시의 목록/트리 전경색입니다.\",\n\t\t\"목록/트리 내에서 검색할 때 일치 항목의 목록/트리 전경색이 능동적으로 포커스가 있는 항목을 강조 표시합니다.\",\n\t\t\"잘못된 항목에 대한 목록/트리 전경 색(예: 탐색기의 확인할 수 없는 루트).\",\n\t\t\"오류를 포함하는 목록 항목의 전경색입니다.\",\n\t\t\"경고를 포함하는 목록 항목의 전경색입니다.\",\n\t\t\"목록 및 트리에서 형식 필터 위젯의 배경색입니다.\",\n\t\t\"목록 및 트리에서 형식 필터 위젯의 윤곽선 색입니다.\",\n\t\t\"일치하는 항목이 없을 때 목록 및 트리에서 표시되는 형식 필터 위젯의 윤곽선 색입니다.\",\n\t\t\"목록 및 트리에서 유형 필터 위젯의 그림자 색상입니다.\",\n\t\t\"필터링된 일치 항목의 배경색입니다.\",\n\t\t\"필터링된 일치 항목의 테두리 색입니다.\",\n\t\t\"들여쓰기 가이드의 트리 스트로크 색입니다.\",\n\t\t\"활성 상태가 아닌 들여쓰기 안내선의 트리 스트로크 색입니다.\",\n\t\t\"열 사이의 표 테두리 색입니다.\",\n\t\t\"홀수 테이블 행의 배경색입니다.\",\n\t\t\"강조되지 않은 항목의 목록/트리 전경색. \",\n\t\t\"확인란 위젯의 배경색입니다.\",\n\t\t\"확인란 위젯이 포함된 요소가 선택된 경우의 확인란 위젯 배경색입니다.\",\n\t\t\"확인란 위젯의 전경색입니다.\",\n\t\t\"확인란 위젯의 테두리 색입니다.\",\n\t\t\"확인란 위젯이 포함된 요소가 선택된 경우의 확인란 위젯 테두리 색입니다.\",\n\t\t\"대신 quickInputList.focusBackground를 사용하세요.\",\n\t\t\"포커스가 있는 항목의 빠른 선택기 전경색입니다.\",\n\t\t\"포커스가 있는 항목의 빠른 선택기 아이콘 전경색입니다.\",\n\t\t\"포커스가 있는 항목의 빠른 선택기 배경색입니다.\",\n\t\t\"메뉴 테두리 색입니다.\",\n\t\t\"메뉴 항목 전경색입니다.\",\n\t\t\"메뉴 항목 배경색입니다.\",\n\t\t\"메뉴의 선택된 메뉴 항목 전경색입니다.\",\n\t\t\"메뉴의 선택된 메뉴 항목 배경색입니다.\",\n\t\t\"메뉴의 선택된 메뉴 항목 테두리 색입니다.\",\n\t\t\"메뉴에서 구분 기호 메뉴 항목의 색입니다.\",\n\t\t\"마우스를 사용하여 작업 위로 마우스를 가져가는 경우 도구 모음 배경\",\n\t\t\"마우스를 사용하여 작업 위로 마우스를 가져가는 경우 도구 모음 윤곽선\",\n\t\t\"작업 위에 마우스를 놓았을 때 도구 모음 배경\",\n\t\t\"코드 조각 탭 정지의 강조 표시 배경색입니다.\",\n\t\t\"코드 조각 탭 정지의 강조 표시 테두리 색입니다.\",\n\t\t\"코드 조각 마지막 탭 정지의 강조 표시 배경색입니다.\",\n\t\t\"코드 조각 마지막 탭 정지의 강조 표시 배경색입니다.\",\n\t\t\"포커스가 있는 이동 경로 항목의 색입니다.\",\n\t\t\"이동 경로 항목의 배경색입니다.\",\n\t\t\"포커스가 있는 이동 경로 항목의 색입니다.\",\n\t\t\"선택한 이동 경로 항목의 색입니다.\",\n\t\t\"이동 경로 항목 선택기의 배경색입니다.\",\n\t\t\"인라인 병합 충돌의 현재 헤더 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"인라인 병합 충돌의 현재 콘텐츠 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"인라인 병합 충돌의 들어오는 헤더 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"인라인 병합 충돌의 들어오는 콘텐츠 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"인라인 병합 충돌의 공통 상위 헤더 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"인라인 병합 충돌의 공통 상위 콘텐츠 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"인라인 병합 충돌에서 헤더 및 스플리터의 테두리 색입니다.\",\n\t\t\"인라인 병합 충돌에서 현재 개요 눈금 전경색입니다.\",\n\t\t\"인라인 병합 충돌에서 수신 개요 눈금 전경색입니다.\",\n\t\t\"인라인 병합 충돌에서 공통 과거 개요 눈금 전경색입니다.\",\n\t\t\"일치 항목 찾기의 개요 눈금자 표식 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"선택 항목의 개요 눈금자 표식 색이 강조 표시됩니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\n\t\t\"일치하는 항목을 찾기 위한 미니맵 표식 색입니다.\",\n\t\t\"편집기 선택을 반복하기 위한 미니맵 표식 색입니다.\",\n\t\t\"편집기 선택 작업을 위한 미니맵 마커 색입니다.\",\n\t\t\"정보에 대한 미니맵 마커 색상입니다.\",\n\t\t\"경고의 미니맵 마커 색상입니다.\",\n\t\t\"오류에 대한 미니맵 마커 색상입니다.\",\n\t\t\"미니맵 배경색입니다.\",\n\t\t\"미니맵에서 렌더링된 전경 요소의 불투명도입니다. 예를 들어, \\\"#000000c0\\\"은 불투명도 75%로 요소를 렌더링합니다.\",\n\t\t\"미니맵 슬라이더 배경색입니다.\",\n\t\t\"마우스로 가리킬 때 미니맵 슬라이더 배경색입니다.\",\n\t\t\"클릭했을 때 미니맵 슬라이더 배경색입니다.\",\n\t\t\"문제 오류 아이콘에 사용되는 색입니다.\",\n\t\t\"문제 경고 아이콘에 사용되는 색입니다.\",\n\t\t\"문제 정보 아이콘에 사용되는 색입니다.\",\n\t\t\"차트에 사용된 전경색입니다.\",\n\t\t\"차트 가로줄에 사용된 색입니다.\",\n\t\t\"차트 시각화에 사용되는 빨간색입니다.\",\n\t\t\"차트 시각화에 사용되는 파란색입니다.\",\n\t\t\"차트 시각화에 사용되는 노란색입니다.\",\n\t\t\"차트 시각화에 사용되는 주황색입니다.\",\n\t\t\"차트 시각화에 사용되는 녹색입니다.\",\n\t\t\"차트 시각화에 사용되는 자주색입니다.\",\n\t],\n\t\"vs/platform/theme/common/iconRegistry\": [\n\t\t\"사용할 글꼴의 ID입니다. 설정하지 않으면 첫 번째로 정의한 글꼴이 사용됩니다.\",\n\t\t\"아이콘 정의와 연결된 글꼴 문자입니다.\",\n\t\t\"위젯에서 닫기 작업의 아이콘입니다.\",\n\t\t\"이전 편집기 위치로 이동 아이콘입니다.\",\n\t\t\"다음 편집기 위치로 이동 아이콘입니다.\",\n\t],\n\t\"vs/platform/undoRedo/common/undoRedoService\": [\n\t\t\"{0} 파일이 닫히고 디스크에서 수정되었습니다.\",\n\t\t\"{0} 파일은 호환되지 않는 방식으로 수정되었습니다.\",\n\t\t\"모든 파일에서 \\'{0}\\'을(를) 실행 취소할 수 없습니다. {1}\",\n\t\t\"모든 파일에서 \\'{0}\\'을(를) 실행 취소할 수 없습니다. {1}\",\n\t\t\"{1}에 변경 내용이 적용되었으므로 모든 파일에서 \\'{0}\\'을(를) 실행 취소할 수 없습니다.\",\n\t\t\"{1}에서 실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 모든 파일에서 \\'{0}\\'을(를) 실행 취소할 수 없습니다.\",\n\t\t\"그동안 실행 취소 또는 다시 실행 작업이 발생했기 때문에 모든 파일에서 \\'{0}\\'을(를) 실행 취소할 수 없습니다.\",\n\t\t\"모든 파일에서 \\'{0}\\'을(를) 실행 취소하시겠습니까?\",\n\t\t\"파일 {0}개에서 실행 취소(&&U)\",\n\t\t\"이 파일 실행 취소(&&F)\",\n\t\t\"실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 \\'{0}\\'을(를) 실행 취소할 수 없습니다.\",\n\t\t\"\\'{0}\\'을(를) 실행 취소하시겠습니까?\",\n\t\t\"예(&&Y)\",\n\t\t\"아니요\",\n\t\t\"모든 파일에서 \\'{0}\\'을(를) 다시 실행할 수 없습니다. {1}\",\n\t\t\"모든 파일에서 \\'{0}\\'을(를) 다시 실행할 수 없습니다. {1}\",\n\t\t\"{1}에 변경 내용이 적용되었으므로 모든 파일에서 \\'{0}\\'을(를) 다시 실행할 수 없습니다.\",\n\t\t\"{1}에서 실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 모든 파일에서 \\'{0}\\'을(를) 다시 실행할 수 없습니다.\",\n\t\t\"그동안 실행 취소 또는 다시 실행 작업이 발생했기 때문에 모든 파일에서 \\'{0}\\'을(를) 다시 실행할 수 없습니다.\",\n\t\t\"실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 \\'{0}\\'을(를) 다시 실행할 수 없습니다.\",\n\t],\n\t\"vs/platform/workspace/common/workspace\": [\n\t\t\"코드 작업 영역\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA<PERSON>,OAAO,+BAAgC,CACtC,+CAAgD,CAC/C,UACD,EACA,yCAA0C,CACzC,cACD,EACA,gDAAiD,CAChD,yCACA,kCACA,iCACD,EACA,4CAA6C,CAC5C,eACA,wCACD,EACA,uCAAwC,CACvC,sIACA,8QACD,EACA,8CAA+C,CAC9C,wBACD,EACA,uCAAwC,CACvC,oBACA,oBACA,oBACA,oDACA,4BACA,uCACD,EACA,qDAAsD,CACrD,kCACD,EACA,+CAAgD,CAC/C,kBACD,EACA,qCAAsC,CACrC,8BACD,EACA,uCAAwC,CACvC,eACA,yCACA,wCACA,wCACA,wCACA,eACA,2DACD,EACA,yBAA0B,CACzB,6BACD,EACA,8BAA+B,CAC9B,WACA,mFACA,+KACA,+KAC<PERSON>,2CACA,8KACD,EACA,kCAAmC,CAClC,OACA,QACA,QACA,UACA,OACA,QACA,QACA,eACA,OACA,QACA,eACA,eACA,OACA,QACA,QACA,UACA,OACA,QACA,QACA,cACD,EACA,0BAA2B,CAC1B,GACD,EACA,+CAAgD,CAC/C,qBACA,kGACA,iKACA,mgBACA,2VACD,EACA,iCAAkC,CACjC,yGACA,yGACA,oDACD,EACA,qCAAsC,CACrC,iCACA,4BACA,iCACA,4BACA,iCACA,2BACD,EACA,4CAA6C,CAC5C,+aACA,2DACD,EACA,2DAA4D,CAC3D,mGACA,mGACA,6FACA,eACA,4MACA,yCACA,oCACA,sCACA,yFACA,4BACA,uDACA,4DACA,sCACA,+BACD,EACA,6CAA8C,CAC7C,+HACA,4IACA,iIACD,EACA,kDAAmD,CAClD,yHACA,wHACD,EACA,8DAA+D,CAC9D,+EACA,yEACA,4HACA,kGACA,4DACA,0BACA,4BACA,yCACA,+EACA,+EACA,0DACA,kEACA,uEACA,iEACD,EACA,4DAA6D,CAC5D,wEACA,oDACD,EACA,wDAAyD,CACxD,6GACD,EACA,kEAAmE,CAClE,kEACA,gIACA,kEACA,sIACA,sCACA,2DACD,EACA,kEAAmE,CAClE,yCACA,yCACA,yCACA,yCACA,8CACA,8CACA,2DACD,EACA,uDAAwD,CACvD,2GACA,iHACA,8DACA,mEACD,EACA,wDAAyD,CACxD,kFACD,EACA,oDAAqD,CACpD,qBACA,qPACA,6XACA,iQACA,8NACA,kGACA,qPACA,mEACA,iGACA,wIACA,wGACA,0UACA,gJACA,mKACA,0LACA,wKACA,qOACA,uKACA,2MACA,sMACA,kXACA,mJACA,qHACA,qHACA,yOACA,qHACA,qHACA,+OACA,oLACA,2LACA,0JACA,6MACA,oOACA,kNACA,kOACA,yIACA,4DACA,qFACA,0FACA,iGACA,2FACA,2KACA,wIACA,uKACA,iMACA,qKACA,iRACD,EACA,wCAAyC,CACxC,uLACA,6IACA,oJACA,kMACA,+IACA,6NACA,6HACA,+KACA,8IACA,0NACA,uIACA,kLACA,+IACA,qHACA,0LACA,gKACA,2KACA,8WACA,uQACA,8VACA,8PACA,0YACA,iUACA,0QACA,2XACA,gFACA,oJACA,oNACA,wEACA,mHACA,4MACA,4TACA,2KACA,wLACA,6JACA,iLACA,2KACA,6KACA,0LACA,mLACA,mLACA,6KACA,+EACA,2JACA,wMACA,mQACA,wMACA,+aACA,iWACA,+RACA,6IACA,iFACA,4MACA,yMACA,iMACA,6KACA,mGACA,icACA,+NACA,sIACA,+EACA,mLACA,+LACA,+EACA,8WACA,yNACA,sKACA;AAAA;AAAA;AAAA,+HACA,qFACA,0HACA,uKACA,uOACA,8OACA,8EACA,uGACA,gIACA,0GACA,uHACA,uKACA,wKACA,6KACA,+NACA,mPACA,+GACA,2FACA,2FACA,wGACA,kGACA,kIACA,kvBACA,qFACA,kGACA,gJACA,wHACA,4FACA,+IACA,+EACA,yYACA,iIACA,kGACA,4FACA,qHACA,iIACA,kGACA,4FACA,qHACA,qFACA,qFACA,kNACA,0LACA,wVACA,6MACA,mTACA,sMACA,4MACA,2HACA,mLACA,4KACA,8KACA,0LACA,qHACA,wIACA,yZACA,gQACA,qLACA,oHACA,0LACA,iGACA,qHACA,gMACA,6KACA,8GACA,kIACA,oLACA,wJACA,8GACA,mNACA,2HACA,yLACA,0JACA,6IACA,8QACA,oLACA,uKACA,mRACA,8IACA,8IACA,0JACA,wIACA,4eACA,iKACA,0HACA,wIACA,6IACA,wRACA,uNACA,iRACA,qJACA,+IACA,qJACA,wKACA,ikBACA,+IACA,+IACA,qJACA,+IACA,iKACA,+IACA,+IACA,qJACA,qJACA,+IACA,yIACA,+IACA,qJACA,6IACA,qJACA,qJACA,yIACA,+IACA,+IACA,yKACA,+IACA,uJACA,4JACA,uIACA,gJACA,+IACA,mKACA,iJACA,qHACA,8HACA,8HACA,kGACA,4RACA,oVACA,2JACA,qNACA,uKACA,yVACA,mLACA,uOACA,kYACA,gJACA,6VACA,mfACA,wCACA,yLACA,6KACA,6KACA,sOACA,uKACA,uKACA,sOACA,gMACA,gOACA,oNACA,oLACA,6KACA,6KACA,kPACA,sIACA,oHACA,gMACA,mSACA,qaACA,uUACA,0LACA,iGACA,iGACA,uOACA,oRACA,yIACA,uFACA,0MACA,gMACA,0LACA,4KACA,wIACA,sKACA,kMACA,gKACA,0JACA,uGACA,iIACA,8PACA,0HACA,6IACA,wEACA,yWACA,wJACA,8EACA,4HACA,kLACA,uMACA,6GACA,4HACA,iGACA,4KACA,yGACA,4KACA,wQACA,wGACA,2HACA,0JACA,kLACA,gTACA,yKACA,wEACA,ubACA,0LACA,gSACA,gKACA,gFACA,kVACA,0LACA,iGACA,gKACA,+MACA,qHACA,wGACA,yGACA,2cACA,yGACA,wGACA,uMACA,oKACA,kGACA,+GACA,yKACA,wKACA,oJACA,6EACA,4FACA,mPACA,kMACA,wKACA,6KACA,+HACA,sIACA,2JACA,4HACA,iKACA,+PACA,2JACA,gIACA,qFACA,6IACA,8IACA,4MACA,gKACA,uVACA,8GACA,yNACA,qFACA,wIACA,2JACA,uIACA,iIACA,8GACA,yGACA,+GACA,+GACA,kGACA,kMACA,iLACA,yRACA,2KACA,mNACA,0LACA,sFACA,4RACA,0MACA,wKACA,sJACA,wGACA,kQACA,2FACA,0HACA,iGACA,0HACA,2JACA,0GACA,sFACA,uTACA,+LACA,mMACA,4DACA,qFACA,+EACA,kIACA,yEACA,iLACA,8MACA,qMACD,EACA,4CAA6C,CAC5C,4HACA,qHACA,yUACA,0HACA,iXACA,2HACA,4DACA,8OACA,+EACA,mEACA,2FACA,iMACA,wGACA,6MACA,4EACA,4EACA,4EACA,4EACA,4EACA,4EACA,yFACA,yFACA,yFACA,yFACA,yFACA,yFACA,+EACA,qJACA,+EACA,4JACA,wEACA,2FACA,wEACA,8EACA,+EACA,iGACA,2LACA,kKACA,gkBACA,0HACA,mHACA,mHACA,qRACA,4FACA,4FACA,4FACA,oMACA,oMACA,oMACA,oMACA,oMACA,oMACA,iGACA,gPACA,gPACA,gPACA,gPACA,gPACA,gPACA,0OACA,0OACA,0OACA,0OACA,0OACA,0OACA,iKACA,yJACD,EACA,qCAAsC,CACrC,8IACA,gNACA,kMACA,8EACA,kFACA,qGACA,+FACA,yHACA,gGACA,uIACA,oFACA,2IACA,yHACA,uGACA,qHACA,mJACA,oFACA,6GACA,8EACA,8EACA,8GACA,oHACA,wJACA,2DACA,8GACA,8GACA,6FACA,iGACA,iGACA,iGACA,8GACA,iGACA,2HACA,8GACA,iGACA,oHACA,gIACA,oHACA,8GACA,2HACA,wIACA,oJACD,EACA,6BAA8B,CAC7B,eACA,eACA,qBACA,eACA,qBACA,qBACA,kCACA,qBACA,eACA,eACA,eACA,iCACA,SACA,qBACA,eACA,uCACA,OACA,eACA,eACA,qBACA,qBACA,eACA,qBACA,qBACA,yCACA,eACA,UACD,EACA,2CAA4C,CAC3C,iCACD,EACA,mCAAoC,CACnC,iCACD,EACA,qCAAsC,CACrC,gDACA,sCACA,+EACA,kCACA,gDACA,qCACA,wDACA,wCACA,4FACA,qDACA,qFACD,EACA,+CAAgD,CAC/C,uCACA,WACD,EACA,sDAAuD,CACtD,yCACA,iDACA,sDACA,kEACA,2DACA,qDACD,EACA,4DAA6D,CAC5D,mJACA,wCACA,wCACA,kCACA,6CACA,yJACD,EACA,4DAA6D,CAC5D,oFACA,yFACD,EACA,sDAAuD,CACtD,iCACD,EACA,gDAAiD,CAChD,gCACA,2BACA,2BACA,2BACA,oBACA,eACA,eACA,eACA,wCACA,wCACA,eACA,eACA,eACA,gCACA,2BACA,2BACA,2BACA,uEACD,EACA,kDAAmD,CAClD,qJACD,EACA,0DAA2D,CAC1D,qFACA,oHACA,sHACA,8JACA,kGACA,8IACA,+BACA,4FACA,+GACA,kGACA,6FACA,4FACA,8BACA,8EACA,iEACA,kGACA,2FACA,+BACA,+GACA,kGACA,6FACA,4FACA,wCACA,qHACA,4BACA,mEACA,+BACA,+EACD,EACA,+DAAgE,CAC/D,2JACA,uPACD,EACA,4DAA6D,CAC5D,sFACA,wEACA,0DACD,EACA,sDAAuD,CACtD,+BACA,4BACA,eACA,qBACA,qBACA,eACA,kCACA,2BACD,EACA,uDAAwD,CACvD,6HACA,8CACA,yCACA,oDACA,+CACA,oCACD,EACA,wDAAyD,CACxD,6FACA,2BACD,EACA,0DAA2D,CAC1D,wGACA,iEACD,EACA,qEAAsE,CACrE,yGACA,8GACA,+CACA,qFACD,EACA,4CAA6C,CAC5C,gDACA,qDACA,mCACA,mCACA,sDACA,0DACD,EACA,oDAAqD,CACpD,qBACA,kCACA,4BACA,eACA,qBACA,eACA,2BACA,kCACA,eACA,8EACD,EACA,kDAAmD,CAClD,yCACA,wCACD,EACA,kEAAmE,CAClE,oEACA,oNACD,EACA,gEAAiE,CAChE,0FACA,wDACA,sKACA,qDACA,mFACD,EACA,6DAA8D,CAC7D,4BACA,+CACA,mBACA,mBACA,4BACA,4BACA,yCACA,wCACD,EACA,uEAAwE,CACvE,2KACD,EACA,qEAAsE,CACrE,8EACA,4CACA,yJACD,EACA,+DAAgE,CAC/D,oKACD,EACA,gDAAiD,CAChD,sJACA,eACA,oBACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA,kCACA,qDACA,4BACA,4BACA,wDACA,qJACA,2JACA,yFACA,yFACA,yCACA,yCACA,qBACA,yBACD,EACA,4CAA6C,CAC5C,4JACA,yJACA,yJACA,4HACA,yIACA,mIACA,mIACA,kCACA,eACA,eACA,yCACA,yCACA,qDACA,eACA,qBACA,qBACA,qBACA,kCACA,+CACA,8NACA,gBACA,4BACA,yBACA,0DACA,0EACA,0DACA,wVACD,EACA,4CAA6C,CAC5C,qBACA,oDACA,eACA,4BACA,8CACA,kEACA,yCACA,+CACA,kGACA,kGACA,4BACA,kCACA,qDACA,4DACA,4DACA,qFACA,sDACA,+BACD,EACA,uDAAwD,CACvD,kOACA,kGACA,oJACA,oJACA,0LACA,yLACD,EACA,8CAA+C,CAC9C,+CACA,+CACA,qFACD,EACA,iDAAkD,CACjD,4BACA,wCACD,EACA,gDAAiD,CAChD,0FACA,2FACA,0FACA,2FACA,6GACA,iCACA,6GACA,gCACD,EACA,sDAAuD,CACtD,eACA,eACA,eACA,eACA,oCACA,0CACA,0CACA,mGACA,oGACA,mGACA,oGACA,mGACA,oGACA,2FACD,EACA,oDAAqD,CACpD,eACA,eACA,4FACA,sDACA,kCACA,uCACA,qDACA,4BACA,eACA,+EACA,sDACA,wCACA,6CACA,+EACA,sDACA,yCACA,4BACA,yGACA,gFACA,+CACA,oDACA,sDACA,eACA,4FACA,mEACA,wCACA,6CACA,4BACA,wEACA,+CACA,kCACA,uCACA,eACA,yCACA,eACA,qDACA,eACA,wEACA,cACD,EACA,qEAAsE,CACrE,6FACD,EACA,iEAAkE,CACjE,sKACA,yBACA,UACD,EACA,2DAA4D,CAC3D,yBACA,yBACA,cACD,EACA,6DAA8D,CAC7D,yEACA,4BACA,cACD,EACA,uDAAwD,CACvD,2DACA,wDACA,gEACA,kEACA,4BACA,6EACA,+EACA,iGACD,EACA,wDAAyD,CACxD,8IACA,kEACA,4BACD,EACA,wCAAyC,CACxC,wEACA,iIACA,uKACA,sMACA,sFACA,2DACA,iEACA,uEACA,6EACA,2DACA,iEACA,2DACA,gEACD,EACA,2DAA4D,CAC3D,yBACA,wQACA,2QACD,EACA,yDAA0D,CACzD,4BACA,yEACA,qEACA,yEACA,8BACD,EACA,0DAA2D,CAC1D,qDACA,oDACD,EACA,oDAAqD,CACpD,uEACA,iEACA,yCACA,mCACA,mCACA,mEACA,2DACA,iEACA,gDACA,uEACA,+CACA,iEACD,EACA,uDAAwD,CACvD,4DACA,mBACA,oBACA,sBACA,mBACA,8LACA,uCACA,2BACD,EACA,uDAAwD,CACvD,4DACA,4DACA,qDACA,+EACA,4BACA,yEACA,sBACA,+CACA,eACA,qDACA,qDACD,EACA,+DAAgE,CAC/D,eACD,EACA,0EAA2E,CAC1E,4DACA,6GACA,gMACA,qFACD,EACA,0EAA2E,CAC1E,4EACD,EACA,2EAA4E,CAC3E,+GACA,+GACA,WACA,eACA,cACD,EACA,wDAAyD,CACxD,+CACD,EACA,4DAA6D,CAC5D,mCACA,wCACA,yCACA,8CACA,+CACA,oDACA,mCACA,wCACA,yCACA,8CACA,qDACA,iEACA,yCACA,+CACA,sBACA,kCACA,kCACA,mCACA,yCACA,yCACA,4DACA,sBACA,4DACA,wCACA,wCACA,qFACA,uEACA,gCACA,uCACD,EACA,wDAAyD,CACxD,+CACA,+JACD,EACA,wCAAyC,CACxC,iJACA,qHACA,4BACA,kCACA,mBACA,oBACA,sBACA,mBACA,gCACA,2BACD,EACA,sDAAuD,CACtD,qIACD,EACA,oDAAqD,CACpD,6CACA,6CACA,yCACA,8CACA,+CACA,oDACA,gDACA,qDACA,sDACA,gDACA,mGACA,8CACA,mGACA,8CACA,sHACA,4HACA,sFACA,8CACA,yCACA,+CACA,qFACA,+CACA,oFACD,EACA,0DAA2D,CAC1D,2DACD,EACA,gEAAiE,CAChE,+GACA,+GACA,oBACA,iIACD,EACA,8CAA+C,CAC9C,uIACA,eACA,oFACA,qDACA,kEACA,qFACA,oFACA,0HACA,0HACA,gIACA,gIACA,6EACA,0FACA,qHACA,4HACA,qHACA,gIACD,EACA,4DAA6D,CAC5D,qHACA,mFACA,mDACA,oJACA,2HACD,EACA,8DAA+D,CAC9D,8KACA,iKACA,wEACA,+CACA,wCACA,kCACA,oBACA,oBACA,0BACA,oBACA,0BACA,oBACA,0BACA,0BACA,0BACA,0BACA,sCACA,4CACA,0BACA,8CACA,oBACA,oBACA,0BACA,uCACA,0BACA,oBACA,oBACA,oBACA,oBACA,oBACA,cACA,oBACA,mBACD,EACA,yDAA0D,CACzD,+GACA,oHACD,EACA,0CAA2C,CAC1C,+CACA,gJACA,qFACA,gDACA,qHACA,8IACA,8IACA,+CACA,iLACD,EACA,oDAAqD,CACpD,8GACA,wLACA,oEACD,EACA,oDAAqD,CACpD,yCACA,8CACA,yCACA,6CACD,EACA,uDAAwD,CACvD,wGACA,6HACA,6HACA,oEACD,EACA,qDAAsD,CACrD,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WACA,WACA,WACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WACA,WACA,UACD,EACA,6DAA8D,CAC7D,+CACA,oDACA,kCACA,uCACA,qDACA,0DACA,mEACA,mEACA,wGACA,iCACD,EACA,4CAA6C,CAC5C,8EACA,2FACA,yGACA,sOACA,0GACA,4HACA,oHACA,+JACD,EACA,sDAAuD,CACtD,2FACA,+CACA,eACA,eACA,qBACA,qBACA,eACA,kCACA,sBACA,kEACD,EACA,kDAAmD,CAClD,wEACA,+EACA,wEACA,oHACA,uIACA,oHACA,gHACA,8MACA,qFACA,yBACA,4DACA,eACA,eACA,UACA,WACA,wBACD,EACA,yDAA0D,CACzD,eACA,wBACD,EACA,0DAA2D,CAC1D,oHACA,iCACD,EACA,wDAAyD,CACxD,UACD,EACA,oDAAqD,CACpD,6NACA,6NACA,mOACA,0MACA,6NACA,mOACA,mOACA,gPACA,mOACA,6NACA,6NACA,6NACA,6NACA,+OACA,uNACA,mOACA,mOACA,6NACA,qPACA,qNACA,6NACA,6NACA,mOACA,mOACA,6NACA,6NACA,0OACA,mOACA,6NACA,mOACA,sPACA,6NACA,4NACD,EACA,kEAAmE,CAClE,+EACA,oLACA,8GACD,EACA,sDAAuD,CACtD,wEACD,EACA,kEAAmE,CAClE,0JACA,iMACA,iKACA,8KACA,gMACA,0KACA,sEACA,yFACA,4BACA,gFACA,6FACA,sFACA,mGACA,6DACA,uFACA,0EACA,oGACA,sFACA,0GACA,yCACA,wIACA,yDACA,6IACA,8EACD,EACA,0EAA2E,CAC1E,2DACA,oFACA,4fACA,6EACA,cACD,EACA,iEAAkE,CACjE,wSACA,8SACA,uQACA,sJACA,4JACA,qHACA,+QACA,kUACA,uSACD,EACA,4DAA6D,CAC5D,4DACA,4DACA,2DACD,EACA,0DAA2D,CAC1D,2BACD,EACA,mDAAoD,CACnD,eACA,qBACA,qBACA,eACA,4BACA,oBACD,EACA,8CAA+C,CAC9C,6FACA,qCACA,oDACA,2BACD,EACA,gDAAiD,CAChD,uIACA,mEACA,+CACA,yCACA,yCACA,+CACA,2DACD,EACA,sDAAuD,CACtD,WACA,WACA;AAAA,UACD,EACA,sCAAuC,CACtC,qBACA,wCACD,EACA,yCAA0C,CACzC,0BACD,EACA,gDAAiD,CAChD,yCACA,yCACA,yCACA,kCACA,+CACA,+CACA,uEACA,4DACA,kCACA,4BACA,+CACA,4BACA,qCACA,+BACA,iCACA,iCACA,iCACA,+CACA,+CACA,gDACA,qBACA,eACA,cACD,EACA,yDAA0D,CACzD,4DACA,8FACA,iIACA,qHACA,iIACA,qHACA,mEACA,kVACA,oKACA,qMACD,EACA,mDAAoD,CACnD,6GACD,EACA,2CAA4C,CAC3C,gDACA,oOACA,0DACA,gCACA,+CACA,2HACA,4DACA,8GACA;AAAA,2BACD,EACA,4CAA6C,CAC5C,iEACA,iEACA,mEACA,oFACA,yJACA,+DACA,uGACA,kDACA,oHACD,EACA,wCAAyC,CACxC,sEACA,uFACA,4FACA,kGACA,0RACD,EACA,yDAA0D,CACzD,gEACD,EACA,0DAA2D,CAC1D,8JACA,sIACA,mFACA,kFACD,EACA,uCAAwC,CACvC,2BACA,wGACA,yGACA,+jBACA,0bACA,sRACA,uKACA,8GACA,+JACA,uKACA,gKACA,yGACA,kOACA,qFACA,2JACA,iRACA,+ZACA,4OACA,4RACA,iIACA,yGACA,4FACA,iMACA,wYACA,6IACA,sOACA,sWACD,EACA,qCAAsC,CACrC,eACA,eACA,cACD,EACA,qDAAsD,CACrD,qDACA,kCACA,oDACA,4BACA,kCACA,WACA,yFACD,EACA,iDAAkD,CACjD,UACD,EACA,4CAA6C,CAC5C,eACA,yKACA,YACA,sGACD,EACA,sDAAuD,CACtD,4DACA,yBACA,yBACA,eACA,kCACA,oBACA,cACD,EACA,gDAAiD,CAChD,2BACD,EACA,iDAAkD,CACjD,sEACD,EACA,yCAA0C,CACzC,oNACA,gQACA,uQACA,6KACA,iGACA,oRACA,0NACA,uOACA,mSACA,wEACA,qFACA,6KACA,6IACA,6IACA,kGACA,yGACA,kGACA,qJACA,2JACA,4DACA,4DACA,kEACA,iIACA,0HACA,kIACA,0HACA,wIACA,2HACA,2HACA,kIACA,2HACA,2HACA,kIACA,2HACA,2HACA,kIACA,2DACA,wEACA,2DACA,iEACA,kEACA,mEACA,qDACA,wGACA,4DACA,kEACA,kEACA,qHACA,kMACA,kMACA,mJACA,iGACA,oJACA,8IACA,oLACA,sQACA,wGACA,uKACA,sQACA,wGACA,uKACA,sQACA,wGACA,iKACA,6GACA,iKACA,+EACA,2DACA,wEACA,8EACA,oHACA,2HACA,iIACA,uQACA,yZACA,8OACA,qPACA,kQACA,qHACA,qHACA,+OACA,+OACA,sPACA,yQACA,+EACA,wGACA,2PACA,iSACA,2JACA,sFACA,+OACA,oPACA,wGACA,wGACA,uQACA,wGACA,2HACA,+GACA,8QACA,4DACA,8EACA,qFACA,8GACA,4DACA,2DACA,2DACA,2FACA,2FACA,wGACA,wGACA,8GACA,wIACA,oGACA,6OACA,uOACA,iOACA,uOACA,2FACA,2FACA,wIACA,wIACA,2FACA,2FACA,+GACA,6NACA,qIACA,qIACA,qIACA,6aACA,6aACA,obACA,qaACA,0ZACA,0ZACA,6aACA,gaACA,gaACA,mbACA,mbACA,0bACA,kIACA,kIACA,kKACA,mMACA,iSACA,0LACA,oHACA,oHACA,kIACA,yIACA,kOACA,+IACA,iGACA,wGACA,oHACA,iKACA,gFACA,qFACA,0GACA,8EACA,0LACA,8EACA,qFACA,iMACA,oFACA,iIACA,oJACA,iIACA,4DACA,kEACA,kEACA,wGACA,wGACA,+GACA,+GACA,oLACA,0LACA,sHACA,sHACA,6HACA,yIACA,yIACA,+GACA,qFACA,+GACA,4FACA,wGACA,wQACA,8QACA,oRACA,0RACA,qRACA,2RACA,2JACA,wIACA,wIACA,qJACA,+QACA,kSACA,kIACA,wIACA,4HACA,kGACA,qFACA,kGACA,2DACA,+QACA,oFACA,uIACA,oHACA,wGACA,wGACA,wGACA,8EACA,qFACA,uGACA,uGACA,uGACA,uGACA,iGACA,sGACD,EACA,wCAAyC,CACxC,0MACA,wGACA,iGACA,wGACA,uGACD,EACA,8CAA+C,CAC9C,uHACA,oIACA,iIACA,iIACA,kNACA,oRACA,gRACA,2HACA,oEACA,qDACA,8NACA,qFACA,cACA,qBACA,iIACA,iIACA,kNACA,oRACA,gRACA,6NACD,EACA,yCAA0C,CACzC,wCACD,CACD,CAAC", "names": [], "file": "editor.main.nls.ko.js"}