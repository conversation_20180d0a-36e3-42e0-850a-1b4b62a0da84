/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/define("vs/base/common/worker/simpleWorker.nls.de",{"vs/base/common/platform":["_"],"vs/editor/common/languages":["Array","Boolescher Wert","Klasse","Konstante","Konstruktor","Enumeration","Enumerationsmember","<PERSON><PERSON><PERSON><PERSON>","Feld","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON>hl\xFC<PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>pace","NULL","<PERSON>ahl","Obje<PERSON>","Operator","Paket","Eigenschaft","Zeichenfolge","Struktur","Typparameter","Variable","{0} ({1})"]});

//# sourceMappingURL=../../../../../min-maps/vs/base/common/worker/simpleWorker.nls.de.js.map