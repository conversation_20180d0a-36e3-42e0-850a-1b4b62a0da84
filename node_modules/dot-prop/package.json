{"name": "dot-prop", "version": "6.0.1", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": "sindresorhus/dot-prop", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^2.0.0"}, "devDependencies": {"ava": "^2.1.0", "benchmark": "^2.1.4", "tsd": "^0.13.1", "xo": "^0.33.1"}, "xo": {"rules": {"@typescript-eslint/method-signature-style": "off"}}}