{"name": "json-schema-typed", "description": "JSONSchema TypeScript definitions.", "version": "7.0.3", "license": "BSD-2-<PERSON><PERSON>", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["jsonschema", "typescript"], "homepage": "https://github.com/typeslick/json-schema-typed", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/typeslick/json-schema-typed.git"}, "dependencies": {}, "devDependencies": {"@babel/cli": "7.6.4", "@babel/core": "7.6.4", "@babel/runtime-corejs2": "7.6.3", "@loomble/cspell-dictionary": "1.0.0", "@microsoft/api-extractor": "7.3.4", "@pika/pack": "0.5.0", "@pika/plugin-build-node": "0.7.1", "@pika/plugin-build-types": "0.7.1", "@pika/plugin-build-web": "0.7.1", "@pika/plugin-standard-pkg": "0.7.1", "@types/babel__core": "7.1.3", "@types/jest": "24.0.20", "@types/jest-diff": "20.0.1", "@types/node": "12.11.7", "babel-core": "6.26.3", "babel-jest": "24.9.0", "babel-preset-slick": "7.0.4", "core-js": "3.3.4", "cspell": "4.0.31", "deep-sort-object": "1.0.2", "jest": "24.9.0", "jest-serializer-path": "0.1.15", "prettier": "1.18.2", "tsconfig-slick": "3.0.2", "tslint": "5.20.0", "tslint-slick": "5.0.0", "typedoc": "0.15.0", "typedoc-plugin-markdown": "2.2.11", "typescript": "3.6.4", "typescript-tslint-plugin": "0.5.4"}, "esnext": "dist-src/index.js", "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts"}