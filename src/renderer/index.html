<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>InsTex - LaTeX Editor</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        overflow: hidden;
      }
      
      #root {
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      /* Loading styles */
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: #1e1e1e;
        color: #ffffff;
        font-size: 18px;
      }
      
      .loading::after {
        content: '';
        width: 20px;
        height: 20px;
        margin-left: 10px;
        border: 2px solid #ffffff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading">Loading InsTex</div>
    </div>
    <script type="module" src="./main.tsx"></script>
  </body>
</html>
